#include "datetime_picker_dialog.h"
#include "ui_datetime_picker_dialog.h"
#include <QMessageBox>
#include <QCalendarWidget>
#include <QTimeEdit>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>

DateTimePickerDialog::DateTimePickerDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::DateTimePickerDialog)
{
    ui->setupUi(this);
    
    // 设置窗口标题
    setWindowTitle(tr("选择时间"));
    
    // 设置默认的日期时间
    QDateTime currentDateTime = QDateTime::currentDateTime();
    startDateTime = currentDateTime;
    endDateTime = currentDateTime.addSecs(3600); // 默认结束时间为当前时间加1小时
    
    // 设置日期时间编辑器
    ui->startDateTimeEdit->setDateTime(startDateTime);
    ui->endDateTimeEdit->setDateTime(endDateTime);
    
    // 确保显示格式正确设置
    ui->startDateTimeEdit->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->endDateTimeEdit->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    
    // 确保时间部分可编辑
    ui->startDateTimeEdit->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->startDateTimeEdit->setCalendarPopup(true);
    ui->startDateTimeEdit->setTimeSpec(Qt::LocalTime);
    ui->startDateTimeEdit->setCurrentSection(QDateTimeEdit::HourSection);
    
    ui->endDateTimeEdit->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->endDateTimeEdit->setCalendarPopup(true);
    ui->endDateTimeEdit->setTimeSpec(Qt::LocalTime);
    ui->endDateTimeEdit->setCurrentSection(QDateTimeEdit::HourSection);
    
    // 创建并设置时间选择器弹出按钮
    QPushButton *startTimeButton = new QPushButton(tr("选择时间"), this);
    QPushButton *endTimeButton = new QPushButton(tr("选择时间"), this);
    
    // 将按钮添加到布局中
    QGridLayout *grid = qobject_cast<QGridLayout*>(ui->timeRangeFrame->findChild<QGridLayout*>("gridLayout"));
    if (grid) {
        grid->addWidget(startTimeButton, 0, 2);
        grid->addWidget(endTimeButton, 1, 2);
    }
    
    // 创建时间选择器对话框
    startTimeDialog = new QDialog(this);
    startTimeDialog->setWindowTitle(tr("选择开始时间"));
    QVBoxLayout *startLayout = new QVBoxLayout(startTimeDialog);
    
    // 创建时间编辑器
    QTimeEdit *startTimeEdit = new QTimeEdit(startTimeDialog);
    startTimeEdit->setDisplayFormat("HH:mm:ss");
    startTimeEdit->setTime(startDateTime.time());
    startTimeEdit->setWrapping(true); // 允许循环滚动
    startTimeEdit->setButtonSymbols(QAbstractSpinBox::UpDownArrows); // 显示上下箭头
    startTimeEdit->setFixedHeight(40); // 适当增大高度，便于触摸操作
    startTimeEdit->setStyleSheet("QTimeEdit { font-size: 14pt; }"); // 增大字体


    // 设置时间部分选择
    startTimeEdit->setCurrentSection(QDateTimeEdit::HourSection);
    // 注意：QTimeEdit中没有setSingleStep方法
    startTimeEdit->setCurrentSection(QDateTimeEdit::MinuteSection);
    // 注意：QTimeEdit中没有setSingleStep方法
    startTimeEdit->setCurrentSection(QDateTimeEdit::SecondSection);
    // 注意：QTimeEdit中没有setSingleStep方法
    startTimeEdit->setCurrentSection(QDateTimeEdit::HourSection); // 默认从小时部分开始选择
    
    // 添加到布局
    startLayout->addWidget(new QLabel(tr("请选择时间:")), 0);
    startLayout->addWidget(startTimeEdit, 1);
    
    // 添加确定按钮
    QHBoxLayout *startButtonLayout = new QHBoxLayout();
    QPushButton *startOkButton = new QPushButton(tr("确定"), startTimeDialog);
    startButtonLayout->addStretch();
    startButtonLayout->addWidget(startOkButton);
    startLayout->addLayout(startButtonLayout);
    
    // 同样为结束时间创建对话框
    endTimeDialog = new QDialog(this);
    endTimeDialog->setWindowTitle(tr("选择结束时间"));
    QVBoxLayout *endLayout = new QVBoxLayout(endTimeDialog);
    
    QTimeEdit *endTimeEdit = new QTimeEdit(endTimeDialog);
    endTimeEdit->setDisplayFormat("HH:mm:ss");
    endTimeEdit->setTime(endDateTime.time());
    endTimeEdit->setWrapping(true); // 允许循环滚动
    endTimeEdit->setButtonSymbols(QAbstractSpinBox::UpDownArrows); // 显示上下箭头
    endTimeEdit->setFixedHeight(40); // 适当增大高度，便于触摸操作
    endTimeEdit->setStyleSheet("QTimeEdit { font-size: 14pt; }"); // 增大字体
    
    // 设置时间步进间隔
    endTimeEdit->setCurrentSection(QDateTimeEdit::HourSection);
    // 注意：QTimeEdit中没有setSingleStep方法
    endTimeEdit->setCurrentSection(QDateTimeEdit::MinuteSection);
    // 注意：QTimeEdit中没有setSingleStep方法
    endTimeEdit->setCurrentSection(QDateTimeEdit::SecondSection);
    // 注意：QTimeEdit中没有setSingleStep方法
    endTimeEdit->setCurrentSection(QDateTimeEdit::HourSection); // 默认从小时部分开始选择
    
    endLayout->addWidget(new QLabel(tr("请选择时间:")), 0);
    endLayout->addWidget(endTimeEdit, 1);
    
    QHBoxLayout *endButtonLayout = new QHBoxLayout();
    QPushButton *endOkButton = new QPushButton(tr("确定"), endTimeDialog);
    endButtonLayout->addStretch();
    endButtonLayout->addWidget(endOkButton);
    endLayout->addLayout(endButtonLayout);
    
    // 连接信号槽
    connect(startTimeButton, &QPushButton::clicked, [=]() {
        startTimeEdit->setTime(ui->startDateTimeEdit->time());
        startTimeDialog->exec();
    });
    
    connect(startOkButton, &QPushButton::clicked, [=]() {
        QDateTime newDateTime = ui->startDateTimeEdit->dateTime();
        newDateTime.setTime(startTimeEdit->time());
        ui->startDateTimeEdit->setDateTime(newDateTime);
        startTimeDialog->accept();
    });
    
    connect(endTimeButton, &QPushButton::clicked, [=]() {
        endTimeEdit->setTime(ui->endDateTimeEdit->time());
        endTimeDialog->exec();
    });
    
    connect(endOkButton, &QPushButton::clicked, [=]() {
        QDateTime newDateTime = ui->endDateTimeEdit->dateTime();
        newDateTime.setTime(endTimeEdit->time());
        ui->endDateTimeEdit->setDateTime(newDateTime);
        endTimeDialog->accept();
    });
    
    // 日历属性设置
    QCalendarWidget *startCalendar = new QCalendarWidget(this);
    startCalendar->setGridVisible(true);
    startCalendar->setFirstDayOfWeek(Qt::Monday);
    ui->startDateTimeEdit->setCalendarWidget(startCalendar);
    
    QCalendarWidget *endCalendar = new QCalendarWidget(this);
    endCalendar->setGridVisible(true);
    endCalendar->setFirstDayOfWeek(Qt::Monday);
    ui->endDateTimeEdit->setCalendarWidget(endCalendar);
    
    // 连接信号和槽
    connect(ui->startDateTimeEdit, &QDateTimeEdit::dateTimeChanged, this, &DateTimePickerDialog::onStartDateTimeChanged);
    connect(ui->endDateTimeEdit, &QDateTimeEdit::dateTimeChanged, this, &DateTimePickerDialog::onEndDateTimeChanged);
    connect(ui->okButton, &QPushButton::clicked, this, &DateTimePickerDialog::onAcceptButtonClicked);
    connect(ui->cancelButton, &QPushButton::clicked, this, &DateTimePickerDialog::onRejectButtonClicked);
}

DateTimePickerDialog::~DateTimePickerDialog()
{
    delete startTimeDialog;
    delete endTimeDialog;
    delete ui;
}

QDateTime DateTimePickerDialog::getStartDateTime() const
{
    return startDateTime;
}

QDateTime DateTimePickerDialog::getEndDateTime() const
{
    return endDateTime;
}

void DateTimePickerDialog::setStartDateTime(const QDateTime &dateTime)
{
    startDateTime = dateTime;
    ui->startDateTimeEdit->setDateTime(startDateTime);
}

void DateTimePickerDialog::setEndDateTime(const QDateTime &dateTime)
{
    endDateTime = dateTime;
    ui->endDateTimeEdit->setDateTime(endDateTime);
}

void DateTimePickerDialog::setDateTimeRange(const QDateTime &minDateTime, const QDateTime &maxDateTime)
{
    // 设置日期时间范围限制
    ui->startDateTimeEdit->setMinimumDateTime(minDateTime);
    ui->startDateTimeEdit->setMaximumDateTime(maxDateTime);
    ui->endDateTimeEdit->setMinimumDateTime(minDateTime);
    ui->endDateTimeEdit->setMaximumDateTime(maxDateTime);
    
    // 同步更新日历部件的日期范围
    if (ui->startDateTimeEdit->calendarWidget()) {
        ui->startDateTimeEdit->calendarWidget()->setMinimumDate(minDateTime.date());
        ui->startDateTimeEdit->calendarWidget()->setMaximumDate(maxDateTime.date());
    }
    
    if (ui->endDateTimeEdit->calendarWidget()) {
        ui->endDateTimeEdit->calendarWidget()->setMinimumDate(minDateTime.date());
        ui->endDateTimeEdit->calendarWidget()->setMaximumDate(maxDateTime.date());
    }
}

void DateTimePickerDialog::onStartDateTimeChanged(const QDateTime &dateTime)
{
    startDateTime = dateTime;
    
    // 确保开始时间不晚于结束时间
    if (startDateTime > endDateTime) {
        ui->endDateTimeEdit->setDateTime(startDateTime);
    }
}

void DateTimePickerDialog::onEndDateTimeChanged(const QDateTime &dateTime)
{
    endDateTime = dateTime;
    
    // 确保结束时间不早于开始时间
    if (endDateTime < startDateTime) {
        ui->startDateTimeEdit->setDateTime(endDateTime);
    }
}

void DateTimePickerDialog::onAcceptButtonClicked()
{
    // 验证时间选择是否有效
    if (startDateTime > endDateTime) {
        QMessageBox::warning(this, tr("无效的时间范围"), tr("开始时间不能晚于结束时间"));
        return;
    }
    
    // 接受对话框
    accept();
}

void DateTimePickerDialog::onRejectButtonClicked()
{
    // 拒绝对话框
    reject();
} 
