#ifndef RESPONSE_HANDLER_H
#define RESPONSE_HANDLER_H

#include "command_handler.h"
#include <QMap>
#include <QVector>
#include <QString>

/**
 * @brief 响应命令处理器
 */
class ResponseCommandHandler : public CommandHandler
{
    Q_OBJECT

public:
    explicit ResponseCommandHandler(QObject *parent = nullptr);
    ~ResponseCommandHandler() override;
    CommandResult processCommand(const QByteArray &data) override;

signals:
    /**
     * @brief 收到响应信号
     * @param response 响应数据
     */
    void responseReceived(const QByteArray &response);

private:
    // 移除数据存储相关成员，使用外部数据存储类
};

#endif // RESPONSE_HANDLER_H 
