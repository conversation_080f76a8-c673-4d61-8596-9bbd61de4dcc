#ifndef DLL_LOADER_H
#define DLL_LOADER_H

#include <windows.h>
#include <QDebug>
#include <QString>
#include <string>
#include <vector>
#include <sstream>
#include "icdStructDef.h"

class DllParseandanalysis
{
public:
    // 单例模式获取实例
    static DllParseandanalysis& getInstance();
    
    // 禁止拷贝和赋值
    DllParseandanalysis(const DllParseandanalysis&) = delete;
    DllParseandanalysis& operator=(const DllParseandanalysis&) = delete;
    DllParseandanalysis(DllParseandanalysis&&) = delete;
    DllParseandanalysis& operator=(DllParseandanalysis&&) = delete;
    
    ~DllParseandanalysis();

    // 初始化DLL
    bool init(const QString& dllPath);
    
    // 释放DLL
    void release();
    
    // 检查是否已加载
    bool isLoaded() const { return m_hModule != nullptr; }

    // 通用模板方法，用于应用函数到指定参数并返回结果
    template<typename FuncPtr, typename... Args>
    auto callFunction(FuncPtr func, Args&&... args) const -> decltype(func(std::forward<Args>(args)...)) {
        if (!isLoaded() || func == nullptr) {
            throw std::runtime_error("Function not available or DLL not loaded");
        }
        return func(std::forward<Args>(args)...);
    }

    // 特定函数的封装方法
    int add(int a, int b) const
    {
        return callFunction(m_AddFunc, a, b);
    }

    int parseIcdAndGenerateDataFile(const std::string& icdFile, const std::string& outputPath, std::string& version) const
    {
        return callFunction(m_ParseIcdAndGenerateDataFileFunc, icdFile, outputPath, version);
    }

    int loadIcdJson(const std::string& file) const
    {
        return callFunction(m_LoadIcdJsonFunc, file);
    }

    int initDecoder(void*& token) const
    {
        return callFunction(m_InitDecoderFunc, token);
    }

    int destroyDecoder(void*& token) const
    {
        return callFunction(m_DestroyDecoderFunc, token);
    }

    std::string getCurrentIcdVersion(void*& token) const
    {
        return callFunction(m_GetCurrentIcdVersionFunc, token);
    }

    int parseFixedLengthData(void*& token, const UnpackData& data, std::vector<SignalData>& result) const
    {
        return callFunction(m_ParseFixedLengthDataFunc, token, data, result);
    }

    int unpackMuseData(void*& token, const DataBuffer& buffer, UnpackData& data) const
    {
        return callFunction(m_UnpackMuseDataFunc, token, buffer, data);
    }

    void msgpackPackVector(std::stringstream& buffer, std::vector<std::string> dataList) const
    {
        return callFunction(m_MsgpackPackVectorFunc, buffer, dataList);
    }

    // 功能单元与接口相关函数封装
    int getFuncUnitList(void*& token, std::vector<FuncUnit>& funcUnitVec) const
    {
        return callFunction(m_GetFuncUnitListFunc, token, funcUnitVec);
    }

    int getInterfacesByServiceName(void*& token, const std::string& serviceName, std::vector<Interface>& interfaceVec) const
    {
        return callFunction(m_GetInterfacesByServiceNameFunc, token, serviceName, interfaceVec);
    }

    int getInterfacesByServiceShortName(void*& token, const std::string& serviceShortName, std::vector<Interface>& interfaceVec) const
    {
        return callFunction(m_GetInterfacesByServiceShortNameFunc, token, serviceShortName, interfaceVec);
    }

    int getTopicInfoByShortName(void*& token, const std::string& shortName, Topic& topicInfo) const
    {
        return callFunction(m_GetTopicInfoByShortNameFunc, token, shortName, topicInfo);
    }

    int searchInterfacesByNameFuzzyMatch(void*& token, const std::string& interfaceName, std::vector<Interface>& interfaceVec) const
    {
        return callFunction(m_SearchInterfacesByNameFuzzyMatchFunc, token, interfaceName, interfaceVec);
    }

private:
    // 私有构造函数，防止外部创建实例
    DllParseandanalysis();

    // 解析出来的函数指针类型定义 - 与parseAndAnalysis.h中的所有函数对应
    typedef int (*AddFunc)(int a, int b);
    typedef int (*ParseIcdAndGenerateDataFileFunc)(const std::string& icdFile, const std::string& outputPath, std::string& version);
    typedef int (*LoadIcdJsonFunc)(const std::string& file);
    typedef int (*InitDecoderFunc)(void*& token);
    typedef int (*DestroyDecoderFunc)(void*& token);
    typedef std::string (*GetCurrentIcdVersionFunc)(void*& token);
    typedef int (*ParseFixedLengthDataFunc)(void*& token, const UnpackData& data, std::vector<SignalData>& result);
    typedef int (*UnpackMuseDataFunc)(void*& token, const DataBuffer& buffer, UnpackData& data);
    typedef void (*MsgpackPackVectorFunc)(std::stringstream& buffer, std::vector<std::string> dataList);
    typedef int (*GetFuncUnitListFunc)(void*& token, std::vector<FuncUnit>& funcUnitVec);
    typedef int (*GetInterfacesByServiceNameFunc)(void*& token, const std::string& serviceName, std::vector<Interface>& interfaceVec);
    typedef int (*GetInterfacesByServiceShortNameFunc)(void*& token, const std::string& serviceShortName, std::vector<Interface>& interfaceVec);
    typedef int (*GetTopicInfoByShortNameFunc)(void*& token, const std::string& shortName, Topic& topicInfo);
    typedef int (*SearchInterfacesByNameFuzzyMatchFunc)(void*& token, const std::string& interfaceName, std::vector<Interface>& interfaceVec);

    // 函数指针 - 与parseAndAnalysis.h中的所有函数对应
    AddFunc m_AddFunc;
    ParseIcdAndGenerateDataFileFunc m_ParseIcdAndGenerateDataFileFunc;
    LoadIcdJsonFunc m_LoadIcdJsonFunc;
    InitDecoderFunc m_InitDecoderFunc;
    DestroyDecoderFunc m_DestroyDecoderFunc;
    GetCurrentIcdVersionFunc m_GetCurrentIcdVersionFunc;
    ParseFixedLengthDataFunc m_ParseFixedLengthDataFunc;
    UnpackMuseDataFunc m_UnpackMuseDataFunc;
    MsgpackPackVectorFunc m_MsgpackPackVectorFunc;
    GetFuncUnitListFunc m_GetFuncUnitListFunc;
    GetInterfacesByServiceNameFunc m_GetInterfacesByServiceNameFunc;
    GetInterfacesByServiceShortNameFunc m_GetInterfacesByServiceShortNameFunc;
    GetTopicInfoByShortNameFunc m_GetTopicInfoByShortNameFunc;
    SearchInterfacesByNameFuzzyMatchFunc m_SearchInterfacesByNameFuzzyMatchFunc;

    HMODULE m_hModule;

    // 打印DLL导出函数列表
    void printDllExports(HMODULE hModule);

    // 检查DLL是否为MinGW编译的
    bool isMinGWDll(const QString& dllPath);
    
    // 检查DLL架构（32位或64位）
    bool is64BitDll(HMODULE hModule);
    
    // 检查DLL是否有C++名称修饰
    bool hasCppNameMangling(HMODULE hModule);

    // 尝试加载函数，但允许失败
    template<typename T>
    bool tryLoadFunction(T& funcPtr, const char* funcName);
    
    // 加载指定名称的函数，要求必须成功
    template<typename T>
    bool loadFunction(T& funcPtr, const char* funcName) {
        funcPtr = reinterpret_cast<T>(GetProcAddress(m_hModule, funcName));
        if (funcPtr == nullptr) {
            qWarning() << "Failed to load function:" << funcName << ", error:" << GetLastError();
            return false;
        }
        return true;
    }
};

#endif // DLL_LOADER_H 
