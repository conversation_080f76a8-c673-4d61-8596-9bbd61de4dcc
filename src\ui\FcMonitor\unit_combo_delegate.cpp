#include "unit_combo_delegate.h"
#include "unit_standard_item.h"
#include "utils/unit_model.h"
#include <QComboBox>
#include <QPainter>
#include <QApplication>
#include <QDebug>
#include <QStandardItemModel>

UnitComboDelegate::UnitComboDelegate(QObject *parent)
    : QStyledItemDelegate(parent)
{
}

QWidget *UnitComboDelegate::createEditor(QWidget *parent, const QStyleOptionViewItem &option,
                                        const QModelIndex &index) const
{
    Q_UNUSED(option)
    
    // 只在单位列（第3列，索引为2）且支持单位切换时创建下拉框
    if (!isUnitColumn(index)) {
        return QStyledItemDelegate::createEditor(parent, option, index);
    }

    QComboBox *comboBox = new QComboBox(parent);
    comboBox->setFrame(false);
    
    // 获取UnitStandardItem
    QStandardItemModel *model = qobject_cast<QStandardItemModel*>(const_cast<QAbstractItemModel*>(index.model()));
    if (model) {
        QStandardItem *item = model->itemFromIndex(index);
        UnitStandardItem *unitItem = dynamic_cast<UnitStandardItem*>(item);
        
        if (unitItem && unitItem->isHaveCombox()) {
            // 添加可用单位列表到下拉框
            comboBox->addItems(unitItem->unitList());
            
            //设置当前选中项
            QString currentUnitName = unitItem->currentUnitName();
            int currentIndex = comboBox->findText(currentUnitName);
            if (currentIndex >= 0) {
                comboBox->setCurrentIndex(currentIndex);
            }
        }
    }
    
    return comboBox;
}

void UnitComboDelegate::setEditorData(QWidget *editor, const QModelIndex &index) const
{
    if (!isUnitColumn(index)) {
        QStyledItemDelegate::setEditorData(editor, index);
        return;
    }

    QComboBox *comboBox = qobject_cast<QComboBox*>(editor);
    if (!comboBox) {
        return;
    }

    // 获取当前单位名称并设置到下拉框
    QStandardItemModel *model = qobject_cast<QStandardItemModel*>(const_cast<QAbstractItemModel*>(index.model()));
    if (model) {
        QStandardItem *item = model->itemFromIndex(index);
        UnitStandardItem *unitItem = dynamic_cast<UnitStandardItem*>(item);
        
        if (unitItem && unitItem->isHaveCombox()) {
            QString currentUnitName = unitItem->currentUnitName();
            int currentIndex = comboBox->findText(currentUnitName);
            if (currentIndex >= 0) {
                comboBox->setCurrentIndex(currentIndex);
            }
        }
    }
}

void UnitComboDelegate::setModelData(QWidget *editor, QAbstractItemModel *model,
                                    const QModelIndex &index) const
{
    if (!isUnitColumn(index)) {
        QStyledItemDelegate::setModelData(editor, model, index);
        return;
    }

    QComboBox *comboBox = qobject_cast<QComboBox*>(editor);
    if (!comboBox) {
        return;
    }

    QStandardItemModel *standardModel = qobject_cast<QStandardItemModel*>(model);
    if (standardModel) {
        QStandardItem *item = standardModel->itemFromIndex(index);
        UnitStandardItem *unitItem = dynamic_cast<UnitStandardItem*>(item);
        
        if (unitItem && unitItem->isHaveCombox()) {
            QString newUnitName = comboBox->currentText();
            QString oldUnitName = unitItem->currentUnitName();
            unsigned int oldUnitID = unitItem->currentUnitID();
            
            // 获取新单位的ID
            unsigned int newUnitID = UnitModel::Instance()->GetUnitIDFromName(newUnitName);
            
            // 更新UnitStandardItem的当前单位信息
            unitItem->setCurrentUnitName(newUnitName);
            unitItem->setCurrentUnitID(newUnitID);
            unitItem->setText(newUnitName);
            
            // 发射单位切换信号
            emit const_cast<UnitComboDelegate*>(this)->unitChanged(index, oldUnitName, newUnitName, oldUnitID, newUnitID);
            
            qDebug() << "Unit changed from" << oldUnitName << "(" << oldUnitID << ") to" 
                     << newUnitName << "(" << newUnitID << ")";
        }
    }
}

void UnitComboDelegate::updateEditorGeometry(QWidget *editor, const QStyleOptionViewItem &option,
                                            const QModelIndex &index) const
{
    Q_UNUSED(index)
    editor->setGeometry(option.rect);
}

void UnitComboDelegate::paint(QPainter *painter, const QStyleOptionViewItem &option,
                             const QModelIndex &index) const
{
    if (!isUnitColumn(index)) {
        QStyledItemDelegate::paint(painter, option, index);
        return;
    }

    // 检查是否支持单位切换
    QStandardItemModel *model = qobject_cast<QStandardItemModel*>(const_cast<QAbstractItemModel*>(index.model()));
    if (model) {
        QStandardItem *item = model->itemFromIndex(index);
        UnitStandardItem *unitItem = dynamic_cast<UnitStandardItem*>(item);
        
        if (unitItem && unitItem->isHaveCombox()) {
            // 绘制带下拉箭头的样式，表示可以点击切换
            QStyleOptionComboBox comboBoxOption;
            comboBoxOption.rect = option.rect;
            comboBoxOption.state = option.state;
            comboBoxOption.currentText = unitItem->currentUnitName();
            
            QApplication::style()->drawComplexControl(QStyle::CC_ComboBox, &comboBoxOption, painter);
            QApplication::style()->drawControl(QStyle::CE_ComboBoxLabel, &comboBoxOption, painter);
            return;
        }
    }
    
    // 不支持单位切换的项目使用默认绘制
    QStyledItemDelegate::paint(painter, option, index);
}

void UnitComboDelegate::commitAndCloseEditor()
{
    QComboBox *editor = qobject_cast<QComboBox*>(sender());
    if (editor) {
        emit commitData(editor);
        emit closeEditor(editor);
    }
}

bool UnitComboDelegate::isUnitColumn(const QModelIndex &index) const
{
    // 单位列是第3列（索引为2）
    return index.column() == 2;
}

