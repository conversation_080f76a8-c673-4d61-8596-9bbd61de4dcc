#include "fc_data_parser.h"
#include <QDebug>
#include <QXmlStreamReader>
#include <QDateTime>
#include <QDataStream>
#include <QSet>
#include "utils/file_list_service.h"
#include "third/parseAndAnalysis.h"

FcDataParser::FcDataParser(QObject *parent)
    : QObject(parent)
    , m_currentParseProgress(0)
{
    // 初始化已知的消息类型和数据类型
    m_knownMessageTypes["heart_report"] = "心跳报告";
    m_knownMessageTypes["status_update"] = "状态更新";
    m_knownMessageTypes["data_sync"] = "数据同步";
    
    m_knownDataTypes["DATA_BLOCK"] = "数据块";
    m_knownDataTypes["STRING"] = "字符串";
    m_knownDataTypes["INTEGER"] = "整数";
    m_knownDataTypes["FLOAT"] = "浮点数";
}

FcDataParser::~FcDataParser()
{
}

bool FcDataParser::parseSchemeXml(const QString& xmlPath, QVector<FcSchemeData>& schemes)
{
    m_currentContext = "解析方案XML";
    m_currentParseProgress = 0;

    QFile file(xmlPath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {

        QString errstr = file.errorString();
        qWarning() << "无法打开XML文件:" << xmlPath << ", 错误:" << errstr;
        return false;
    }

    QXmlStreamReader xml(&file);
    schemes.clear();

    std::string icdVersion;
    void *icdtoken = FileListService::getInstance().getParseandanalysisP();
    icdVersion = getCurrentIcdVersion(icdtoken);

    FcSchemeData currentScheme;
    FcUnitMessageData currentMsg;
    FcTopicData currentTopic;
    bool inSolution = false;
    bool inMsg = false;
    bool inTopic = false;

    // 开始解析XML
    while (!xml.atEnd() && !xml.hasError()) {
        QXmlStreamReader::TokenType token = xml.readNext();

        if (token == QXmlStreamReader::StartElement) {
            QString elementName = xml.name().toString();

            // 解析solution元素
            if (elementName == "solution") {
                // 如果之前有未完成的方案，先保存
                if (inSolution) {
                    currentScheme.buildIndices();                   
                    schemes.append(currentScheme);
                    qDebug() << "保存方案:" << currentScheme.schemeName;
                }

                // 开始新方案
                currentScheme = FcSchemeData();
                QXmlStreamAttributes attrs = xml.attributes();

                currentScheme.schemeName = attrs.value("name").toString();
                currentScheme.icdName = attrs.value("ICD_name").toString();
                currentScheme.isShared = (attrs.value("isShared").toString() == "1");

                inSolution = true;
                qDebug() << "开始解析方案:" << currentScheme.schemeName << "ICD:" << currentScheme.icdName;
            }
            // 解析Msg元素
            else if (elementName == "Msg" && inSolution) {
                // 如果之前有未完成的消息，先保存
                if (inMsg) {
                    currentScheme.unitMessages.append(currentMsg);
                    qDebug() << "保存消息:" << currentMsg.messageName;
                }

                // 开始新消息
                currentMsg = FcUnitMessageData();
                QXmlStreamAttributes attrs = xml.attributes();

                currentMsg.messageName = attrs.value("msgName").toString();
                currentMsg.sourceFuncId = attrs.value("srcUnitId").toInt();
                currentMsg.pubSubTopicId = attrs.value("subPubTopId").toInt();

                //从解析库获取该消息完整信息
                void *m_decoderToken = FileListService::getInstance().getParseandanalysisP();
                Message message;
                int ret = getMessageInfoByMessageName(m_decoderToken, currentMsg.messageName.toStdString(), message);

                if (ret == 0) {
                    // 成功获取消息信息，更新currentMsg
                    qDebug() << "成功获取消息信息:" << currentMsg.messageName;

                    // 更新FcUnitMessageData (currentMsg)
                    currentMsg.messageName = QString::fromStdString(message.messageInfo.msgName);
                    currentMsg.sourceFuncId = message.messageInfo.sourceFuncId;
                    currentMsg.pubSubTopicId = message.messageInfo.pubSubTopicId;
                    currentMsg.messageTopicName = QString::fromStdString(message.messageInfo.msgTopicName);
                    currentMsg.messageTopicIdentifier = QString::fromStdString(message.messageInfo.msgTopicShortName);

                    // 转换目的功能ID列表
                    currentMsg.destFuncIds.clear();
                    for (int destId : message.messageInfo.destFuncVec) {
                        currentMsg.destFuncIds.append(QString::number(destId));
                    }

                    // 更新主题信息到currentMsg.topicData
                    currentMsg.topicData.topicId = QString::number(message.messageTopic.id);
                    currentMsg.topicData.topicName = QString::fromStdString(message.messageTopic.name);
                    currentMsg.topicData.topicIdentifier = QString::fromStdString(message.messageTopic.shortName);
                    currentMsg.topicData.topicType = QString::fromStdString(message.messageTopic.topicType);
                    currentMsg.topicData.formatType = QString::fromStdString(message.messageTopic.formatType);
                    currentMsg.topicData.dataLength = QString::number(message.messageTopic.dataLength);
                    currentMsg.topicData.lengthType = QString::fromStdString(message.messageTopic.lengthType);

                    // 清空并重新填充信号列表
                    currentMsg.topicData.signalList.clear();
                    for (const auto& topicSignalPtr : message.messageTopic.signalVec) {
                        if (topicSignalPtr) {
                            FcSignalData fcSignal;
                            fcSignal.signalId = QString::fromStdString(topicSignalPtr->id);
                            fcSignal.signalName = QString::fromStdString(topicSignalPtr->name);
                            fcSignal.signalIdentifier = QString::fromStdString(topicSignalPtr->shortName);
                            fcSignal.startWord = QString::number(topicSignalPtr->startByte);
                            fcSignal.startBit = QString::number(topicSignalPtr->startBit);
                            fcSignal.lsb = QString::fromStdString(topicSignalPtr->lsb);
                            fcSignal.msb = QString::fromStdString(topicSignalPtr->msb);
                            fcSignal.unitCode = QString::fromStdString(topicSignalPtr->signalUnit);
                            fcSignal.maxValue = topicSignalPtr->maxValue;
                            fcSignal.minValue = topicSignalPtr->minValue;
                            fcSignal.signalType = QString::fromStdString(topicSignalPtr->sigType);
                            fcSignal.signalSize = topicSignalPtr->signalSize;

                            // 默认设置为非活跃，后续在XML解析中根据XML中是否存在来设置为活跃
                            fcSignal.isActive = false;

                            currentMsg.topicData.signalList.append(fcSignal);
                            qDebug() << "添加信号到主题:" << fcSignal.signalName << "ID:" << fcSignal.signalId << "(默认非活跃)";
                        }
                    }

                    // 构建信号索引
                    currentMsg.topicData.buildSignalIndex();



                    qDebug() << "消息信息更新完成，主题:" << currentMsg.topicData.topicName
                             << "信号数量:" << currentMsg.topicData.signalList.size();
                } else {
                    qWarning() << "获取消息信息失败，消息名:" << currentMsg.messageName << "错误码:" << ret;
                }

                inMsg = true;
                qDebug() << "开始解析消息:" << currentMsg.messageName;
            }
            // 解析topic元素
            else if (elementName == "topic" && inMsg) {
                // 如果之前有未完成的主题，先保存
                if (inTopic) {
                    currentTopic.buildSignalIndex();
                    qDebug() << "保存主题:" << currentTopic.topicName;
                }

                // 检查是否已经从解析库获取了主题信息
                if (!currentMsg.topicData.topicName.isEmpty()) {
                    // 已经有主题信息，使用解析库获取的主题数据，但仍需要解析XML来设置信号的isActive状态
                    QXmlStreamAttributes attrs = xml.attributes();
                    QString xmlTopicName = attrs.value("name").toString();
                    QString xmlTopicShortName = attrs.value("shortName").toString();

                    // 使用已有的主题数据
                    currentTopic = currentMsg.topicData;
                    qDebug() << "使用解析库获取的主题，继续解析XML以设置信号活跃状态:" << xmlTopicName;

                    inTopic = true;
                } else {
                    // 开始新主题（仅在没有从解析库获取信息时）
                    currentTopic = FcTopicData();
                    QXmlStreamAttributes attrs = xml.attributes();

                    currentTopic.topicName = attrs.value("name").toString();
                    currentTopic.topicIdentifier = attrs.value("shortName").toString();

                    inTopic = true;
                    qDebug() << "开始解析主题:" << currentTopic.topicName;
                }
            }
            // 解析Signal元素
            else if (elementName == "Signal" && inTopic) {
                QXmlStreamAttributes attrs = xml.attributes();
                if (attrs.hasAttribute("name") && attrs.hasAttribute("id")) {
                    QString xmlSignalName = attrs.value("name").toString();
                    QString xmlSignalId = attrs.value("id").toString();

                    // 如果已经从解析库获取了信号信息，则在其中查找匹配的信号并设置isActive
                    if (!currentTopic.signalList.isEmpty()) {
                        bool found = false;
                        for (auto& signal : currentTopic.signalList) {
                            // 通过信号名称或ID匹配
                            if (signal.signalName == xmlSignalName ||
                                signal.signalIdentifier == xmlSignalId ||
                                signal.signalId == xmlSignalId) {
                                signal.isActive = true;  // XML中存在的信号设置为活跃
                                found = true;
                                qDebug() << "XML中找到信号，设置为活跃:" << signal.signalName << "ID:" << signal.signalId;
                                break;
                            }
                        }
                        if (!found) {
                            qDebug() << "XML中的信号在解析库中未找到:" << xmlSignalName << "ID:" << xmlSignalId;
                        }
                    } else {
                        // 如果没有从解析库获取信号信息，则直接添加XML中的信号
                        FcSignalData signal;
                        signal.signalName = xmlSignalName;
                        signal.signalIdentifier = xmlSignalId;
                        signal.signalId = xmlSignalId;
                        signal.isActive = true;  // XML中的信号默认为活跃

                        currentTopic.signalList.append(signal);
                        qDebug() << "添加XML信号:" << signal.signalName << "ID:" << signal.signalIdentifier;
                    }
                }
            }
        }
        else if (token == QXmlStreamReader::EndElement) {
            QString elementName = xml.name().toString();

            if (elementName == "Signal") {
                // Signal结束，无需特殊处理
            }
            else if (elementName == "topic") {
                // topic结束，保存当前主题到currentMsg.topicData
                if (inTopic) {
                    currentTopic.buildSignalIndex();

                    // 将处理后的主题数据保存到currentMsg.topicData
                    currentMsg.topicData = currentTopic;

                    qDebug() << "完成主题解析:" << currentTopic.topicName << "信号数:" << currentTopic.signalList.size();
                    inTopic = false;
                }
            }
            else if (elementName == "Msg") {
                // Msg结束，保存当前消息
                if (inMsg) {
                    currentScheme.unitMessages.append(currentMsg);
                    qDebug() << "完成消息解析:" << currentMsg.messageName << "主题信号数:" << currentMsg.topicData.signalList.size();
                    inMsg = false;
                }
            }
            else if (elementName == "solution") {
                // solution结束，保存当前方案
                if (inSolution) {
                    currentScheme.buildIndices();
                    schemes.append(currentScheme);
                    qDebug() << "完成方案解析:" << currentScheme.schemeName << "消息数:" << currentScheme.unitMessages.size();
                    inSolution = false;
                }
            }
        }
    }

    // 检查XML解析错误
    if (xml.hasError()) {
        qWarning() << "XML解析错误:" << xml.errorString() << "行:" << xml.lineNumber() << "列:" << xml.columnNumber();
        return false;
    }

    // 如果最后还有未保存的数据，保存它们
    if (inTopic) {
        currentTopic.buildSignalIndex();
        currentMsg.topicData = currentTopic;
    }
    if (inMsg) {
        currentScheme.unitMessages.append(currentMsg);
    }
    if (inSolution) {
        currentScheme.buildIndices();
        schemes.append(currentScheme);
    }

    m_currentParseProgress = 100;
    emit parseProgress(m_currentParseProgress);

    qDebug() << "成功解析XML文件，共解析出" << schemes.size() << "个方案";
    for (const auto& scheme : schemes) {
        qDebug() << "方案:" << scheme.schemeName << "功能单元消息数:" << scheme.unitMessages.size();
    }

    return !schemes.isEmpty();
}


FcDataParser::ParsedFrameData FcDataParser::parseRawFrameData(const QByteArray& frameData)
{
    ParsedFrameData result;
    
    if (!validateFrameData(frameData)) {
        reportError("解析原始帧数据", "帧数据验证失败");
        return result;
    }
    
    // 解析帧头
    int headerSize = 0;
    if (!parseFrameHeader(frameData, headerSize, result.messageId)) {
        reportError("解析原始帧数据", "帧头解析失败");
        return result;
    }
    
    // 提取其他基本信息
    result.sourceFuncId = extractSourceFuncIdFromFrame(frameData);
    result.timestamp = extractTimestampFromFrame(frameData);
    
    // 解析消息负载
    QByteArray payload = frameData.mid(headerSize);
    if (!parseMessagePayload(payload, result.messageId, result.topicValues)) {
        reportError("解析原始帧数据", "消息负载解析失败");
        return result;
    }
    
    result.isValid = true;
    return result;
}

bool FcDataParser::parseFcMessageData(const QByteArray& messageData, 
                                     const QString& expectedMessageId,
                                     QMap<QString, QString>& topicValues)
{
    topicValues.clear();
    
    if (messageData.isEmpty()) {
        reportError("解析FC消息数据", "消息数据为空");
        return false;
    }
    
    // 验证消息ID
    QString actualMessageId = extractMessageIdFromFrame(messageData);
    if (actualMessageId != expectedMessageId) {
        reportWarning("解析FC消息数据", 
                     QString("消息ID不匹配，期望: %1, 实际: %2").arg(expectedMessageId, actualMessageId));
    }
    
    return parseMessagePayload(messageData, expectedMessageId, topicValues);
}

bool FcDataParser::validateXmlStructure(const QString& xmlContent)
{
    if (xmlContent.isEmpty()) {
        return false;
    }
    
    // 基本的XML结构验证
    QXmlStreamReader reader(xmlContent);
    int elementCount = 0;
    bool hasUnitMessage = false;
    bool hasTopic = false;
    
    while (!reader.atEnd()) {
        reader.readNext();
        
        if (reader.isStartElement()) {
            elementCount++;
            QString elementName = reader.name().toString();
            
            if (elementName == "功能单元消息") {
                hasUnitMessage = true;
            } else if (elementName == "主题") {
                hasTopic = true;
            }
            
            if (!isValidXmlElement(elementName)) {
                reportWarning("XML结构验证", QString("未知的XML元素: %1").arg(elementName));
            }
        }
    }
    
    if (reader.hasError()) {
        reportError("XML结构验证", QString("XML语法错误: %1").arg(reader.errorString()));
        return false;
    }
    
    if (elementCount == 0) {
        reportError("XML结构验证", "XML内容为空");
        return false;
    }
    
    if (!hasUnitMessage && !hasTopic) {
        reportError("XML结构验证", "XML中没有找到功能单元消息或主题元素");
        return false;
    }
    
    return true;
}

bool FcDataParser::validateFrameData(const QByteArray& frameData)
{
    if (frameData.size() < 16) {  // 最小帧大小
        return false;
    }
    
    // 可以添加更多的帧数据验证逻辑
    // 例如检查帧头标识、校验和等
    
    return true;
}

QString FcDataParser::extractMessageIdFromFrame(const QByteArray& frameData)
{
    if (frameData.size() < 8) {
        return QString();
    }
    
    // 假设消息ID位于帧数据的特定位置
    // 这里需要根据实际的FC帧格式来实现
    QDataStream stream(frameData);
    stream.setByteOrder(QDataStream::LittleEndian);
    
    quint32 messageId;
    stream.skipRawData(4);  // 跳过前4个字节
    stream >> messageId;
    
    return QString::number(messageId);
}

QString FcDataParser::extractSourceFuncIdFromFrame(const QByteArray& frameData)
{
    if (frameData.size() < 12) {
        return QString();
    }
    
    // 假设源功能ID位于帧数据的特定位置
    QDataStream stream(frameData);
    stream.setByteOrder(QDataStream::LittleEndian);
    
    quint32 sourceFuncId;
    stream.skipRawData(8);  // 跳过前8个字节
    stream >> sourceFuncId;
    
    return QString::number(sourceFuncId);
}

QDateTime FcDataParser::extractTimestampFromFrame(const QByteArray& frameData)
{
    if (frameData.size() < 16) {
        return QDateTime::currentDateTime();
    }
    
    // 假设时间戳位于帧数据的特定位置
    QDataStream stream(frameData);
    stream.setByteOrder(QDataStream::LittleEndian);
    
    quint64 timestamp;
    stream.skipRawData(12);  // 跳过前12个字节
    stream >> timestamp;
    
    // 将时间戳转换为QDateTime（假设是Unix时间戳）
    return QDateTime::fromSecsSinceEpoch(timestamp);
}

bool FcDataParser::parseFrameHeader(const QByteArray& frameData, int& headerSize, QString& messageId)
{
    if (frameData.size() < 16) {
        return false;
    }

    // 根据实际的FC帧格式解析帧头
    // 这里提供一个示例实现
    QDataStream stream(frameData);
    stream.setByteOrder(QDataStream::LittleEndian);

    quint32 magic;
    stream >> magic;

    // 检查魔数（假设为0x5A5A5A5A）
    if (magic != 0x5A5A5A5A) {
        reportError("解析帧头", QString("无效的帧头魔数: 0x%1").arg(magic, 8, 16, QChar('0')));
        return false;
    }

    quint32 length;
    stream >> length;

    quint32 msgId;
    stream >> msgId;
    messageId = QString::number(msgId);

    headerSize = 16;  // 假设帧头大小为16字节
    return true;
}

bool FcDataParser::parseMessagePayload(const QByteArray& payload, const QString& messageId,
                                      QMap<QString, QString>& topicValues)
{
    if (payload.isEmpty()) {
        return true;  // 空负载也是有效的
    }

    // 根据消息ID和实际的数据格式解析负载
    // 这里提供一个示例实现
    QDataStream stream(payload);
    stream.setByteOrder(QDataStream::LittleEndian);

    // 假设负载格式为：主题数量 + (主题ID + 数据长度 + 数据) * N
    quint16 topicCount;
    stream >> topicCount;

    for (int i = 0; i < topicCount && !stream.atEnd(); ++i) {
        quint16 topicId;
        quint16 dataLength;
        stream >> topicId >> dataLength;

        if (dataLength > 0 && dataLength <= payload.size()) {
            QByteArray topicData(dataLength, 0);
            stream.readRawData(topicData.data(), dataLength);

            QString topicKey = QString::number(topicId);
            QString topicValue = convertBinaryToString(topicData, "DATA_BLOCK");
            topicValues[topicKey] = topicValue;
        }
    }

    return true;
}

bool FcDataParser::parseTopicValue(const QByteArray& data, int offset, int length,
                                  const QString& dataType, QString& value)
{
    if (offset + length > data.size()) {
        return false;
    }

    QByteArray topicData = data.mid(offset, length);

    if (dataType == "STRING") {
        value = QString::fromUtf8(topicData);
    }
    else if (dataType == "INTEGER") {
        value = QString::number(convertBinaryToInt(topicData, dataType));
    }
    else if (dataType == "FLOAT") {
        value = QString::number(convertBinaryToDouble(topicData, dataType));
    }
    else {
        value = convertBinaryToString(topicData, dataType);
    }

    return true;
}

QString FcDataParser::convertBinaryToString(const QByteArray& data, const QString& dataType)
{
    if (dataType == "STRING") {
        return QString::fromUtf8(data);
    }
    else if (dataType == "DATA_BLOCK") {
        // 将二进制数据转换为十六进制字符串
        return data.toHex(' ').toUpper();
    }
    else {
        // 默认转换为十六进制
        return data.toHex().toUpper();
    }
}

double FcDataParser::convertBinaryToDouble(const QByteArray& data, const QString& dataType)
{
    if (data.size() < 4) {
        return 0.0;
    }

    QDataStream stream(data);
    stream.setByteOrder(QDataStream::LittleEndian);

    if (dataType == "FLOAT" && data.size() >= 4) {
        float value;
        stream >> value;
        return static_cast<double>(value);
    }
    else if (data.size() >= 8) {
        double value;
        stream >> value;
        return value;
    }

    return 0.0;
}

int FcDataParser::convertBinaryToInt(const QByteArray& data, const QString& dataType)
{
    if (data.isEmpty()) {
        return 0;
    }

    QDataStream stream(data);
    stream.setByteOrder(QDataStream::LittleEndian);

    if (data.size() >= 4) {
        qint32 value;
        stream >> value;
        return value;
    }
    else if (data.size() >= 2) {
        qint16 value;
        stream >> value;
        return value;
    }
    else {
        qint8 value;
        stream >> value;
        return value;
    }
}

bool FcDataParser::isValidXmlElement(const QString& elementName)
{
    static QSet<QString> validElements = {
        "solution", "Msg", "topic", "Signal",
        "功能单元消息", "消息名", "消息ID", "源功能ID", "目的", "目的功能ID",
        "消息主题名称", "消息主题标识符", "发布订阅主题ID",
        "主题", "主题ID", "主题名称", "主题标识符", "主题类型",
        "格式类型", "数据长度", "长度类型", "包含信号",
        "信号", "信号名称", "信号标识符", "信号组标志", "起始字", "起始位",
        "LSB", "MSB", "单位代码"
    };

    return validElements.contains(elementName);
}

bool FcDataParser::isValidDataType(const QString& dataType)
{
    return m_knownDataTypes.contains(dataType);
}

bool FcDataParser::isValidMessageId(const QString& messageId)
{
    if (messageId.isEmpty()) {
        return false;
    }

    // 检查消息ID是否为数字
    bool ok;
    messageId.toInt(&ok);
    return ok;
}

void FcDataParser::reportError(const QString& context, const QString& error)
{
    QString fullError = QString("[%1] %2").arg(context, error);
    qCritical() << fullError;
    emit parseError(fullError);
}

void FcDataParser::reportWarning(const QString& context, const QString& warning)
{
    QString fullWarning = QString("[%1] %2").arg(context, warning);
    qWarning() << fullWarning;
    emit parseWarning(fullWarning);
}
