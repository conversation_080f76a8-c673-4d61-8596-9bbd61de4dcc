#ifndef CONTINUE_HANDLER_H
#define CONTINUE_HANDLER_H

#include "command_handler.h"

/**
 * @brief 继续命令处理器
 */
class ContinueCommandHandler : public CommandHandler
{
    Q_OBJECT

public:
    explicit ContinueCommandHandler(QObject *parent = nullptr);
    ~ContinueCommandHandler() override;
    CommandHandler::CommandResult processCommand(const QByteArray &data) override;

private:
    // 可以添加特定于继续命令的私有成员和方法
};

#endif // CONTINUE_HANDLER_H 
