#include "tcp_data_processor.h"
#include <QThread>
#include <QJsonParseError>
#include "command_handlers/command_handler.h"
#include <QDateTime>
#include <QDebug>
#include <QJsonDocument>
#include <QMutexLocker>
#include "utils/command_handlers/subscribe_handler.h"
#include "utils/command_handlers/unsubscribe_handler.h"
#include "utils/command_handlers/suspend_handler.h"
#include "utils/command_handlers/continue_handler.h"
#include "utils/command_handlers/response_handler.h"

// 初始化静态变量
TcpDataProcessor* TcpDataProcessor::instance = nullptr;
QMutex TcpDataProcessor::mutex;

/**
 * 获取单例实例
 */
TcpDataProcessor* TcpDataProcessor::getInstance() {
    QMutexLocker locker(&mutex);
    if (instance == nullptr) {
        instance = new TcpDataProcessor();
    }
    return instance;
}

/**
 * 构造函数
 */
TcpDataProcessor::TcpDataProcessor(QObject *parent)
    : QObject(parent),
      verboseLogging(false)
{
    qDebug() << "TcpDataProcessor 初始化，线程ID:" << (quintptr)QThread::currentThreadId();
    
    // 初始化命令处理器映射
    m_commandHandlers.clear();
}

/**
 * 析构函数
 */
TcpDataProcessor::~TcpDataProcessor() {
    qDebug() << "TcpDataProcessor 销毁";
    
    // 清理命令处理器
    for (auto it = m_commandHandlers.begin(); it != m_commandHandlers.end(); ++it) {
        if (it.value()) {
            it.value()->deleteLater();
        }
    }
    m_commandHandlers.clear();
}

/**
 * 设置详细日志
 */
void TcpDataProcessor::setVerboseLogging(bool enable) {
    verboseLogging = enable;
    qDebug() << "详细日志已" << (enable ? "启用" : "禁用");
}

/**
 * 注册命令处理器
 */
void TcpDataProcessor::registerCommandHandler(SUBSCRIBE_COMMAND cmdType, CommandHandler* handler) {
    if (!handler) {
        qWarning() << "尝试注册空命令处理器，命令类型:" << cmdType;
        return;
    }
    
    // 如果已存在处理器，先删除旧的
    if (m_commandHandlers.contains(cmdType)) {
        CommandHandler* oldHandler = m_commandHandlers.take(cmdType);
        if (oldHandler) {
            oldHandler->deleteLater();
        }
    }
    
    // 注册新处理器
    m_commandHandlers[cmdType] = handler;
    
    qDebug() << "注册命令处理器，命令类型:" << cmdType;
}

/**
 * 取消注册命令处理器
 */
void TcpDataProcessor::unregisterCommandHandler(SUBSCRIBE_COMMAND cmdType) {
    if (m_commandHandlers.contains(cmdType)) {
        CommandHandler* handler = m_commandHandlers.take(cmdType);
        if (handler) {
            handler->deleteLater();
        }
        qDebug() << "取消注册命令处理器，命令类型:" << cmdType;
    }
}

/**
 * 处理接收到的数据
 */
void TcpDataProcessor::processData(const QByteArray &data) {
    if (data.isEmpty()) {
        qWarning() << "收到空数据包";
        emit dataProcessed("收到空数据包");
        return;
    }
    
    if (verboseLogging) {
        qDebug() << "收到数据，大小:" << data.size() << "字节";
        qDebug() << "处理线程ID:" << (quintptr)QThread::currentThreadId();
        
        // 以十六进制显示前 500 个字节
        QString hexData = data.left(500).toHex(' ').toUpper();
        if (data.size() > 500) {
            hexData += "...";
        }
        qDebug() << "数据内容(十六进制):" << hexData;
    }

    // 尝试提取命令类型
    SUBSCRIBE_COMMAND cmdType = extractCommandType(data);
    if (cmdType != 0) {
        // 如果成功提取到命令类型，则交给相应的处理器处理
        processCommandData(data, cmdType);
    }
    else {
        emit commandError((SUBSCRIBE_COMMAND)0, "错误的命令");
    }
}

/**
 * 提取命令类型
 */
SUBSCRIBE_COMMAND TcpDataProcessor::extractCommandType(const QByteArray &data) {
    // 确保数据长度足够
    if (data.size() < 1) {
        qWarning() << "数据包过短，无法提取命令类型";
        return (SUBSCRIBE_COMMAND)0;
    }
    
    // 命令类型现在在数据包的第一个字节
    quint8 cmd = (quint8)data.at(4);
    
    // 验证命令值在有效范围内
    if (cmd == CMD_RESPONSE || cmd == CMD_DATA) {
        return (SUBSCRIBE_COMMAND)cmd;
    }
    
    qWarning() << "无效的命令类型:" << cmd;
    return (SUBSCRIBE_COMMAND)0;
}

/**
 * 处理文本格式的数据
 */
// void TcpDataProcessor::processTextData(const QByteArray &data) {
//     QString text = QString::fromUtf8(data);
//     QString result = QString("处理文本数据: %1 个字符").arg(text.length());
    
//     if (verboseLogging) {
//         // 限制日志中显示的文本长度，避免日志过长
//         QString displayText = text;
//         if (displayText.length() > 100) {
//             displayText = displayText.left(100) + "...";
//         }
//         result += QString("\n内容: %1").arg(displayText);
//     }
    
//     qDebug() << result;
    
//     // 这里可以添加其他文本处理逻辑
//     text = text.trimmed();
    
//     // 可以根据特定格式解析文本
//     if (text.startsWith("CMD:")) {
//         QString command = text.mid(4).trimmed();
//         result += QString("\n解析到命令: %1").arg(command);
//     }
    
//     emit dataProcessed(result);
// }

/**
 * 处理二进制格式的数据
 */
void TcpDataProcessor::processBinaryData(const QByteArray &data) {
    QString result = QString("处理二进制数据: %1 字节").arg(data.size());
    
    if (verboseLogging) {
        // 以十六进制显示前 50 个字节
        QString hexData = data.left(50).toHex(' ').toUpper();
        if (data.size() > 50) {
            hexData += "...";
        }
        result += QString("\n十六进制: %1").arg(hexData);
    }
    
    qDebug() << result;
    
    // 这里可以添加二进制数据处理逻辑
    QByteArray processedData = data;
    
    emit dataProcessed(result);
    emit binaryDataProcessed(processedData);
}

/**
 * 处理命令数据
 */
void TcpDataProcessor::processCommandData(const QByteArray &data, SUBSCRIBE_COMMAND cmdType) {
    QString cmdName;
    switch (cmdType) {
        case CMD_DATA:        cmdName = "数据"; break;
        case CMD_RESPONSE:    cmdName = "响应"; break;
        default:              cmdName = QString("未知(%1)").arg(cmdType); break;
    }
    
    qDebug() << "处理" << cmdName << "命令，数据大小:" << data.size() << "字节";
    
    // 标记是否需要发送响应信号，避免重复发送
    bool needEmitResponse = false;
    
    // 检查是否有对应的处理器
    if (m_commandHandlers.contains(cmdType)) {
        CommandHandler* handler = m_commandHandlers[cmdType];
        if (handler) {

                // 使用新接口处理命令并获取详细结果
            CommandHandler::CommandResult result = handler->processCommand(data);
            if (result.success) {
                qDebug() << cmdName << "命令处理成功: " << result.message;
                // 对于响应命令，设置标记以发送订阅响应信号
                if (cmdType == CMD_RESPONSE) {
                     emit commandProcessed(cmdType, true, result.message);
                }
            }
            else {
                qWarning() << cmdName << "命令处理失败: " << result.errorMessage;
                emit commandError(cmdType, result.errorMessage);
            }

        }
    } 
    // 如果是响应命令且没有处理器或还没发送过响应信号，发出特殊信号
    else if (cmdType == CMD_RESPONSE && !needEmitResponse) {
        emit subscribeResponse(data);
        qDebug() << "发出订阅响应信号";
    } 
    // 没有处理器且不是响应命令
    else if (cmdType != CMD_RESPONSE) {
        qWarning() << "未找到" << cmdName << "命令的处理器";
        emit commandError(cmdType, QString("未找到%1命令的处理器").arg(cmdName));
    }
}

/**
 * 识别数据类型
 */
QString TcpDataProcessor::identifyDataType(const QByteArray &data) {
    // 尝试解析为 JSON
    QJsonParseError parseError;
    QJsonDocument::fromJson(data, &parseError);
    if (parseError.error == QJsonParseError::NoError) {
        return "JSON";
    }
    
    // 检查是否是文本数据
    // 这里使用一个简单的启发式方法：检查数据是否全部是可打印字符或常见控制字符
    bool isText = true;
    for (int i = 0; i < data.size(); i++) {
        unsigned char c = data.at(i);
        // 检查是否是可打印 ASCII 字符，制表符，换行符，回车符
        if (!(c >= 32 && c <= 126) && c != 9 && c != 10 && c != 13) {
            isText = false;
            break;
        }
    }
    
    if (isText) {
        return "TEXT";
    }
    
    // 默认认为是二进制数据
    return "BINARY";
}
