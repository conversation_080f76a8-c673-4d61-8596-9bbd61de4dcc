#ifndef __FRAMESTRUCTS_H__
#define __FRAMESTRUCTS_H__
#include <cstdint>

#define DEV_STATUS_NUM				(128)

//#pragma pack(push, 1)

//@brief fc扩展帧头 
typedef struct {
    unsigned    len         : 12;
    unsigned    port        : 2;
    unsigned    rx_status   : 8;
    unsigned    time_h      : 10;
    unsigned    time_l      : 32;        
} FrameHeaderEx;

/*
typedef struct {
	unsigned	time1;
	unsigned 	time2;
	unsigned 	time3;
	unsigned 	length;		// 31:16 -帧长  15:14 -  通道标志			13:3-保留			2-CRC错误			1：消息id错误		0：长度错误
	unsigned    count;	    //2022-01-21 和许恒确认在扩展帧头里面添加了一个count字用于计数, 用来做丢帧的检测.
	unsigned    reserved;
} FcExtendHeader;
*/

typedef struct {
	unsigned	time1;			//
	unsigned    time1_reserved; //本地时标为8个字节，此地分为两个字（time1,time1_reserved）
	unsigned    hrtc      : 10;//大小端，低位0~9
	unsigned    count     : 22;//大小端，高位10~31
	unsigned    time2;
	unsigned    flag      : 16;//因大小端问题，所以length放后面（进行了大小端转换）
    unsigned    length    : 16;
	unsigned 	reserved;//保留字段 4个字节
} FcExtendHeader;



typedef struct {
    uint8_t     r_ctl;      // The routing portion says if this is a data frame or a link-control frame (either an ACK or a Link_Response), and the information portion indicates the type of data.
    uint32_t    d_id : 24;  // The FC address of the destination.
    uint8_t     cs_ctl;     // Essentially, Quality of Service.
    uint32_t    s_id : 24;  // The FC address of the originating node.
    uint8_t     type;       // Indicates the next protocol (what’s in the Payload), unless R_CTL indicates a control frame.
    uint32_t    f_ctl : 24;  // Various crazy FC options, such as sequencing information and what to do in case of a problem.
    uint8_t     seq_id;     // A sequence number, just like IP.
    uint8_t     df_ctl;     // Indicates the presence of optional headers, and the size.
    uint16_t    seq_cnt;    // The number of frames that have been transmitted in a sequence.
    uint16_t    ox_id;      // Assigned by the initiator, used to group related sequences.
    uint16_t    rx_id;      // Same as the OX_ID, but assigned by a target node.
    uint32_t    param;      // Mostly used as a “relative offset” in sequences, much like IP’s offset.
}FcFrameHeaderSF;

// FC-2帧头
typedef struct {
    #define FC2_F_CTL_FILL_LEN_MASK     (0x00000003)
    unsigned    d_id        : 24;       // 目的端地址
    unsigned    r_ctl       : 8;        // 路由控制，这个字段指明了一个帧是链路控制帧还是数据帧。链路控制帧是非数据帧，不携带任何有效载荷。这些帧被用作设置和发送信息。相反，数据帧会携带有效载荷，被用作数据传输

    unsigned    s_id        : 24;
    unsigned    cs_ctl      : 8;

    unsigned    f_ctl       : 24;
    unsigned    type        : 8;        // 0x01 - els   0x49 - asm

    unsigned    seq_cnt     : 16;
    unsigned    df_ctl      : 8;
    unsigned    seq_id      : 8;
    
    unsigned    ox_id       : 16;
    unsigned    rx_id       : 16;

    unsigned    parameter   : 32;
}FrameHeaderFC2;

//FC-ASM 帧头
typedef struct  {
    unsigned    messageId   : 32;

    unsigned    reserved1   : 32;
    unsigned    reserved2   : 32;

    unsigned    length      : 24;
    unsigned    priority    : 7;
    unsigned    lb          : 1;
}FrameHeaderAsm;

// FC-Els 帧头
typedef struct {
    unsigned   ls_cmd;

    unsigned    reserved0   : 16;
    unsigned    seed        : 8;
    unsigned    sn          : 8;

    unsigned    reserved1;
    unsigned    functionId;
}FrameHeaderEls;


// 发送帧头
typedef struct {
    unsigned    sendFcLen;
    unsigned    sof;
    FrameHeaderFC2  fcHeader;
    union {
        FrameHeaderAsm  asmHeader;
        FrameHeaderEls  elsHeader;
    };
}FcFrameHeaderSend;


typedef struct {
    FrameHeaderEx   exFcHeader;
    unsigned        sof;
    FrameHeaderFC2  fcHeader;
    union {
        FrameHeaderAsm  asmHeader;
        FrameHeaderEls  elsHeader;
    };
}FcFrameHeaderRecv;


typedef struct {
	FcExtendHeader  exFcHeader; //6
	unsigned        sof;                 //1
    FrameHeaderFC2  fcHeader;  //6
    FrameHeaderAsm  asmHeader; // 4 //要取的msgid是asmheader的messageid
}FcFrameHeader; 


typedef struct {
	// remove those unknown1
	unsigned	unknown1;			//20220125,和李斌确认在FC头之后还需要两个32位之后才是真正的Aoxe头
	unsigned	unknown2;
	//
	unsigned	source;				// 源功能id取低16位
	unsigned	dest;				// 目的功能id取高16位

	unsigned 	msgSpec;
	unsigned	topicId;			// 主题id取低16位

	uint64_t	timetag;
	unsigned	qos;

	unsigned	length;

	unsigned    bitimap1;   //ICD 1.2之前没有这4个
	unsigned    bitimap2;
	unsigned    bitimap3;
	unsigned    bitimap4;
	
}AoxeHeader;

typedef struct {
    unsigned	source;
    unsigned	dest;

    unsigned 	msgSpec;
    unsigned	topicId;

    uint64_t	timetag;
    unsigned	qos;

    unsigned	length;

    unsigned    bitimap1;   //ICD 1.2之前没有这4个
    unsigned    bitimap2;
    unsigned    bitimap3;
    unsigned    bitimap4;

}I394AoxeHeader;

#pragma pack(push, 1)
typedef struct  {
    uint32_t pack_length; //UDP包长=zmq头+执行环境头+muse数据
    uint32_t ctr_command; //控制指令
    uint32_t tv_sec;      //UTC时间(秒)
    uint32_t tv_usec;     //UTC时间(微妙)
    uint32_t device_id;    //设备号
    uint32_t reserved1;   //保留字段1
    uint64_t reserved2;   //保留字段2
    uint32_t model;       //型号标识
    uint32_t bench_id;    //系统标识
    uint32_t data_bus_type; //总线标识:1-FC,2-任务1394,3-飞管1394,4-818,5-muse主网,6-muse备网
    uint32_t card_id;       //卡号
    uint32_t port_id;       //节点号
    uint32_t port_stat1;    //端口状态1
    uint32_t port_stat2;    //端口状态2
    uint32_t port_stat3;    //端口状态3
}/*__attribute__((packed))*/ ZMQWrapHeader;
#pragma pack(pop)


typedef struct {
	unsigned 	sy 		 : 4;		  // 未使用，固定设置为0
	unsigned 	tc 		 : 4;		  // 固定设置为0xA，表示异步流包
	unsigned 	channel  : 6;  		  //表示异步流包的目的地址
	unsigned	tag 	 : 2;	      //等时包所携带的数据格式，固定为0
	unsigned 	dataLen  : 16;  	  // 数据包负载长度，单位：字节；包含：ASM Header、Message Payload、Packet Trailer

	//unsigned 	crc;
} I394Header;


typedef struct {
	unsigned 	messageId;
	unsigned	security;
	unsigned	nodeId;
	unsigned 	length : 24;
	unsigned	priority : 8;
}I394AsmHeader;


typedef struct {
	unsigned 	magic;				// 监控头，固定标识  0x5A5A5A5A
	unsigned 	length;				// 监控包长度
	unsigned 	channel;			// 通道号 指哪一路监控通道
	unsigned 	sequence;			// 监控包次序，上电/复位后为0，之后每监控一包数据，递增1
	unsigned 	sysRtcHigh;			// 包数据被监控的系统飞行时间，高32位
	unsigned 	sysRtcLow;			// 包数据被监控的系统飞行时间，低32位，单位：100ms	
	unsigned 	localRtcHigh;		// 本地RTC高32位
	unsigned	localRtcLow;		// 本地RTC低32位，单位：100ms
	unsigned 	monitorInfo;		// 监控包信息 由LLC IP核提供的监控信息
	I394Header	i394Header;			// 1394包头信息
	I394AsmHeader i394Asm;
}I394FrameHeader;

//#pragma pack(pop)

typedef struct {
	unsigned	tag;				// 标签： 0 - 订阅id列表	1 - 激励消息
	unsigned	size;				// 个数，id列表时 为多少个订阅id
}InteractiveHeader;


typedef struct {
	unsigned 	portNumber 		: 24;			// 端口号
	unsigned 	linkStatus 		: 8;			// 连接状态

	unsigned 	deviceNumber 	: 24;			// 设备号
	unsigned 	loginStatus 	: 8;			// 登陆状态
}DevPortStatus;

typedef struct {
	unsigned 	version;			// 版本信息 固定为0x1
	uint64_t 	timestamp;			// 时间戳
	unsigned	vender;				// 厂商标识
	char		versionInfo[64];	// 版本信息
	unsigned	broadcastCycle;		// 广播周期
	char		dataTime[16];		// 日历时间快照
	char		taskRtc[16];		// 任务rtc
	char		reserved[396];		// 预留
	DevPortStatus portStatus[DEV_STATUS_NUM];	// 端口状态信息		
}FcElsDevStatusHeader;

//Muse执行环境帧头

//#pragma pack(push, 1)
typedef struct{
    uint16_t net : 8;           //网络类型
    uint16_t protocol_type : 8;     //协议类型 0x1 udp 0x2 tcp 0x3 pcie
    uint16_t port;      //端口号
    uint32_t host;             //设备地址
}ENDPOINT_OPTION;

//执行环境包头
typedef struct{
    uint32_t source;  //源id
    uint32_t dest;    //目的id

    uint16_t destId;  // caller_id:请求方id  dstId
    uint16_t protocolVersion : 4;  //协议版本
    uint16_t msgspec : 3; //传输特性
    uint16_t pushTag : 1;   //转发标志
    uint16_t routerId : 8;  //路由器id
    uint16_t sourceId ;  //service_id 服务id  srcId

    uint16_t interfaceId : 12;   //接口id
    uint16_t interfaceType : 4; //接口类型：0-RPC 1-PUBSUB 2-EVENT

    uint64_t serializedTag : 1 ; //序列化标签 o:未序列化，1：序列化
    uint64_t spare_seq : 8;
    uint64_t spare : 3;
    uint64_t timestamp : 52;     //发送时间戳

    uint32_t length;      //消息负载长度
    uint32_t spare_32;      //消息负载长度

    ENDPOINT_OPTION  source_option; //源信息列表
    ENDPOINT_OPTION  dest_option; //目的信息列表
}ExeEvFrameHeader;
//#pragma pack(pop)


#define I394MONITOR_HEAD_SIZE		(sizeof(I394FrameHeader) - sizeof(I394AsmHeader) - sizeof(I394Header))
#define __i394(addr, length)		{addr = reinterpret_cast<uint8_t*>(addr) + I394MONITOR_HEAD_SIZE; length -= I394MONITOR_HEAD_SIZE;}


#define FC_PAYLOAD_OFFSET			(sizeof(FcExtendHeader) + sizeof(unsigned) + sizeof(FrameHeaderFC2))
#define FC_ASM_PAYLOD_OFFSET 		(FC_PAYLOAD_OFFSET + sizeof(FrameHeaderAsm))
#define FC_ELS_PAYLOAD_OFFSET		(FC_PAYLOAD_OFFSET + sizeof(FrameHeaderEls))
#define FC_HEADER_OFFSET			(sizeof(FcExtendHeader) + sizeof(unsigned))

#define __fc_asm_header(addr)		reinterpret_cast<FrameHeaderAsm*>((addr) + FC_PAYLOAD_OFFSET)
#define __fc_els_header(addr)		reinterpret_cast<FrameHeaderEls*>((addr) + FC_PAYLOAD_OFFSET)
#define __fc_els_dev_status(addr)	reinterpret_cast<FcElsDevStatusHeader*>((addr) + FC_ELS_PAYLOAD_OFFSET)
#define __fc_header(addr)			reinterpret_cast<FrameHeaderFC2*>((addr) + FC_HEADER_OFFSET)

#define	 	ELS_TAG						(0x01)
#define  	ASM_TAG						(0x49)


#define		FRAME_FC_TAG				(0x00)
#define 	FRAME_1394_TAG				(0x01)
#define 	FRAME_STATUS_TAG			(0x02)
#define 	FRAME_818_TAG				(0x03)
#define 	STATUS_FLAG					(0xff000000)

// @breif 是否为设备端口状态数据
#define __is_devport_status(els)	(els->functionId == STATUS_FLAG)


inline int probeFrameType(const uint8_t * buf, size_t size) {
	uint8_t* data = const_cast<uint8_t*>(buf);
	/*
	uint8_t tag = *data;
	if (tag != FRAME_FC_TAG) {
		return tag;
	}
	
	FrameHeaderFC2* fc = __fc_header(data + 1);
	FrameHeaderEls* els = __fc_els_header(data+1);	
	if (fc->type == ELS_TAG && __is_devport_status(els)) {
		return FRAME_STATUS_TAG;
	}
	 */
	ZMQWrapHeader * zmqWrapHeader = (ZMQWrapHeader*) data;
	int tag = zmqWrapHeader->data_bus_type;
	return tag;
}



#pragma pack(push, 1)

//执行环境包头
typedef struct{
    uint16_t destId;  // caller_id:请求方id  dstId
    uint16_t sourceId ;  //service_id 服务id  srcId
    //uint16_t method_id;   //方法id:前四个字节为type，后12个字节为方法id
    uint16_t method_id ;   //接口id
    //uint16_t method_type : 4; //接口类型：0-RPC 1-PUBSUB 2-EVENT
    uint8_t method_type ;
//    MSGPACK_DEFINE(destId,sourceId,method_id,method_type);
} SubToIVFrameHeader;
#pragma pack(pop)

#endif //__FRAMESTRUCTS_H__
