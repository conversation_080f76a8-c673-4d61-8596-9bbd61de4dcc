#include "response_handler.h"
#include "../response_data_store.h"
#include <QDebug>
#include <QDateTime>
#include "third/parseAndAnalysis.h"
#include "utils/file_list_service.h"
#include "ui/scheme/history_data_store.h"
#include "third/tcpFrameStruct.h"

/**
 * @brief 构造函数
 */
ResponseCommandHandler::ResponseCommandHandler(QObject *parent) 
    : CommandHandler(parent) 
{
    qDebug() << "创建响应命令处理器";
}

/**
 * @brief 析构函数
 */
ResponseCommandHandler::~ResponseCommandHandler() 
{
    qDebug() << "销毁响应命令处理器";
}

/**
 * @brief 处理响应命令并返回详细结果
 */
CommandHandler::CommandResult ResponseCommandHandler::processCommand(const QByteArray &data)
{
    //修改数据解析方式为二楼fc/1394 解析
    CommandResult result;
    result.success = false;
    int ret;
    if (data.isEmpty()) {
        result.errorMessage = "响应数据为空";
        qWarning() << result.errorMessage;
        return result;

    }
    char processBuf[BUFFER_SIZE] = {0};
    ret = deserialize(data.constData(), data.size(), processBuf);
    //SUBSCRIBE_PACKAGE_CMD_DATA *cmdData = static_cast<SUBSCRIBE_PACKAGE_CMD_DATA *>((void *)(processBuf));
    SUBSCRIBE_PACKAGE_CMD_DATA_BUFFER *cmdData = static_cast<SUBSCRIBE_PACKAGE_CMD_DATA_BUFFER *>((void *)(processBuf));
    if (cmdData->dataLen <= 0) {
        qWarning() << "收到的cmdData数据有误";
        result.errorMessage = "收到的cmdData数据有误";
        return result;
    }

    void *token = FileListService::getInstance().getParseandanalysisP();
    if (token == nullptr) {
        result.errorMessage = "动态库指针为空";
        qWarning() << result.errorMessage;
        return result;
    }
    ParsedData unData;
    DataBuffer buffer;
    buffer.buffer = (unsigned char *)cmdData->dataPtr;
    buffer.size = cmdData->dataLen;

    if (parseData(token, buffer, unData) != 0) {
        result.errorMessage = "unpackMuseData 解析错误";
        qWarning() << result.errorMessage;
        return result;
    }

    //信号类型 //总线标识:1-FC,2-任务1394,3-飞管1394
    uint32_t dataType = unData.header.zmqWrapHeader.data_bus_type;

    //源功能单元id 和 发布订阅主题id
    int srcUnitId,pubSubTopicId;
    if(dataType == 1)
    {
        srcUnitId = unData.header.headerFc.aoxeHeader.source & 0xffff;
        pubSubTopicId = unData.header.headerFc.aoxeHeader.topicId & 0xffff;
    }
    else if(dataType == 2||dataType==3)
    {
        srcUnitId = unData.header.header1394.aoxeHeader.source & 0xffff;
        pubSubTopicId = unData.header.header1394.aoxeHeader.topicId & 0xffff;
    }
    else {
        result.errorMessage = "data Type error,unknown types";
        qWarning() << result.errorMessage;
        return result;
    }


    // 1. 存储到HistoryDataStore（历史数据）
    HistoryDataStore::getInstance().addData(unData);

    // 2. 构造TreeView索引key并存储到ResponseDataStore（实时数据）
    QMap<QString, QVector<QString>> treeViewDataMap;

    //存储数据类型和时间
    QVector<QString> msgData;
    msgData.push_back(QString::number(dataType));

    // 将秒和微秒组合成毫秒时间戳
    qint64 msecs = static_cast<qint64>(unData.header.zmqWrapHeader.tv_sec) * 1000 + unData.header.zmqWrapHeader.tv_usec / 1000;
    // 从Unix时间戳创建QDateTime（UTC时间）
    QDateTime dateTime = QDateTime::fromMSecsSinceEpoch(msecs, Qt::UTC);
    // 转换为本地时间并格式化为字符串
    QString timeString = dateTime.toLocalTime().toString("yyyy-MM-dd HH:mm:ss.zzz");
    msgData.push_back(timeString);
    //信号序列化编码为空
    QString msgKey = QString::number(srcUnitId) + "_" + QString::number(pubSubTopicId) + "_0";
    treeViewDataMap[msgKey] = msgData;

    // 遍历signalValueMap，为每个信号构造TreeView索引key
    for (const auto& signalPair : unData.signalValueMap) {
        QString signalId = QString::fromStdString(signalPair.first);
        QString signalValue = QString::fromStdString(signalPair.second);

        // 构造TreeView索引key：sourceFuncId_pubSubTopicId_signalId
        QString treeViewKey = QString("%1_%2_%3")
                .arg(srcUnitId)
                .arg(pubSubTopicId)
                .arg(signalId);

        // 存储信号值到ResponseDataStore
        QVector<QString> signalData;
        signalData.append(signalValue.split("_").first());  // 信号值
        //构造原始值插入
        signalData.append(signalValue.split("_").last());

        treeViewDataMap[treeViewKey] = signalData;

        qDebug() << "构造TreeView索引key:" << treeViewKey << "值:" << signalValue;
    }

    // 3. 批量更新到ResponseDataStore
    if (!treeViewDataMap.isEmpty()) {
        ResponseDataStore::getInstance().updateAllData(treeViewDataMap);
        qDebug() << "更新ResponseDataStore，信号数量:" << treeViewDataMap.size();
    }

    result.success = true;
    return result;

}

