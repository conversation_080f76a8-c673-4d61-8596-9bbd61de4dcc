#include "unit_standard_item.h"

UnitStandardItem::UnitStandardItem(const QString& text)
    : QStandardItem(text)
    , m_isHaveCombox(false)
    , m_defaultUnitID(0)
    , m_currentUnitID(0)
{
}

UnitStandardItem::UnitStandardItem(const QIcon& icon, const QString& text)
    : QStandardItem(icon, text)
    , m_isHaveCombox(false)
    , m_defaultUnitID(0)
    , m_currentUnitID(0)
{
}

QStandardItem* UnitStandardItem::clone() const
{
    UnitStandardItem* item = new UnitStandardItem(text());
    
    // 复制基类属性
    item->setData(data(Qt::UserRole), Qt::UserRole);
    item->setCheckable(isCheckable());
    item->setCheckState(checkState());
    item->setEnabled(isEnabled());
    item->setEditable(isEditable());
    item->setIcon(icon());
    item->setToolTip(toolTip());
    item->setWhatsThis(whatsThis());
    item->setStatusTip(statusTip());
    item->setFont(font());
    item->setTextAlignment(textAlignment());
    item->setBackground(background());
    item->setForeground(foreground());
    
    // 复制单位相关属性
    item->m_isHaveCombox = m_isHaveCombox;
    item->m_unitList = m_unitList;
    item->m_defaultUnitID = m_defaultUnitID;
    item->m_defaultUnitName = m_defaultUnitName;
    item->m_currentUnitID = m_currentUnitID;
    item->m_currentUnitName = m_currentUnitName;
    
    return item;
}
