#include "suspend_handler.h"
#include <QDebug>

/**
 * @brief 构造函数
 */
SuspendCommandHandler::SuspendCommandHandler(QObject *parent) 
    : CommandHandler(parent) 
{
    qDebug() << "创建暂停命令处理器";
}

/**
 * @brief 析构函数
 */
SuspendCommandHandler::~SuspendCommandHandler() 
{
    qDebug() << "销毁暂停命令处理器";
}

/**
 * @brief 处理订阅命令并返回详细结果
 */
CommandHandler::CommandResult SuspendCommandHandler::processCommand(const QByteArray &data)
{
    // 使用基类的新通用命令处理方法，返回详细结果
    return processCommonCommand(data, CMD_SUBSCRIBE, "订阅");
}
