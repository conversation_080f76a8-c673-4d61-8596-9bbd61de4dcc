﻿#ifndef SchemeAddDialog_H
#define SchemeAddDialog_H

#include <QDialog>
#include <QStandardItemModel>
#include <QTreeView>
#include <QTreeWidget>
#include <QLineEdit>
#include <QPushButton>
#include <QLabel>
#include <QSplitter>
#include <QListWidget>
#include <QDomElement>
#include <QNetworkAccessManager>
#include <QNetworkReply>

#include "solution.h"
#include "third/parseAndAnalysis.h"


namespace Ui {
class SchemeAddDialog;
}

class SchemeAddDialog : public QDialog
{
    Q_OBJECT

public:
    explicit SchemeAddDialog(const QString &schemeName,QWidget *parent = nullptr);
    ~SchemeAddDialog();

    //icd 解析及加载
    int initIcdToken();

private:
    Ui::SchemeAddDialog *ui;
    void* m_decoderToken = nullptr;

private slots:
    void onSearchTextChanged(const QString &text);
    void onSearchUpClicked();
    void onSearchDownClicked();
    void performSearch(const QString &text);
    void highlightCurrentSearchResult();
    void onUnitItemClicked(QTreeWidgetItem *item, int column);
    void onTreeItemClicked(const QModelIndex &index);
    void onCancelClicked();
    void onSaveAsClicked();
    void onDeleteSelectedItem();
    void onClearSelectionClicked();
    void onAddToSchemeClicked();
    void showContextMenu(const QPoint &pos);
    void onUploadFinished(QNetworkReply *reply);


private:
    void setupUI();
    void setupConnections();
    //更新最左边icd tree
    void initializeUnitTree();
    void initializeTreeView();
    void initializeSelectedList();
    void loadSchemeData(const QString &schemeName);
    void filterTreeItems(QTreeWidgetItem *item, const QString &text);
    void uploadToServer(const QString &content, const QString &fileName);

    //更新中间信号
    void updateSchemeTreeView(QTreeWidgetItem *selectedItem);
    QStandardItem* addSignalRecursive(QStandardItem* node, const QStringList& pathParts);
    //查找并返回特定名称的child，没有则新建该child
    QStandardItem* findChildItemByNama(QStandardItem* node,QString name);
    void processChildNodes(const QModelIndex &parentIndex, QStringList &pathParts, TopicStruct &topic);

    //在返回结果前清空界面，保证下一次点开该界面是最新状态
    void clearView();

private:
    QStandardItemModel *treeModel;
    QString currentSchemeName;

    // 主要组件
    QSplitter *mainSplitter;

    // 左侧面板组件
    QWidget *leftPanel;
    QLabel *schemeNameLabel;
    QLineEdit *searchEdit;
    QTreeWidget *unitTreeWidget;
    QPushButton *searchUpButton;
    QPushButton *searchDownButton;
    QVector<QTreeWidgetItem*> searchResults;  // 存储搜索结果
    int currentSearchIndex;  // 当前搜索结果的索引

    // 中间面板组件
    QWidget *middlePanel;
    QTreeView *schemeTreeView;
    QPushButton *clearSelectionButton;
    QPushButton *addToSchemeButton;

    // 右侧面板组件
    QWidget *rightPanel;
    QLabel *selectedLabel;
    QListWidget *selectedListWidget;
    QLineEdit *schemeNameEdit;
    QPushButton *cancelButton;
    QPushButton *saveAsButton;

    //接口map key:接口名称 value:接口缩略名
    QMap<QString,QString>m_interfaceMap;

    solution m_solution;
//    InterfaceInfo m_interfaceInfo;
    // 网络请求管理器
    QNetworkAccessManager *m_networkManager;
    QMap<QString, parseFromLib> m_message_infoFromLib;

signals:
    //增加方案完成信号
    // return 1:有新增 2:取消
    void addSchemefinish(int ret);
};

#endif // SchemeAddDialog_H
