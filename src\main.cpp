#include <QApplication>
#include <QCommandLineParser>
#include <QDebug>
#include <QDir>
#include <QFileInfo>
#include "ui/login/login_window.h"
#include <windows.h>

#include "third/parseAndAnalysis.h"

// 当需要运行测试时包含测试头文件
#ifdef RUN_TESTS
#include "tests/parseandanalysis_test.h"
#include <QTest>
#endif

// 包含DLL加载器单例类
#ifdef PARSE_ANALYSIS_DLL_LOAD
#include "parseandanalysis/dll_parseandanalysis.h"
#endif

int main(int argc, char *argv[])
{
    SetConsoleOutputCP(CP_UTF8);
    system("chcp 65001");
    QApplication a(argc, argv);
    
    // 设置应用程序信息
    QApplication::setApplicationName("MicroserviceMonitor");
    QApplication::setOrganizationName("jinglingyun");
    QApplication::setOrganizationDomain("jinglingyun.com");
    
    // 解析命令行参数
    QCommandLineParser parser;
    parser.setApplicationDescription("Microservice Monitor");
    parser.addHelpOption();
    
    // 添加测试模式选项
    QCommandLineOption testOption(QStringList() << "t" << "test", "Run tests instead of normal application.");
    parser.addOption(testOption);

    parser.process(a);

    // 检查是否为测试模式
    if (parser.isSet(testOption)) {
#ifdef RUN_TESTS
        qDebug() << "Running ParseAndAnalysis tests...";

        char* testArgv[1];
        testArgv[0] = argv[0];
        
        ParseAndAnalysisTest test;
        return QTest::qExec(&test, 1, testArgv);
#else
        qWarning() << "Tests were requested but application was not compiled with RUN_TESTS defined.";
        return 1;
#endif
    }
    
    // 创建应用程序并处理登录/登出循环
    int exitCode = 0;
    bool shouldRelogin = false;
    
    do {
        // 创建并显示登录窗口
        LoginWindow loginWindow;
        loginWindow.show();
        
        // 等待登录窗口处理完成
        // 如果是首次登录，这里会等待直到登录成功并进入主窗口
        // 如果是登出后重新登录，同样会等待直到用户重新登录
        exitCode = a.exec();
        
        // 检查退出代码
        // exitCode == 0: 正常退出应用程序
        // exitCode == 1: 用户登出，需要重新登录
        shouldRelogin = (exitCode == 1);
        
        if (shouldRelogin) {
            qDebug() << "用户已登出，准备重新显示登录窗口";
        }
        else {
            qDebug() << "应用程序正常退出，退出代码:" << exitCode;
        }      
    } while (shouldRelogin); // 如果需要重新登录，则继续循环
     
    return exitCode;
} 
