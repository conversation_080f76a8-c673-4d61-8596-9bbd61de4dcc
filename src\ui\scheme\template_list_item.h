#ifndef TEMPLATELISTITEM_H
#define TEMPLATELISTITEM_H

#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDateTime>
#include "utils/user_session.h"
#include "utils/api_url_manager.h"

namespace Ui {
class TemplateListItem;
}

class TemplateListItem : public QWidget
{
    Q_OBJECT

public:
    explicit TemplateListItem(QWidget *parent = nullptr);
    void setTemplateInfo(const QString &name, const QString &userName,
                        const QString &createTime, int id, bool isCurrentUser,
                        const QString &url = QString());

    int getId() const { return m_templateId; }
    QString getTemplateName() const { return m_name; }
    QString getUrl() const { return m_url; }
    void setIsGraph(bool isGraph) { m_isGraph= isGraph; };

signals:
    void downloadClicked(int id);
    void deleteClicked(int id);

private slots:
    void onDownloadClicked();
    void onDeleteClicked();
    void onNetworkReply(QNetworkReply *reply);
    void uploadToServer(const QString &content, const QString &fileName);

private:
    Ui::TemplateListItem *ui;

    QLabel *m_nameLabel;
    QLabel *m_userNameLabel;
    QLabel *m_createTimeLabel;
    QPushButton *m_downloadBtn;
    QPushButton *m_deleteBtn;
    int m_templateId;
    bool m_isCurrentUser;
    QString m_name;
    QString m_url;
    QNetworkAccessManager *m_networkManager;
    //判断是数值监控还是图形监控
    bool m_isGraph;

    void setupUI();
    void setupConnections();
    void unShared();
};

#endif // TEMPLATELISTITEM_H
