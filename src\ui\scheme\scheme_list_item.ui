<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SchemeListItem</class>
 <widget class="QWidget" name="SchemeListItem">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>270</width>
    <height>53</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <item>
    <widget class="QLabel" name="iconLabel">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>36</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>30</width>
       <height>40</height>
      </size>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="SchemeNameLabel">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>36</height>
      </size>
     </property>
     <property name="text">
      <string>TextLabel</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="editBtn">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>24</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>30</width>
       <height>30</height>
      </size>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="iconSize">
      <size>
       <width>24</width>
       <height>24</height>
      </size>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="shareBtn">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>24</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>30</width>
       <height>30</height>
      </size>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="iconSize">
      <size>
       <width>24</width>
       <height>24</height>
      </size>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="deleteBtn">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>24</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>30</width>
       <height>30</height>
      </size>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="iconSize">
      <size>
       <width>24</width>
       <height>24</height>
      </size>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
