<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LoginWindow</class>
 <widget class="QMainWindow" name="LoginWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>768</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>微服务系统监控</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item alignment="Qt::AlignHCenter|Qt::AlignVCenter">
     <widget class="QWidget" name="loginCard" native="true">
      <property name="minimumSize">
       <size>
        <width>400</width>
        <height>500</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>400</width>
        <height>500</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QWidget#loginCard {
    background: white;
    border-radius: 10px;
}</string>
      </property>
      <layout class="QVBoxLayout" name="cardLayout">
       <property name="spacing">
        <number>20</number>
       </property>
       <property name="leftMargin">
        <number>30</number>
       </property>
       <property name="topMargin">
        <number>40</number>
       </property>
       <property name="rightMargin">
        <number>30</number>
       </property>
       <property name="bottomMargin">
        <number>40</number>
       </property>
       <item>
        <widget class="QLabel" name="titleLabel">
         <property name="styleSheet">
          <string notr="true">font-size: 24px;
font-weight: bold;</string>
         </property>
         <property name="text">
          <string>微服务系统监控</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="subtitleLabel">
         <property name="styleSheet">
          <string notr="true">font-size: 14px;
color: #666;</string>
         </property>
         <property name="text">
          <string>CABIN MICROSERVICE SYSTEM
MONITORING</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Fixed</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLineEdit" name="usernameEdit">
         <property name="styleSheet">
          <string notr="true">QLineEdit {
    padding: 10px;
    border: none;
    border-bottom: 1px solid #ddd;
    border-radius: 0px;
    font-size: 14px;
    background-color: transparent;
}
QLineEdit:focus {
    border-bottom: 1px solid #1890ff;
}</string>
         </property>
         <property name="placeholderText">
          <string>admin</string>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="passwordLayout">
         <property name="spacing">
          <number>0</number>
         </property>
         <item>
          <widget class="QLineEdit" name="passwordEdit">
           <property name="styleSheet">
            <string notr="true">QLineEdit {
    padding: 10px;
    border: none;
    border-bottom: 1px solid #ddd;
    border-radius: 0px;
    font-size: 14px;
    background-color: transparent;
}
QLineEdit:focus {
    border-bottom: 1px solid #1890ff;
}</string>
           </property>
           <property name="echoMode">
            <enum>QLineEdit::Password</enum>
           </property>
           <property name="placeholderText">
            <string>Admin123</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="togglePasswordButton">
           <property name="minimumSize">
            <size>
             <width>24</width>
             <height>24</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>24</width>
             <height>24</height>
            </size>
           </property>
           <property name="cursor">
            <cursorShape>PointingHandCursor</cursorShape>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton {
    border: none;
    background: transparent;
}
QPushButton:hover {
    background: transparent;
}</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset resource="../../resources/resources.qrc">
             <normaloff>:/images/eye-off.png</normaloff>:/images/eye-off.png</iconset>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QCheckBox" name="rememberPwdCheck">
         <property name="styleSheet">
          <string notr="true">font-size: 14px;
color: #666;</string>
         </property>
         <property name="text">
          <string>记住密码</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="loginButton">
         <property name="styleSheet">
          <string notr="true">QPushButton {
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px;
    font-size: 16px;
}
QPushButton:hover {
    background: #40a9ff;
}</string>
         </property>
         <property name="text">
          <string>登录</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="serverLayout">
         <property name="spacing">
          <number>5</number>
         </property>
         <item>
          <widget class="QLabel" name="serverLink">
           <property name="minimumSize">
            <size>
             <width>120</width>
             <height>30</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>120</width>
             <height>30</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">color: #666;
font-size: 14px;
padding-left: 10px;</string>
           </property>
           <property name="text">
            <string>&lt;img src=&quot;:/images/cloud.png&quot; width=&quot;14&quot; height=&quot;14&quot; style=&quot;vertical-align: -2px;&quot;&gt;&amp;nbsp;服务器地址：</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="serverAddressEdit">
           <property name="minimumSize">
            <size>
             <width>200</width>
             <height>30</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QLineEdit {
    padding: 5px;
    border: none;
    border-bottom: 1px solid #ddd;
    border-radius: 0px;
    font-size: 14px;
    background-color: transparent;
}
QLineEdit:focus {
    border-bottom: 1px solid #1890ff;
}</string>
           </property>
           <property name="placeholderText">
            <string>*************:9001</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources>
  <include location="../../resources/resources.qrc"/>
 </resources>
 <connections/>
</ui>
