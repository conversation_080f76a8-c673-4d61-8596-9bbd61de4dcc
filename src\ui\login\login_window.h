#ifndef LOGIN_WINDOW_H
#define LOGIN_WINDOW_H

#include <QMainWindow>
#include <QNetworkAccessManager>
#include <QNetworkReply>

namespace Ui {
class LoginWindow;
}

class LoginWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit LoginWindow(QWidget *parent = nullptr);
    ~LoginWindow();

private slots:
    void onLoginClicked();
    void onServerAddressChanged(const QString &address);
    void onTogglePasswordVisibility();
    void onLoginResponse(QNetworkReply *reply);

private:
    void setupConnections();
    void updatePasswordVisibilityIcon();
    void paintEvent(QPaintEvent *event) override;
    void drawBackground(QPainter &painter);
    QString getServerAddress() const;
    void showError(const QString &message);

    Ui::LoginWindow *ui;
    bool isPasswordVisible;
    QNetworkAccessManager *networkManager;
};

#endif // LOGIN_WINDOW_H