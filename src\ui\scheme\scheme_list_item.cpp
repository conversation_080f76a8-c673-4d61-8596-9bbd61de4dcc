﻿#include "scheme_list_item.h"
#include "ui_scheme_list_item.h"

#include <QDomDocument>
#include <QFile>
#include <QFileInfo>
#include <QDebug>
#include <QMessageBox>
#include <QJsonObject>
#include <QJsonDocument>

#include "utils/user_session.h"
#include "utils/api_url_manager.h"
#include "utils/file_list_service.h"
#include "third/parseAndAnalysis.h"
#include "../FcMonitor/fc_monitor_widget.h"

SchemeListItem::SchemeListItem(QString name,QWidget *parent) :
    QWidget(parent),
    m_isShared(false),
    m_isDelPressed(false),
    ui(new Ui::SchemeListItem)
{
    ui->setupUi(this);
    ui->SchemeNameLabel->setText(name);

    // 初始化网络管理器
    m_networkManager = new QNetworkAccessManager(this);
    m_shareWorkManager = new QNetworkAccessManager(this);
    connect(m_networkManager, &QNetworkAccessManager::finished, this, &SchemeListItem::onUploadFinished);
    connect(m_shareWorkManager, &QNetworkAccessManager::finished, this, &SchemeListItem::onShareUploadFinished);

    initUiStyle();

    m_schemeEditDialog = new SchemeEditDialog(ui->SchemeNameLabel->text(),this);
//    m_schemeEditDialog->hide();
    connect(m_schemeEditDialog,&SchemeEditDialog::editSchemefinish,this,&SchemeListItem::editFinishedSlot);
}

SchemeListItem::~SchemeListItem()
{
    delete ui;
}

void SchemeListItem::initUiStyle()
{
    //设置item图标
    QPixmap pixmap(":/images/folder.png");
    ui->iconLabel->setPixmap(pixmap.scaled(16, 16, Qt::KeepAspectRatio, Qt::SmoothTransformation));
    ui->iconLabel->setStyleSheet("QLabel { "
                                       "    background-color: white; "
                                       "    padding-left: 0px; "
                                       "}");

    // 设置按钮图标和样式
    ui->editBtn->setIcon(QIcon(":/images/edit.png"));
    ui->deleteBtn->setIcon(QIcon(":/images/delete-trash.png"));
    ui->shareBtn->setIcon(QIcon(":/images/share.png"));

    // 设置按钮样式 - 白色背景
    // 添加顶部边距以对齐
    QString buttonStyle = "QPushButton { "
                          "    border: none; "
                          "    padding: 2px; "
                          "    background-color: white !important;"
                          "}"
                          "QPushButton:hover { "
                          "    background-color: #e0e0e0; "
                          "    border-radius: 2px; "
                          "}";
    ui->editBtn->setStyleSheet(buttonStyle);
    ui->deleteBtn->setStyleSheet(buttonStyle);
    ui->shareBtn->setStyleSheet(buttonStyle);

    // 设置按钮大小
    QSize buttonSize(24, 24);
    ui->editBtn->setFixedSize(buttonSize);
    ui->deleteBtn->setFixedSize(buttonSize);
    ui->shareBtn->setFixedSize(buttonSize);

    // 设置SchemeNameLabel样式 - 白色背景和左对齐
    ui->SchemeNameLabel->setStyleSheet("QLabel { "
                                       "    background-color: white; "
                                       "    padding-left: 5px; "
                                       "}");
    ui->SchemeNameLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
}

void SchemeListItem::on_deleteBtn_clicked()
{
    m_isDelPressed = true;
    QString schemeName = ui->SchemeNameLabel->text();
    QString userName = UserSession::getInstance().getUsername();
    QString filePath = QCoreApplication::applicationDirPath() + "/scheme/" + userName + "_solution.xml";
    //获取ICD VERSION
    m_decoderToken = FileListService::getInstance().getParseandanalysisP();
    QString icdVersion = QString::fromStdString(getCurrentIcdVersion(m_decoderToken));

    // 打开XML文件
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "无法打开XML文件:" << filePath << ", 错误:" << file.errorString();
        return ;
    }

    // 读取XML内容
    QDomDocument doc;
    QString errorMsg;
    int errorLine, errorColumn;
    if (!doc.setContent(&file, &errorMsg, &errorLine, &errorColumn)) {
        qWarning() << "XML解析错误:" << errorMsg << "at line" << errorLine << "column" << errorColumn;
        file.close();
        return ;
    }
    file.close();

    // 获取根元素
    QDomElement root = doc.documentElement();
    if (root.isNull()) {
        qWarning() << "XML文件没有根元素";
        return ;
    }

    // 查找要删除的方案元素
    QDomNodeList solutions = root.elementsByTagName("solution");
    bool found = false;
    for (int i = 0; i < solutions.count(); ++i) {
        QDomElement solution = solutions.at(i).toElement();
        if (solution.attribute("name") == schemeName && solution.attribute("ICD_name") == icdVersion) {
            // 删除该方案元素
            root.removeChild(solutions.at(i));
            found = true;
            break;
        }
    }

    if (!found) {
        qWarning() << "未找到方案:" << schemeName;
        return ;
    }

    // 重新打开文件用于写入
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "无法打开XML文件进行写入:" << filePath << ", 错误:" << file.errorString();
        return ;
    }

    // 创建文本流
    QTextStream out(&file);
    out.setCodec("UTF-8");  // 设置编码为UTF-8

    // 写入XML内容
    doc.save(out, 4);  // 4是缩进空格数
    QString content = doc.toString(4);
    file.close();

    qDebug() << "成功从XML文件中删除方案:" << schemeName;

    QString fileName = QFileInfo(filePath).fileName();
    uploadToServer(content,fileName);

    return ;
}

void SchemeListItem::uploadToServer(const QString &content, const QString &fileName)
{
    // 获取用户会话数据
    QString token = UserSession::getInstance().getToken();

    // 准备请求
    QNetworkRequest request(ApiUrlManager::getInstance().getUrl(ApiUrlManager::SCHEME_UPDATE));
    request.setRawHeader("accept", "*/*");
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    if (!token.isEmpty()) {
        request.setRawHeader("Authorization", token.toUtf8());
    }

    // 创建 JSON 数据
    QJsonObject jsonObj;
    jsonObj["content"] = content;
    jsonObj["id"] = 0;  // 按接口要求设置为0
    jsonObj["name"] = fileName;
    jsonObj["type"] = "solutions";
    // 添加用户ID（如果可用）
    jsonObj["userId"] =  UserSession::getInstance().getUserId();

    QJsonDocument jsonDoc(jsonObj);
    QByteArray jsonData = jsonDoc.toJson();

    // 发送 POST 请求
    m_networkManager->post(request, jsonData);
}

void SchemeListItem::onUploadFinished(QNetworkReply *reply)
{
    // 处理响应
    if (reply->error() == QNetworkReply::NoError) {
        // 读取响应数据
        QByteArray responseData = reply->readAll();

        // 尝试解析 JSON 响应
        QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData);
        if (!jsonDoc.isNull() && jsonDoc.isObject()) {
            QJsonObject responseObj = jsonDoc.object();

            // 获取顶层字段
            QString code = responseObj["code"].toString();
            QString message = responseObj["message"].toString();
            bool success = responseObj["success"].toBool();

            if (success) {
                // 获取 data 对象
                QJsonObject dataObj = responseObj["data"].toObject();
                if (!dataObj.isEmpty()) {
                    // 提取 data 中的字段
                    QString name = dataObj["name"].toString();
                    QString type = dataObj["type"].toString();
                    QString url = dataObj["url"].toString();
                    QString createTime = dataObj["createTime"].toString();
                    QString updateTime = dataObj["updateTime"].toString();
                    int id = dataObj["id"].toInt();

                    // 构建成功消息
                    QString successMsg = tr("文件已成功上传到服务器\n\n");
                    successMsg += tr("名称: %1\n").arg(name);
                    successMsg += tr("类型: %1\n").arg(type);

                    successMsg += tr("ID: %1\n").arg(id);
                    successMsg += tr("创建时间: %1\n").arg(createTime);
                    successMsg += tr("更新时间: %1\n").arg(updateTime);

                    if (!url.isEmpty())
                        successMsg += tr("URL: %1").arg(url);

                    //QMessageBox::information(this, tr("上传成功"), successMsg);
                    if(m_isDelPressed)
                    {
                        m_isDelPressed = false;
                        emit deleteFinished();
                    }
                } else {
                    QMessageBox::information(this, tr("上传成功"),
                                            tr("文件已成功上传，但服务器返回的数据为空"));
                }
            } else {
                // 显示错误信息
                QString errorMsg = tr("上传失败: %1").arg(message);
                QMessageBox::warning(this, tr("上传失败"), errorMsg);
            }
        } else {
            QMessageBox::information(this, tr("上传成功"),
                                    tr("文件已上传，但服务器响应不是有效的JSON格式"));
        }
    } else {
        // 显示网络错误信息
        QMessageBox::warning(this, tr("上传失败"),
                            tr("无法上传文件: %1").arg(reply->errorString()));
    }

    // 释放响应对象
    reply->deleteLater();
}

QString SchemeListItem::getSchemeContent(const QString &schemeName)
{
    //获取ICD VERSION
    m_decoderToken = FileListService::getInstance().getParseandanalysisP();
    QString icdVersion = QString::fromStdString(getCurrentIcdVersion(m_decoderToken));

    QString userName = UserSession::getInstance().getUsername();
    QString filePath = QCoreApplication::applicationDirPath() + "/scheme/" + userName + "_solution.xml";
    QFile file(filePath);

    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QMessageBox::warning(this, tr("错误"), tr("无法打开方案文件"));
        return QString();
    }

    QDomDocument doc;
    QString errorMsg;
    int errorLine, errorColumn;
    if (!doc.setContent(&file, &errorMsg, &errorLine, &errorColumn)) {
        file.close();
        QMessageBox::warning(this, tr("错误"),
            tr("XML解析错误：%1\n行：%2，列：%3").arg(errorMsg).arg(errorLine).arg(errorColumn));
        return QString();
    }
    file.close();

    // 查找指定名称的方案
    QDomElement root = doc.documentElement();
    QDomNodeList solutions = root.elementsByTagName("solution");

    for (int i = 0; i < solutions.count(); i++) {
        QDomElement solution = solutions.at(i).toElement();
        if (solution.attribute("name") == schemeName && solution.attribute("ICD_name") == icdVersion) {
            // 创建新的文档只包含这个方案
            QDomDocument newDoc;
            QDomElement newRoot = newDoc.createElement("root");
            newDoc.appendChild(newRoot);
            newRoot.appendChild(solution.cloneNode(true));
            return newDoc.toString(4);
        }
    }

    return QString();
}

void SchemeListItem::uploadShareToServer(const QString &content, const QString &schemeName,
                                       const QString &username, const QString &icdVersion)
{
    QString token = UserSession::getInstance().getToken();

    QNetworkRequest request;
    if(m_isShared)
    {
        request = QNetworkRequest(ApiUrlManager::getInstance().getUrl(ApiUrlManager::SCHEME_SHARE));
    }
    else {
        request = QNetworkRequest(ApiUrlManager::getInstance().getUrl(ApiUrlManager::SCHEME_UNSHARE));
    }
    request.setRawHeader("accept", "*/*");
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    if (!token.isEmpty()) {
        request.setRawHeader("Authorization", token.toUtf8());
    }

    QJsonObject jsonObj;
    jsonObj["content"] = content;
    jsonObj["icdVersion"] = icdVersion;
     jsonObj["id"] = 0;
    jsonObj["name"] = schemeName;
    jsonObj["type"] = 1;
    jsonObj["userId"] = UserSession::getInstance().getUserId();
    jsonObj["userName"] = username;

    QJsonDocument jsonDoc(jsonObj);
    QByteArray jsonData = jsonDoc.toJson();

    QNetworkReply *reply;

    if(m_isShared)
    {
        reply = m_shareWorkManager->post(request, jsonData);
    }
    else
    {
        reply = m_shareWorkManager->sendCustomRequest(request, "DELETE", jsonData);
    }
}

void SchemeListItem::onShareUploadFinished(QNetworkReply *reply)
{
    if (reply->error() == QNetworkReply::NoError) {
        QByteArray responseData = reply->readAll();
        QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData);

        if (!jsonDoc.isNull() && jsonDoc.isObject()) {
            QJsonObject responseObj = jsonDoc.object();
            bool success = responseObj["success"].toBool();
            QString message = responseObj["message"].toString();

            if (success) {
//                QMessageBox::information(this, tr("分享成功"),
//                    tr("方案已成功分享到服务器"));
            } else {
                QMessageBox::warning(this, tr("方案分享状态同步到服务器失败"),
                    tr("方案分享状态同步到服务器失败: %1").arg(message));
                // 如果分享失败，恢复按钮状态
                m_isShared = !m_isShared;
                ui->shareBtn->setIcon(QIcon(m_isShared ? ":/images/share-blue.png" : ":/images/share.png"));
            }
        }
    } else {
        QMessageBox::warning(this, tr("分享失败"),
            tr("网络错误: %1").arg(reply->errorString()));
        // 如果网络错误，恢复按钮状态
        m_isShared = !m_isShared;
        ui->shareBtn->setIcon(QIcon(m_isShared ? ":/images/share-blue.png" : ":/images/share.png"));
    }

    reply->deleteLater();
}

void SchemeListItem::on_shareBtn_clicked()
{
    /*
    QString schemeName = ui->SchemeNameLabel->text();
    QString content = getSchemeContent(schemeName);

    if (content.isEmpty()) {
        QMessageBox::warning(this, tr("错误"), tr("无法获取方案内容"));
        return;
    }

    // 切换分享状态
    m_isShared = !m_isShared;

    // 更新按钮图标
    ui->shareBtn->setIcon(QIcon(m_isShared ? ":/images/share-blue.png" : ":/images/share.png"));


    // 获取用户名和ICD版本
    QString username = UserSession::getInstance().getUsername();
    m_decoderToken = FileListService::getInstance().getParseandanalysisP();
    // 从XML中获取ICD版本
    QString icdVersion = QString::fromStdString(getCurrentIcdVersion(m_decoderToken));

    // 上传到服务器
    uploadShareToServer(content, schemeName, username, icdVersion);*/

    QString schemeName = ui->SchemeNameLabel->text();
    QString userName = UserSession::getInstance().getUsername();
    QString filePath = QCoreApplication::applicationDirPath() + "/scheme/" + userName + "_solution.xml";

    // 获取ICD版本
    m_decoderToken = FileListService::getInstance().getParseandanalysisP();
    QString icdVersion = QString::fromStdString(getCurrentIcdVersion(m_decoderToken));

    // 打开XML文件
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QMessageBox::warning(this, tr("错误"), tr("无法打开方案文件"));
        return;
    }

    // 读取XML内容
    QDomDocument doc;
    QString errorMsg;
    int errorLine, errorColumn;
    if (!doc.setContent(&file, &errorMsg, &errorLine, &errorColumn)) {
        file.close();
        QMessageBox::warning(this, tr("错误"),
                             tr("XML解析错误：%1\n行：%2，列：%3").arg(errorMsg).arg(errorLine).arg(errorColumn));
        return;
    }
    file.close();

    // 查找指定名称的方案
    QDomElement root = doc.documentElement();
    QDomNodeList solutions = root.elementsByTagName("solution");
    bool found = false;
    QDomElement targetSolution;

    for (int i = 0; i < solutions.count(); i++) {
        QDomElement solution = solutions.at(i).toElement();
        if (solution.attribute("name") == schemeName && solution.attribute("ICD_name") == icdVersion) {
            // 切换分享状态
            m_isShared = !m_isShared;
            solution.setAttribute("isShared", m_isShared ? "1" : "0");
            targetSolution = solution;
            found = true;
            break;
        }
    }

    if (!found) {
        QMessageBox::warning(this, tr("错误"), tr("未找到方案：%1").arg(schemeName));
        return;
    }

    // 重新打开文件用于写入
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::warning(this, tr("错误"), tr("无法打开文件进行写入"));
        return;
    }

    // 创建文本流
    QTextStream out(&file);
    out.setCodec("UTF-8");  // 设置编码为UTF-8

    // 写入XML内容
    doc.save(out, 4);  // 4是缩进空格数
    QString content = doc.toString(4);
    file.close();

    // 更新按钮图标
    ui->shareBtn->setIcon(QIcon(m_isShared ? ":/images/share-blue.png" : ":/images/share.png"));

    // 上传整个文件到服务器
    QString fileName = QFileInfo(filePath).fileName();
    uploadToServer(content, fileName);

    // 创建新的文档只包含这个方案用于分享
    QDomDocument shareDoc;
    QDomElement shareRoot = shareDoc.createElement("root");
    shareDoc.appendChild(shareRoot);
    shareRoot.appendChild(targetSolution.cloneNode(true));
    QString shareContent = shareDoc.toString(4);

    // 上传分享内容到服务器
    uploadShareToServer(shareContent, schemeName, userName, icdVersion);

}

QString SchemeListItem::getSchemeName()
{
    return ui->SchemeNameLabel->text();
}

void SchemeListItem::on_editBtn_clicked()
{

    m_schemeEditDialog->loadSchemeData(ui->SchemeNameLabel->text());
    m_schemeEditDialog->showMaximized();
    m_schemeEditDialog->show();

}

void SchemeListItem::editFinishedSlot(int ret)
{
    m_schemeEditDialog->hide();
}

void SchemeListItem::setShareStatus(bool isShared)
{
    m_isShared = isShared;
    // 更新分享按钮图标
    ui->shareBtn->setIcon(QIcon(isShared ? ":/images/share-blue.png" : ":/images/share.png"));
}
