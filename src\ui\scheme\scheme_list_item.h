﻿#ifndef SCHEME_LIST_ITEM_H
#define SCHEME_LIST_ITEM_H

#include <QWidget>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include "scheme_edit_dialog.h"

namespace Ui {
class SchemeListItem;
}

class SchemeListItem : public QWidget
{
    Q_OBJECT

public:
    explicit SchemeListItem(QString name, QWidget *parent = nullptr);
    void initUiStyle();
    ~SchemeListItem();
    QString getSchemeName();
    void setShareStatus(bool isShared);

private:
    void uploadToServer(const QString &content, const QString &fileName);
    void uploadShareToServer(const QString &content, const QString &schemeName, const QString &username, const QString &icdVersion);
    QString getSchemeContent(const QString &schemeName);

private slots:
    void on_deleteBtn_clicked();
    void on_shareBtn_clicked();

    void onUploadFinished(QNetworkReply *reply);
    void onShareUploadFinished(QNetworkReply *reply);

    void on_editBtn_clicked();
    void editFinishedSlot(int ret);

signals:
    //点击删除后发送此信号，主函数接收到此信号删除该item
    void deleteFinished();
    void editFinished();

public:
    SchemeEditDialog *m_schemeEditDialog;

private:
    Ui::SchemeListItem *ui;
    // 网络请求管理器
    QNetworkAccessManager *m_networkManager;
    QNetworkAccessManager *m_shareWorkManager;
    // 添加分享状态跟踪
    bool m_isShared = false;
    void* m_decoderToken = nullptr;
    //SchemeEditDialog *m_schemeEditDialog;
    bool m_isDelPressed;

};

#endif // SCHEME_LIST_ITEM_H
