#ifndef MONITOR_WINDOW_H
#define MONITOR_WINDOW_H

#include <QWidget>
#include <QRadioButton>
#include <QPushButton>
#include <QLabel>
#include <QDateTime>
#include <QVBoxLayout>
#include <QMap>
#include <functional>

namespace Ui {
class MonitorWindow;
}

class MonitorWindow : public QWidget
{
    Q_OBJECT

public:
    explicit MonitorWindow(QWidget *parent = nullptr);
    ~MonitorWindow();

    enum MonitorType {
        VALUE_MONITOR,
        GRAPH_MONITOR,
        UNIT_CONFIG
    };

signals:
    void realtimeButtonClicked();
    void replayButtonClicked(const QDateTime& startTime, const QDateTime& endTime);

public slots:
    void setMonitorType(MonitorType type);
    void updateFileList(const QStringList& fileList);

private slots:
    void onFCRadioToggled(bool checked);
    void onProtocolRadioToggled(bool checked);
    void onFCSettingRadioToggled(bool checked);
    void onFunctionDisplayRadioToggled(bool checked);
    void onUploadFCButtonClicked();
    void onUploadICDButtonClicked();
    void onRightToolbarUploadClicked();
    void onRealTimeButtonClicked();
    void onReplayButtonClicked();
    void updateDateTime();
    void onFileRadioToggled(bool checked);
    void showDateTimePickerDialog();
    void onFileListItemClicked();

private:
    void setupUI();
    void setupConnections();
    void updateUI();
    void handleSelectedTimeRange(const QDateTime& startTime, const QDateTime& endTime);
    void loadFileList();
    void clearFileList();
    QRadioButton* createFileRadioButton(const QString& fileName);
    
    // 下载并保存文件的函数，添加一个回调函数参数
    void downloadAndSaveFile(const QString& fileName, const QString& filePath, 
                           std::function<void(bool success)> callback = nullptr);

    Ui::MonitorWindow *ui;
    MonitorType currentType;
    
    QRadioButton *fcRadio;
    QRadioButton *protocolRadio;
    QRadioButton *fcSettingRadio;
    QRadioButton *functionDisplayRadio;
    QRadioButton *currRadio;
    
    QPushButton *uploadFCButton;
    QPushButton *uploadICDButton;
    
    QLabel *titleLabel;
    QLabel *dateTimeLabel;
    
    QDateTime replayStartTime;
    QDateTime replayEndTime;
    
    // 存储文件对应的单选按钮
    QMap<QString, QRadioButton*> fileRadioButtons;
    // 垂直布局，用于动态添加文件单选按钮
    QVBoxLayout* fileListLayout;
};

#endif // MONITOR_WINDOW_H
