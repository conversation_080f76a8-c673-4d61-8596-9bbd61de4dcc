#include "monitor_window.h"
#include "ui_monitorwindow.h"
#include "ui/icd/icd_view_dialog.h"
#include "ui/common/datetime_picker_dialog.h"
#include "utils/file_list_service.h"
#include "utils/user_session.h"
// #include "utils/api_url_manager.h"

#include <QDateTime>
#include <QTimer>
#include <QThread>
#include <QMessageBox>
#include <QFileDialog>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonDocument>
#include <QJsonObject>
#include <QCoreApplication>
#include <QDir>
#include <QFile>
#include <QTextStream>
#include <QDebug>
#include <QVBoxLayout>
#include <QStandardPaths>
#include <QNetworkRequest>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonDocument>
#include <QJsonObject>
#include <QCoreApplication>
#include <functional>

MonitorWindow::MonitorWindow(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::MonitorWindow)
    , currentType(VALUE_MONITOR)
    , currRadio(nullptr)
{
    ui->setupUi(this);
    setupUI();
    setupConnections();
    updateUI();

    // 创建定时器，每秒更新一次时间
    QTimer *timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, &MonitorWindow::updateDateTime);
    timer->start(1000);

    // 获取文件列表
    loadFileList();
}

MonitorWindow::~MonitorWindow()
{
    delete ui;
}

void MonitorWindow::setupUI()
{
    // 获取UI中的控件
    fcRadio = ui->fcRadio;
    protocolRadio = ui->protocolRadio;
    fcSettingRadio = ui->fcSettingRadio;
    functionDisplayRadio = ui->functionDisplayRadio;

    uploadFCButton = ui->uploadFCButton;
    uploadICDButton = ui->uploadICDButton;

    titleLabel = ui->titleLabel;
    dateTimeLabel = ui->dateTimeLabel;

    // 设置当前日期时间
    updateDateTime();

    // 初始化文件列表布局
    QWidget* scrollAreaContent = ui->scrollAreaWidgetContents;

    // 清空现有布局
    if (scrollAreaContent->layout()) {
        QLayoutItem* item;
        while ((item = scrollAreaContent->layout()->takeAt(0)) != nullptr) {
            if (item->widget()) {
                delete item->widget();
            }
            delete item;
        }
        delete scrollAreaContent->layout();
    }

    // 创建新的垂直布局
    fileListLayout = new QVBoxLayout(scrollAreaContent);
    fileListLayout->setContentsMargins(5, 5, 5, 5);
    fileListLayout->setSpacing(0);

    // 添加垂直弹簧
    fileListLayout->addStretch();

    // 隐藏状态栏，直到有文件被选中
    ui->statusBar->setVisible(false);
}

void MonitorWindow::setupConnections()
{
    connect(fcRadio, &QRadioButton::toggled, this, &MonitorWindow::onFCRadioToggled);
    connect(protocolRadio, &QRadioButton::toggled, this, &MonitorWindow::onProtocolRadioToggled);
    connect(fcSettingRadio, &QRadioButton::toggled, this, &MonitorWindow::onFCSettingRadioToggled);
    connect(functionDisplayRadio, &QRadioButton::toggled, this, &MonitorWindow::onFunctionDisplayRadioToggled);

    connect(uploadFCButton, &QPushButton::clicked, this, &MonitorWindow::onUploadFCButtonClicked);
    connect(uploadICDButton, &QPushButton::clicked, this, &MonitorWindow::onUploadICDButtonClicked);

    // 底部按钮
    // connect(ui->realtimeBtn, &QPushButton::clicked, this, &MonitorWindow::onRealTimeButtonClicked);
    connect(ui->realtimeBtn, &QPushButton::clicked, this, [this]() { emit realtimeButtonClicked();});
    connect(ui->replayBtn, &QPushButton::clicked, this, &MonitorWindow::onReplayButtonClicked);

    // 连接FileListService的默认选中ICD文件变化信号
    connect(&FileListService::getInstance(), &FileListService::defaultSelectedIcdFileChanged,
            this, [this](const QString& fileName) {
                // 当默认选中的ICD文件发生变化时，更新UI中的选中状态
                if (fileRadioButtons.contains(fileName)) {
                    QRadioButton* radio = fileRadioButtons[fileName];
                    if (radio && !radio->isChecked()) {
                        radio->setChecked(true);
                    }
                }
            });
}

void MonitorWindow::setMonitorType(MonitorType type)
{
    currentType = type;

    // 根据监控类型更新UI
    switch (type) {
    case VALUE_MONITOR:
        titleLabel->setText("控制台：");
        ui->consoleNumberLabel->setText("一号台 ");
        break;
    case GRAPH_MONITOR:
        titleLabel->setText("控制台：");
        ui->consoleNumberLabel->setText("一号台 ");
        break;
    case UNIT_CONFIG:
        titleLabel->setText("控制台：");
        ui->consoleNumberLabel->setText("一号台 ");
        break;
    }

    // 检查并更新文件列表
    loadFileList();
}

void MonitorWindow::updateUI()
{
    // 根据选中的单选按钮更新UI显示区域
    if (fcRadio->isChecked()) {
        ui->displayArea->setStyleSheet("background-color: #f5f5f5; border: 1px solid #e0e0e0;");
        // 可以在这里添加显示FC网络图的代码
    } else if (protocolRadio->isChecked()) {
        ui->displayArea->setStyleSheet("background-color: #f5f5f5; border: 1px solid #e0e0e0;");
        // 可以在这里添加显示1394网络配分的代码
    } else if (fcSettingRadio->isChecked()) {
        ui->displayArea->setStyleSheet("background-color: #f5f5f5; border: 1px solid #e0e0e0;");
        // 可以在这里添加显示FC设置状态的代码
    } else if (functionDisplayRadio->isChecked()) {
        ui->displayArea->setStyleSheet("background-color: #f5f5f5; border: 1px solid #e0e0e0;");
        // 可以在这里添加显示功能显示状态的代码
    }
}

void MonitorWindow::updateDateTime()
{
    dateTimeLabel->setText(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
}

void MonitorWindow::onFCRadioToggled(bool checked)
{
    if (checked) {
        updateUI();
    }
}

void MonitorWindow::onProtocolRadioToggled(bool checked)
{
    if (checked) {
        updateUI();
    }
}

void MonitorWindow::onFCSettingRadioToggled(bool checked)
{
    if (checked) {
        updateUI();
    }
}

void MonitorWindow::onFunctionDisplayRadioToggled(bool checked)
{
    if (checked) {
        updateUI();
    }
}

void MonitorWindow::onFileListItemClicked()
{
    QRadioButton* radio = qobject_cast<QRadioButton*>(sender());
    if (!radio) {
        return;
    }

    QString fileName = radio->text();

    // 保存当前选中的单选按钮，以便在失败时恢复
    QRadioButton* currentCheckedRadio = currRadio;
    // QString fileName1 = currentCheckedRadio->text();

    // 检查本地文件是否存在
    QString icdFilesDir =  QCoreApplication::applicationDirPath() + "/icd_files";
    QString filePath = icdFilesDir + "/" + fileName;

    QDir dir(icdFilesDir);
    if (!dir.exists()) {
        dir.mkpath(".");
    }

    QFile file(filePath);
    if (file.exists()) {
        // 文件存在，允许选中
        // 保存默认选中的ICD文件
        int ret = FileListService::getInstance().createParseandanalysisP(fileName);
        if(ret == 0) {
            FileListService::getInstance().setDefaultSelectedIcdFile(fileName);
        }
        else {
            currentCheckedRadio->setChecked(true);
        }
    }
    else {
        qDebug() << "文件不存在，尝试下载:" << fileName;

        // 使用 lambda 回调处理下载结果
        downloadAndSaveFile(fileName, filePath, [this, radio, currentCheckedRadio, fileName](bool success) {
            if (!success) {
                // 下载失败，恢复之前的选中状态
                if (currentCheckedRadio) {
                    // 断开信号连接，避免触发事件循环
                    disconnect(radio, &QRadioButton::clicked, this, &MonitorWindow::onFileListItemClicked);
                    radio->setChecked(false);

                    disconnect(currentCheckedRadio, &QRadioButton::clicked, this, &MonitorWindow::onFileListItemClicked);
                    currentCheckedRadio->setChecked(true);

                    // 重新连接信号
                    connect(radio, &QRadioButton::clicked, this, &MonitorWindow::onFileListItemClicked);
                    connect(currentCheckedRadio, &QRadioButton::clicked, this, &MonitorWindow::onFileListItemClicked);

                    QMessageBox::warning(this, tr("文件访问失败"),
                                         tr("无法访问文件 %1: 文件不存在且无法下载").arg(fileName));
                }
                else {
                    // 没有之前选中的按钮，只需取消当前选择
                    disconnect(radio, &QRadioButton::clicked, this, &MonitorWindow::onFileListItemClicked);
                    radio->setChecked(false);
                    connect(radio, &QRadioButton::clicked, this, &MonitorWindow::onFileListItemClicked);

                    QMessageBox::warning(this, tr("文件访问失败"),
                                         tr("无法访问文件 %1: 文件不存在且无法下载").arg(fileName));
                }
            }
            else {
                // 保存默认选中的ICD文件
                int ret = FileListService::getInstance().createParseandanalysisP(fileName);
                if(ret == 0) {
                    FileListService::getInstance().setDefaultSelectedIcdFile(fileName);
                }
                else {
                    currentCheckedRadio->setChecked(true);
                }
            }
        });
    }
}

// 下载并保存文件的函数
void MonitorWindow::downloadAndSaveFile(const QString& fileName, const QString& filePath,
                                        std::function<void(bool success)> callback)
{
    // 显示下载中的提示
    // QMessageBox msgBox;
    // msgBox.setWindowTitle(tr("正在下载"));
    // msgBox.setText(tr("正在从服务器下载文件 %1...").arg(fileName));
    // msgBox.setStandardButtons(QMessageBox::NoButton);
    // msgBox.setIcon(QMessageBox::Information);

    // // 创建异步消息框，允许继续执行后续代码
    // QTimer::singleShot(100, &msgBox, &QMessageBox::close);
    // msgBox.show();

    // 从FileListService获取文件项信息
    FileItem fileItem = FileListService::getInstance().getFileItemByName(fileName);
    if (fileItem.id == 0 || fileItem.url.isEmpty()) {
        qWarning() << "无法获取文件信息或URL为空:" << fileName;
        QMessageBox::warning(this, tr("下载失败"),
                             tr("无法获取文件 %1 的下载信息").arg(fileName));
        if (callback) {
            callback(false);
        }
        return;
    }

    qDebug() << "获取到文件信息:" << fileName << "ID:" << fileItem.id << "URL:" << fileItem.url;

    // 直接使用FileItem中的URL进行下载
    QUrl url(fileItem.url);
    if (!url.isValid()) {
        qWarning() << "文件URL无效:" << fileItem.url;
        QMessageBox::warning(this, tr("下载失败"),
                             tr("文件 %1 的URL无效").arg(fileName));
        if (callback) {
            callback(false);
        }
        return;
    }

    // 准备网络请求
    QNetworkRequest request(url);
    request.setRawHeader("accept", "*/*");

    // 获取用户会话数据
    // QString token = UserSession::getInstance().getToken();
    // if (!token.isEmpty()) {
    //     request.setRawHeader("Authorization", token.toUtf8());
    // }

    // 创建网络管理器
    QNetworkAccessManager* networkManager = new QNetworkAccessManager(this);

    // 发送GET请求并处理响应
    QNetworkReply* reply = networkManager->get(request);

    // 使用Lambda表达式处理响应
    connect(reply, &QNetworkReply::finished, [this, reply, fileName, filePath, networkManager, callback]() {
        bool success = false;

        // 请求完成后，处理响应
        if (reply->error() == QNetworkReply::NoError) {
            // 读取响应数据
            QByteArray responseData = reply->readAll();

            // 直接保存响应内容到文件
            QFile file(filePath);
            if (file.open(QIODevice::WriteOnly)) {
                file.write(responseData);
                file.close();

                qDebug() << "文件已已成功下载并保存到本地:" << filePath;
//                QMessageBox::information(this, tr("下载成功"),
//                                         tr("文件 %1 已成功下载并保存到本地").arg(fileName));

                success = true;
            } else {
                qWarning() << "无法保存文件:" << file.errorString();
                QMessageBox::warning(this, tr("保存失败"),
                                     tr("无法保存文件 %1: %2").arg(fileName).arg(file.errorString()));
            }
        } else {
            qWarning() << "网络请求错误:" << reply->errorString();
            QMessageBox::warning(this, tr("下载失败"),
                                 tr("网络请求错误: %1").arg(reply->errorString()));
        }

        // 执行回调函数，返回下载结果
        if (callback) {
            callback(success);
        }

        // 清理资源
        reply->deleteLater();
        networkManager->deleteLater();
    });

    qDebug() << "已发送下载请求:" << fileName << "URL:" << fileItem.url;
}

void MonitorWindow::onUploadFCButtonClicked()
{
    QString fileName = QFileDialog::getOpenFileName(this,
                                                    tr("选择FC设备配置表"), "",
                                                    tr("Excel Files (*.xlsx *.xls);;All Files (*)"));

    if (!fileName.isEmpty()) {
        // 获取文件名（不含路径）
        QFileInfo fileInfo(fileName);
        QString baseName = fileInfo.fileName();

        // 复制文件到应用程序数据目录
        QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
        QDir dir(configPath + "/fc_configs");
        if (!dir.exists()) {
            dir.mkpath(".");
        }

        QFile::copy(fileName, dir.filePath(baseName));
        QMessageBox::information(this, "提示", "FC设备配置表上传成功");

        // 刷新文件列表
        loadFileList();
    }
}


void MonitorWindow::onUploadICDButtonClicked()
{
    // 创建对话框
    QDialog* dialog = new QDialog(this);
    dialog->setWindowTitle("ICD文件管理");

    // 创建ICD查看组件
    ICDViewDialog* icdWidget = new ICDViewDialog(dialog);

    // 添加到对话框
    QVBoxLayout* layout = new QVBoxLayout(dialog);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->addWidget(icdWidget);

    // 设置对话框在关闭时自动删除
    dialog->setAttribute(Qt::WA_DeleteOnClose);

    // 设置无框架窗口
    dialog->setWindowFlags(Qt::Window | Qt::FramelessWindowHint);

    // 使用ESC键可以关闭对话框
    dialog->setAttribute(Qt::WA_QuitOnClose, false);

    // 显示对话框并全屏
    dialog->showFullScreen();

    // 连接对话框关闭信号，刷新文件列表
    connect(dialog, &QDialog::finished, this, &MonitorWindow::loadFileList);
}


void MonitorWindow::onRightToolbarUploadClicked()
{
    // 此方法在新UI中已不需要，保留以兼容现有接口
}

void MonitorWindow::onRealTimeButtonClicked()
{
    QMessageBox::information(this, "实时模式", "切换到实时监控模式");
}

void MonitorWindow::onReplayButtonClicked()
{
    // 显示日期时间选择对话框
    showDateTimePickerDialog();
}

// 显示日期时间选择对话框
void MonitorWindow::showDateTimePickerDialog()
{
    // 创建日期时间选择对话框
    DateTimePickerDialog dialog(this);

    // 设置默认的时间范围
    QDateTime currentDateTime = QDateTime::currentDateTime();
    QDateTime defaultStartTime = currentDateTime.addDays(-1); // 默认开始时间为前一天
    QDateTime defaultEndTime = currentDateTime; // 默认结束时间为当前时间

    dialog.setStartDateTime(defaultStartTime);
    dialog.setEndDateTime(defaultEndTime);

    // 设置日期时间范围限制（限制在过去30天内）
    QDateTime minDateTime = currentDateTime.addDays(-30);
    dialog.setDateTimeRange(minDateTime, currentDateTime);

    // 显示对话框并等待用户确认
    if (dialog.exec() == QDialog::Accepted) {
        // 获取用户选择的时间范围
        replayStartTime = dialog.getStartDateTime();
        replayEndTime = dialog.getEndDateTime();

        // 处理所选时间范围
        handleSelectedTimeRange(replayStartTime, replayEndTime);
    }
}

// 处理所选时间范围
void MonitorWindow::handleSelectedTimeRange(const QDateTime& startTime, const QDateTime& endTime)
{
    // 显示所选时间范围
    QString message = tr("已选择回放时间范围:\n开始: %1\n结束: %2")
                          .arg(startTime.toString("yyyy-MM-dd HH:mm:ss"))
                          .arg(endTime.toString("yyyy-MM-dd HH:mm:ss"));

    // 更新状态栏显示所选时间段
    ui->statusBar->setVisible(true);
    // ui->statusBar->showMessage(message, 5000);

    // 在console提示当前进入回放模式
    ui->consoleTitleLabel->setText(tr("控制台 - 回放模式"));

    // 发射回放按钮点击信号，传递选择的时间范围
    emit replayButtonClicked(startTime, endTime);

    // 在这里可以添加实际处理回放数据的代码
    // 例如从数据库或文件加载指定时间范围内的历史数据

    QMessageBox::information(this, "回放模式", message);
}

// 加载文件列表
void MonitorWindow::loadFileList()
{
    // 使用FileListService单例获取文件列表
    FileListService::getInstance().fetchFileList([this](const QStringList& fileList, bool success) {
        if (success) {
            updateFileList(fileList);
        } else {
            qWarning() << "获取文件列表失败";
        }
    });
}

// 更新文件列表UI
void MonitorWindow::updateFileList(const QStringList& fileList)
{
    // 如果不在主线程中，则将调用移到主线程
    if (QThread::currentThread() != thread()) {
        QMetaObject::invokeMethod(this, "updateFileList",
                                  Qt::QueuedConnection,
                                  Q_ARG(QStringList, fileList));
        return;
    }

    // 清空现有的文件列表
    clearFileList();

    qDebug() << "更新文件列表，共" << fileList.size() << "个文件";

    // 添加新的文件列表项
    for (const QString& fileName : fileList) {
        if (!fileName.isEmpty()) {
            QRadioButton* radio = createFileRadioButton(fileName);

            // 插入布局，确保在垂直弹簧之前
            fileListLayout->insertWidget(fileListLayout->count() - 1, radio);

            // 保存到映射中
            fileRadioButtons[fileName] = radio;
        }
    }

    // 获取默认选中的ICD文件
    QString defaultSelectedIcdFile = FileListService::getInstance().getDefaultSelectedIcdFile();

    // 如果有默认选中的ICD文件且在列表中存在，则选中它
    if (!defaultSelectedIcdFile.isEmpty() && fileRadioButtons.contains(defaultSelectedIcdFile)) {
        QRadioButton* defaultRadio = fileRadioButtons[defaultSelectedIcdFile];
        // int ret = FileListService::getInstance().createParseandanalysisP(defaultSelectedIcdFile);
        // if (ret ==0 && defaultRadio) {
        if (defaultRadio) {
            defaultRadio->setChecked(true);
            currRadio = defaultRadio;
            qDebug() << "选中默认ICD文件:" << defaultSelectedIcdFile;         
        }
    }
    // 否则，如果有文件，则默认选中第一个
    else if (!fileRadioButtons.isEmpty()) {
        QRadioButton* firstRadio = fileRadioButtons.first();
        int ret = FileListService::getInstance().createParseandanalysisP(firstRadio->text());
        if (ret == 0 && firstRadio) {
            firstRadio->setChecked(true);
            currRadio = firstRadio;
            // 将第一个文件设置为默认选中
            FileListService::getInstance().setDefaultSelectedIcdFile(firstRadio->text());
            qDebug() << "未找到默认ICD文件，选中第一个文件:" << firstRadio->text();
        }
    }

}

// 清空文件列表
void MonitorWindow::clearFileList()
{
    // 移除所有单选按钮，但保留垂直弹簧
    QLayoutItem* item;
    while (fileListLayout->count() > 1) {  // 保留最后一项垂直弹簧
        item = fileListLayout->takeAt(0);
        if (item->widget()) {
            delete item->widget();
        }
        delete item;
    }

    // 清空映射
    fileRadioButtons.clear();
}

// 创建文件单选按钮
QRadioButton* MonitorWindow::createFileRadioButton(const QString& fileName)
{
    QRadioButton* radio = new QRadioButton(fileName, ui->scrollAreaWidgetContents);
    radio->setStyleSheet("padding: 10px;");

    // 连接点击信号
    connect(radio, &QRadioButton::clicked, this, &MonitorWindow::onFileListItemClicked);

    return radio;
}

void MonitorWindow::onFileRadioToggled(bool checked)
{
    if (!checked) {
        return;
    }

    QRadioButton *radioButton = qobject_cast<QRadioButton*>(sender());
    if (radioButton) {
        // 获取当前日期时间并格式化为文件名样式
        QString timestamp = QDateTime::currentDateTime().toString("yyyy_MM_dd_hh_mm");
        QString fileExt = radioButton->text().split(".").last();
        QString statusText = QString("test_%1.%2").arg(timestamp).arg(fileExt);

        // 显示状态栏并更新文本
        ui->statusBar->setVisible(true);
    }
}
