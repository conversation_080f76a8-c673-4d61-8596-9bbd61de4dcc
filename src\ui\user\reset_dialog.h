#ifndef RESET_DIALOG_H
#define RESET_DIALOG_H

#include <QDialog>
#include <QLineEdit>
#include <QComboBox>
#include <QPushButton>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>

class ResetDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ResetDialog(QWidget *parent = nullptr);
    ~ResetDialog();

    QString getNewPassword() const;
    QString getconfirmPassword() const;

private slots:
    void onConfirmClicked();
    void onCancelClicked();

private:
    void setupUI();
    void setupConnections();

    QLineEdit *passwordEditNew;      // 新密码输入框
    QLineEdit *passwordEditConfirm;         // 确认密码输入框

    QPushButton *confirmBtn;         // 确认按钮
    QPushButton *cancelBtn;          // 取消按钮

    QVBoxLayout *mainLayout;
    QFormLayout *formLayout;
    QHBoxLayout *buttonLayout;
};

#endif // RESET_DIALOG_H
