#ifndef UNITALLOCATION_H
#define UNITALLOCATION_H

#include <QWidget>
#include <QVector>
#include <QStringList>
#include <QStyledItemDelegate>
#include <QPainter>

#include "add_type_dialog.h"

namespace Ui {
class unitAllocation;
}

class unitAllocation : public QWidget
{
    Q_OBJECT

public:
    explicit unitAllocation(QWidget *parent = nullptr);
    ~unitAllocation();

private slots:
    void addNewTypeGroup();
    void onAddButtonClicked(int row);
    void onDeleteButtonClicked(int row);

private:
    Ui::unitAllocation *ui;
    
    void addExampleData();
    void addTypeGroup(const QString &typeName, const QVector<QStringList> &items);
    void addOperationButtons(int row);
    void updateTypeSpan(int typeRow);
};

class FirstColumnDelegate : public QStyledItemDelegate {
public:
    using QStyledItemDelegate::QStyledItemDelegate;

    void paint(QPainter *painter, const QStyleOptionViewItem &option, const QModelIndex &index) const override {
        if (index.column() == 0) {  // 如果是第一列
            QStyleOptionViewItem opt = option;
            opt.state &= ~QStyle::State_Selected;  // 移除选中状态
            
            // 先绘制白色背景
            painter->save();
            painter->fillRect(option.rect, Qt::white);
            painter->restore();
            
            // 绘制文本
            QStyledItemDelegate::paint(painter, opt, index);
        } else {
            QStyledItemDelegate::paint(painter, option, index);
        }
    }
};
#endif // UNITALLOCATION_H
