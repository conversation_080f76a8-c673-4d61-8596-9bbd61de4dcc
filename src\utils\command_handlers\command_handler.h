#ifndef COMMAND_HANDLER_H
#define COMMAND_HANDLER_H

#include <QObject>
#include <QByteArray>
#include "third/tcpFrameStruct.h"

/**
 * @brief 命令处理器基类
 *
 * 命令处理器负责解析和处理各种订阅相关的命令，如订阅、取消订阅、暂停和继续等
 */
class CommandHandler : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 命令处理结果结构体
     * 
     * 用于包含命令处理的详细结果信息，比如成功/失败标志，
     * 结果消息，错误消息，以及可能的响应数据
     */
    struct CommandResult {
        bool success = false;             // 处理是否成功
        QString message;                  // 成功时的结果消息
        QString errorMessage;             // 失败时的错误消息
        QByteArray responseData;          // 可能的响应数据
    };

    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit CommandHandler(QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    virtual ~CommandHandler();
    
    /**
     * @brief 处理命令并返回详细结果
     * @param data 命令数据
     * @return 命令处理的详细结果
     */
    virtual CommandResult processCommand(const QByteArray &data);

protected:

    /**
     * @brief 处理通用命令逻辑并返回详细结果
     * @param data 命令数据
     * @param expectedCmdType 期望的命令类型
     * @param cmdTypeStr 命令类型的描述字符串
     * @return 命令处理的详细结果
     */
    CommandResult processCommonCommand(const QByteArray &data, SUBSCRIBE_COMMAND expectedCmdType, const QString &cmdTypeStr);

    /**
     * @brief 从数据中提取错误消息
     * @param dataPtr 数据指针
     * @param dataSize 数据大小
     * @param headerSize 包头大小
     * @param codeSize 返回码大小
     * @return 错误消息
     */
    QString extractErrorMessage(const char* dataPtr, size_t dataSize, size_t headerSize, size_t codeSize);

    /**
     * @brief 生成结果消息
     * @param code 返回码
     * @param cmdTypeStr 命令类型描述
     * @param errorMessage 错误消息
     * @return 结果消息对，第一个元素为成功标志，第二个元素为消息内容
     */
    QPair<bool, QString> generateResultMessage(SUBSCRIBE_RET_CODE code, const QString &cmdTypeStr);

};

#endif // COMMAND_HANDLER_H
