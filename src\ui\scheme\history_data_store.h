﻿#ifndef HISTORY_DATA_STORE_H
#define HISTORY_DATA_STORE_H

#include <QObject>
#include <QMap>
#include <QList>
#include <QString>
#include <QMutex>
#include <QDateTime>
#include <map>
#include <string>
#include "third/icdStructDef.h"

/**
 * @brief 历史数据存储类
 *
 * 该类管理ParsedData数据，按key存储signalValueMap
 * key格式：srcUnitId_msgId
 * value：ParsedData的signalValueMap成员
 */
class HistoryDataStore : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 历史数据项结构
     */
    struct HistoryDataItem {
        std::map<std::string, std::string> signalValueMap;  // 信号值映射
        QDateTime timestamp;                                // 数据时间戳
        uint32_t dataType;                                 // 总线类型
        int srcUnitId;                                     // 源单元ID
        int msgId;                                         // 发布订阅主题id

        HistoryDataItem() : dataType(0), srcUnitId(0), msgId(0) {}

        HistoryDataItem(const ParsedData& parsedData) {
            signalValueMap = parsedData.signalValueMap;
            timestamp = QDateTime::currentDateTime();

            // 根据总线类型提取srcUnitId和msgId
            dataType = parsedData.header.zmqWrapHeader.data_bus_type;
            if (dataType == 1) {
                // FC总线
                srcUnitId = parsedData.header.headerFc.aoxeHeader.source & 0xffff;
                msgId = parsedData.header.headerFc.aoxeHeader.topicId & 0xffff;
            } else if (dataType == 2 || dataType == 3) {
                // 任务1394或飞管1394总线
                srcUnitId = parsedData.header.header1394.aoxeHeader.source & 0xffff;
                msgId = parsedData.header.header1394.aoxeHeader.topicId & 0xffff;
            } else {
                srcUnitId = 0;
                msgId = 0;
            }
        }
    };

private:
    static const int MAX_ITEMS_PER_KEY = 1000;  // 每个key最大存储的数据项数量
    static const int MAX_TOTAL_ITEMS = 10000;   // 总的最大数据项数量

    QMap<QString, QList<HistoryDataItem>> m_dataStore;  // 数据存储容器
    mutable QMutex m_mutex;                             // 线程安全锁
    int m_totalItems;                                   // 当前总数据项数量

    // 私有构造函数（单例模式）
    explicit HistoryDataStore(QObject *parent = nullptr);

public:
    // 禁用拷贝构造和赋值操作
    HistoryDataStore(const HistoryDataStore&) = delete;
    HistoryDataStore& operator=(const HistoryDataStore&) = delete;

    /**
     * @brief 获取单例实例
     * @return HistoryDataStore单例引用
     */
    static HistoryDataStore& getInstance();

    /**
     * @brief 添加ParsedData到历史数据存储
     * @param parsedData 解析后的数据
     */
    void addData(const ParsedData& parsedData);

    /**
     * @brief 根据key获取历史数据列表
     * @param key 数据key（格式：srcUnitId_msgId）
     * @return 历史数据项列表
     */
    QList<HistoryDataItem> getData(const QString& key) const;

    /**
     * @brief 根据srcUnitId和msgId获取历史数据列表
     * @param srcUnitId 源单元ID
     * @param msgId 消息ID
     * @return 历史数据项列表
     */
    QList<HistoryDataItem> getData(int srcUnitId, int msgId) const;

    /**
     * @brief 获取最新的数据项
     * @param key 数据key
     * @return 最新的历史数据项，如果不存在则返回空的HistoryDataItem
     */
    HistoryDataItem getLatestData(const QString& key) const;

    /**
     * @brief 根据srcUnitId和msgId获取最新的数据项
     * @param srcUnitId 源单元ID
     * @param msgId 消息ID
     * @return 最新的历史数据项
     */
    HistoryDataItem getLatestData(int srcUnitId, int msgId) const;

    /**
     * @brief 获取所有存储的key列表
     * @return key列表
     */
    QStringList getAllKeys() const;

    /**
     * @brief 获取指定key的数据项数量
     * @param key 数据key
     * @return 数据项数量
     */
    int getDataCount(const QString& key) const;

    /**
     * @brief 获取总的数据项数量
     * @return 总数据项数量
     */
    int getTotalDataCount() const;

    /**
     * @brief 清空指定key的历史数据
     * @param key 数据key
     */
    void clearData(const QString& key);

    /**
     * @brief 清空所有历史数据
     */
    void clearAllData();

    /**
     * @brief 根据ParsedData生成存储key
     * @param parsedData 解析后的数据
     * @return 生成的key字符串
     */
    static QString generateKey(const ParsedData& parsedData);

    /**
     * @brief 根据srcUnitId和msgId生成存储key
     * @param srcUnitId 源单元ID
     * @param msgId 消息ID
     * @return 生成的key字符串
     */
    static QString generateKey(int srcUnitId, int msgId);

private:
    /**
     * @brief 清理旧数据以保持在限制范围内
     */
    void cleanupOldData();

    /**
     * @brief 从ParsedData中提取srcUnitId和msgId
     * @param parsedData 解析后的数据
     * @param srcUnitId 输出的源单元ID
     * @param msgId 输出的消息ID
     * @return 是否成功提取
     */
    static bool extractIds(const ParsedData& parsedData, int& srcUnitId, int& msgId);

signals:
    /**
     * @brief 新数据添加信号
     * @param key 数据key
     * @param dataItem 新添加的数据项
     */
    void dataAdded(const QString& key, const HistoryDataItem& dataItem);

    /**
     * @brief 数据清理信号
     * @param key 被清理的数据key
     * @param removedCount 被移除的数据项数量
     */
    void dataCleared(const QString& key, int removedCount);
};

#endif // HISTORY_DATA_STORE_H
