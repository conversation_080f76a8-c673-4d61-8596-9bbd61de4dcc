<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>UserManagement</class>
 <widget class="QWidget" name="UserManagement">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>用户管理</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget {
    background-color: #f0f2f5;
    font-family: Microsoft YaHei, Arial, sans-serif;
}

QLabel {
    color: #333333;
    font-size: 14px;
}

QLineEdit {
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background-color: white;
    font-size: 14px;
    min-height: 32px;
}

QLineEdit:focus {
    border-color: #40a9ff;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

QPushButton {
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    padding: 6px 16px;
    min-height: 32px;
    min-width: 80px;
}

#searchBtn, #addUserBtn {
    background-color: #1890ff !important;
    color: white !important;
    font-weight: bold !important;
}

#searchBtn:hover, #addUserBtn:hover {
    background-color: #40a9ff !important;
}

#searchBtn:pressed, #addUserBtn:pressed {
    background-color: #096dd9 !important;
}

#resetBtn {
    background-color: #f0f0f0;
    color: #595959;
    border: 1px solid #d9d9d9;
}

#resetBtn:hover {
    background-color: #fafafa;
    color: #40a9ff;
    border-color: #40a9ff;
}

QTableView {
    background-color: white;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    selection-background-color: #e6f7ff;
    selection-color: #000000;
    gridline-color: #f0f0f0;
}

QTableView::item {
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
    min-height: 48px;
}

QTableView::item:selected {
    background-color: #e6f7ff;
    color: #1890ff;
}

QHeaderView::section {
    background-color: #fafafa;
    color: #262626;
    padding: 12px 8px;
    border: none;
    border-right: 1px solid #f0f0f0;
    border-bottom: 1px solid #e8e8e8;
    font-weight: bold;
}

QComboBox {
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background-color: white;
    min-height: 32px;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: right center;
    width: 20px;
    border-left: none;
}

QComboBox:on {
    border-color: #40a9ff;
}

QWidget#userTableView QPushButton {
    background-color: #1890ff;
    color: white;
    font-weight: bold;
    min-width: 60px;
    border-radius: 3px;
    padding: 5px 10px;
}

QWidget#userTableView QPushButton:hover {
    background-color: #40a9ff;
}

QPushButton.delete-btn {
    color: #f5222d;
}

QPushButton.delete-btn:hover {
    color: #ff4d4f;
}

QPushButton.disable-btn {
    color: #faad14;
}

QPushButton.disable-btn:hover {
    color: #ffc53d;
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="0,1">
   <property name="spacing">
    <number>10</number>
   </property>
   <property name="leftMargin">
    <number>20</number>
   </property>
   <property name="topMargin">
    <number>20</number>
   </property>
   <property name="rightMargin">
    <number>20</number>
   </property>
   <property name="bottomMargin">
    <number>20</number>
   </property>
   <item>
    <widget class="QWidget" name="searchPanel" native="true">
     <property name="styleSheet">
      <string notr="true">background-color: white;
border-radius: 4px;
padding: 10px;</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="leftMargin">
       <number>20</number>
      </property>
      <property name="topMargin">
       <number>10</number>
      </property>
      <property name="rightMargin">
       <number>20</number>
      </property>
      <property name="bottomMargin">
       <number>10</number>
      </property>
      <item>
       <widget class="QLabel" name="usernameLabel">
        <property name="text">
         <string>用户</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="usernameEdit">
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>54</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>250</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="placeholderText">
         <string>请输入用户名</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="phoneLabel">
        <property name="text">
         <string>联系电话</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="phoneEdit">
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>54</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>250</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="placeholderText">
         <string>请输入联系电话</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="roleLabel">
        <property name="text">
         <string>角色</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="roleComboBox">
        <property name="minimumSize">
         <size>
          <width>150</width>
          <height>54</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>200</width>
          <height>16777215</height>
         </size>
        </property>
        <item>
         <property name="text">
          <string>请选择角色</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>管理员</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>普通用户</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>访客</string>
         </property>
        </item>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="resetBtn">
        <property name="text">
         <string>重 置</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="searchBtn">
        <property name="styleSheet">
         <string notr="true">background-color: #1890ff;
color: white;
font-weight: bold;</string>
        </property>
        <property name="text">
         <string>查 询</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="tablePanel" native="true">
     <property name="styleSheet">
      <string notr="true">background-color: white;
border-radius: 4px;</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="leftMargin">
       <number>20</number>
      </property>
      <property name="topMargin">
       <number>20</number>
      </property>
      <property name="rightMargin">
       <number>20</number>
      </property>
      <property name="bottomMargin">
       <number>20</number>
      </property>
      <item>
       <layout class="QHBoxLayout" name="tableHeaderLayout">
        <property name="bottomMargin">
         <number>10</number>
        </property>
        <item>
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="addUserBtn">
          <property name="styleSheet">
           <string notr="true">background-color: #1890ff;
color: white;
font-weight: bold;</string>
          </property>
          <property name="text">
           <string>添加用户</string>
          </property>
          <property name="icon">
           <iconset>
            <normaloff>:/images/add-user.png</normaloff>:/images/add-user.png</iconset>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QTableView" name="userTableView">
        <property name="editTriggers">
         <set>QAbstractItemView::NoEditTriggers</set>
        </property>
        <property name="selectionBehavior">
         <enum>QAbstractItemView::SelectRows</enum>
        </property>
        <property name="sortingEnabled">
         <bool>true</bool>
        </property>
        <attribute name="horizontalHeaderStretchLastSection">
         <bool>true</bool>
        </attribute>
        <attribute name="verticalHeaderVisible">
         <bool>false</bool>
        </attribute>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
