#include "subscribe_handler.h"
#include <QDebug>

/**
 * @brief 构造函数
 */
SubscribeCommandHandler::SubscribeCommandHandler(QObject *parent) 
    : CommandHandler(parent) 
{
    qDebug() << "创建订阅命令处理器";
}

/**
 * @brief 析构函数
 */
SubscribeCommandHandler::~SubscribeCommandHandler() 
{
    qDebug() << "销毁订阅命令处理器";
}


/**
 * @brief 处理订阅命令并返回详细结果 (新方法)
 */
CommandHandler::CommandResult SubscribeCommandHandler::processCommand(const QByteArray &data)
{
    // 使用基类的新通用命令处理方法，返回详细结果
    return processCommonCommand(data, CMD_SUBSCRIBE, "订阅");
} 
