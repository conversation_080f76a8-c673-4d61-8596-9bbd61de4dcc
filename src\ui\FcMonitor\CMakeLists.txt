# FC Monitor CMakeLists.txt
cmake_minimum_required(VERSION 3.16)

project(FcMonitor)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt6组件
find_package(Qt6 REQUIRED COMPONENTS Core Widgets)

# 设置Qt自动处理
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# 源文件
set(SOURCES
    fc_data_structures.h
    fc_data_manager.h
    fc_data_manager.cpp
    fc_data_parser.h
    fc_data_parser.cpp
    fc_monitor_widget.h
    fc_monitor_widget.cpp
)

# 测试程序源文件
set(TEST_SOURCES
    test_fc_monitor.cpp
    fc_monitor.cpp
    ${SOURCES}
)

# 创建FC Monitor库
add_library(FcMonitor STATIC ${SOURCES})

# 链接Qt库
target_link_libraries(FcMonitor
    Qt6::Core
    Qt6::Widgets
)

# 设置包含目录
target_include_directories(FcMonitor PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# 创建测试程序
add_executable(FcMonitorTest ${TEST_SOURCES})

# 链接库
target_link_libraries(FcMonitorTest
    Qt6::Core
    Qt6::Widgets
)

# 设置包含目录
target_include_directories(FcMonitorTest PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# 编译选项
if(MSVC)
    target_compile_options(FcMonitor PRIVATE /W4)
    target_compile_options(FcMonitorTest PRIVATE /W4)
else()
    target_compile_options(FcMonitor PRIVATE -Wall -Wextra -Wpedantic)
    target_compile_options(FcMonitorTest PRIVATE -Wall -Wextra -Wpedantic)
endif()

# 安装规则
install(TARGETS FcMonitor FcMonitorTest
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES
    fc_data_structures.h
    fc_data_manager.h
    fc_data_parser.h
    fc_monitor_widget.h
    DESTINATION include/FcMonitor
)
