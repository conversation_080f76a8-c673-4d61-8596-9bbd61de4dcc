#ifndef MEMBER_DATA_H
#define MEMBER_DATA_H

#include <QString>

// Member data structure
struct MemberData {
    QString name;
    double signalValue;
    QString unit;
    QString signalType;
    int startWord;
    int startBit;
    QString status;
    int length;
    QString rawValue;
    double maxValue;
    double minValue;
    bool changed;
    
    // 判断数据是否有实质性变化的方法
    bool hasSignificantChanges(const MemberData& other) const {
        // 只判断重要字段是否变化
        return signalValue != other.signalValue ||
               status != other.status ||
               rawValue != other.rawValue;
    }
};

#endif // MEMBER_DATA_H 