#include "user_management.h"
#include "ui_usermanagement.h"
#include "user_dialog.h"
#include "reset_dialog.h"
#include "utils/api_url_manager.h"
#include "utils/user_session.h"

#include <QDateTime>
#include <QMessageBox>
#include <QPushButton>
#include <QHBoxLayout>
#include <QSizePolicy>
#include <QTimer>
#include <QStyledItemDelegate>
#include <QGridLayout>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>
#include <QDebug>

UserManagement::UserManagement(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::UserManagement)
    , userModel(new QStandardItemModel(this))
    , proxyModel(new QSortFilterProxyModel(this))
{
    ui->setupUi(this);
    setupUI();
    setupConnections();
    initializeUserTable();
    loadUserData();
}

UserManagement::~UserManagement()
{
    delete ui;
}

void UserManagement::setupUI()
{
    // 设置代理模型
    proxyModel->setSourceModel(userModel);
    ui->userTableView->setModel(proxyModel);

    // 调整表格外观
    ui->userTableView->horizontalHeader()->setDefaultSectionSize(150);
    ui->userTableView->verticalHeader()->setDefaultSectionSize(50);
    ui->userTableView->setAlternatingRowColors(true);
    ui->userTableView->setSortingEnabled(true);

    // 设置默认排序为第一列（账号）升序
    ui->userTableView->sortByColumn(0, Qt::AscendingOrder);

    // 允许表格显示水平和垂直滚动条
    ui->userTableView->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    ui->userTableView->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 设置表格视图的选择模式
    ui->userTableView->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->userTableView->setSelectionBehavior(QAbstractItemView::SelectRows);

    // 设置表格行高
    ui->userTableView->verticalHeader()->setDefaultSectionSize(50);
    ui->userTableView->verticalHeader()->setSectionResizeMode(QHeaderView::Fixed);

    // 优化表格性能
    ui->userTableView->setItemDelegate(new QStyledItemDelegate(ui->userTableView));
    ui->userTableView->viewport()->setAttribute(Qt::WA_Hover);

    // 隐藏行号
    ui->userTableView->verticalHeader()->setVisible(false);

    // 提高视图 Z 顺序，确保在其他元素之上显示
    ui->userTableView->raise();
}

void UserManagement::setupConnections()
{
    connect(ui->searchBtn, &QPushButton::clicked, this, &UserManagement::onSearchClicked);
    connect(ui->resetBtn, &QPushButton::clicked, this, &UserManagement::onResetClicked);
    connect(ui->addUserBtn, &QPushButton::clicked, this, &UserManagement::onAddUserClicked);

    // 设置重置按钮样式，与查询按钮保持一致
    ui->resetBtn->setStyleSheet("QPushButton#resetBtn { background-color: #1890ff; color: white; border: none; font-weight: bold; } "
                                "QPushButton#resetBtn:hover { background-color: #40a9ff; } "
                                "QPushButton#resetBtn:pressed { background-color: #096dd9; }");
}

void UserManagement::initializeUserTable()
{
    // 设置表头
    QStringList headers;
    headers << "账号" << "姓名" << "联系电话" << "证件号码" << "角色" << "状态" << "操作";
    userModel->setHorizontalHeaderLabels(headers);

    // 设置列宽
    ui->userTableView->setColumnWidth(0, 120 + 80);  // 账号
    ui->userTableView->setColumnWidth(1, 120 + 80);  // 姓名
    ui->userTableView->setColumnWidth(2, 150 + 80);  // 联系电话
    ui->userTableView->setColumnWidth(3, 230 + 80);  // 证件号码 - 略微增加宽度
    ui->userTableView->setColumnWidth(4, 120 + 80);  // 角色
    ui->userTableView->setColumnWidth(5, 100 + 80);  // 状态
    ui->userTableView->setColumnWidth(6, 300);  // 操作 - 恢复为300px宽度

    // 确保操作列足够宽以显示所有按钮，但不要过宽
    ui->userTableView->horizontalHeader()->setSectionResizeMode(6, QHeaderView::Fixed);

    // 设置所有列的对齐方式为居中
    for (int col = 0; col < userModel->columnCount(); ++col) {
        userModel->setHeaderData(col, Qt::Horizontal, Qt::AlignCenter, Qt::TextAlignmentRole);
    }

    // 创建居中对齐的委托
    class CenterAlignDelegate : public QStyledItemDelegate {
    public:
        CenterAlignDelegate(QObject* parent = nullptr) : QStyledItemDelegate(parent) {}

        void initStyleOption(QStyleOptionViewItem* option, const QModelIndex& index) const override {
            QStyledItemDelegate::initStyleOption(option, index);
            option->displayAlignment = Qt::AlignCenter;
        }
    };

    // 为表格视图设置居中对齐的委托
    ui->userTableView->setItemDelegate(new CenterAlignDelegate(ui->userTableView));
}

void UserManagement::loadUserData()
{
    // 清空现有数据
    userModel->removeRows(0, userModel->rowCount());

    // 创建网络请求管理器
    QNetworkAccessManager* networkManager = new QNetworkAccessManager(this);

    // 创建请求对象
    QString token = UserSession::getInstance().getToken();
    QNetworkRequest request(ApiUrlManager::getInstance().getAllUserUrl());
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    request.setRawHeader("accept", "*/*");
    if (!token.isEmpty()) {
        request.setRawHeader("Authorization", token.toUtf8());
    }

    // 显示等待对话框
    // QMessageBox waitMsgBox;
    // waitMsgBox.setWindowTitle("请稍候");
    // waitMsgBox.setText("正在加载用户数据，请稍候...");
    // waitMsgBox.setStandardButtons(QMessageBox::NoButton);
    // waitMsgBox.setModal(true);

    // // 使用QTimer延迟显示等待对话框，避免闪烁
    // QTimer::singleShot(100, &waitMsgBox, [&waitMsgBox]() {
    //     waitMsgBox.show();
    // });

    // 发送GET请求
    QNetworkReply* reply = networkManager->get(request);

    // 连接请求完成信号
    connect(reply, &QNetworkReply::finished, [this, reply, networkManager]() {
        if (reply->error() == QNetworkReply::NoError) {
            // 读取响应数据
            QByteArray responseData = reply->readAll();
            QJsonDocument jsonResponse = QJsonDocument::fromJson(responseData);
            QJsonObject jsonObj = jsonResponse.object();

            bool success = jsonObj["success"].toBool();
            QString code = jsonObj["code"].toString();
            QString message = jsonObj["message"].toString();

            if (success && code == "200") {
                // 获取data对象
                QJsonObject dataObj = jsonObj["data"].toObject();

                // 获取用户列表
                QJsonArray items = dataObj["items"].toArray();

                // 临时禁用排序
                bool wasSortingEnabled = ui->userTableView->isSortingEnabled();
                ui->userTableView->setSortingEnabled(false);

                // 记录当前排序状态
                int sortColumn = ui->userTableView->horizontalHeader()->sortIndicatorSection();
                Qt::SortOrder sortOrder = ui->userTableView->horizontalHeader()->sortIndicatorOrder();

                // 遍历用户列表，添加到表格
                for (int i = 0; i < items.size(); ++i) {
                    QJsonObject userObj = items[i].toObject();
                    // 保存用户
                    m_allUser[userObj["userName"].toString()] = userObj;
                    // 创建行项目
                    QList<QStandardItem*> userRow;
                    QStandardItem* item;

                    // 账号
                    QString userName = userObj["userName"].toString();
                    item = new QStandardItem(userName);
                    item->setTextAlignment(Qt::AlignCenter);
                    userRow << item;

                    // 姓名
                    QString realName = userObj["realName"].toString();
                    item = new QStandardItem(realName);
                    item->setTextAlignment(Qt::AlignCenter);
                    userRow << item;

                    // 联系电话
                    QString phone = userObj["phone"].toString();
                    item = new QStandardItem(phone);
                    item->setTextAlignment(Qt::AlignCenter);
                    userRow << item;

                    // 证件号码
                    QString identification = userObj["identification"].toString();
                    item = new QStandardItem(identification);
                    item->setTextAlignment(Qt::AlignCenter);
                    userRow << item;

                    // 角色 (根据roleVos确定，这里暂时写死)
                    QString role = "普通用户";
                    if (userName == "admin") {
                        role = "管理员";
                    }
                    item = new QStandardItem(role);
                    item->setTextAlignment(Qt::AlignCenter);
                    userRow << item;

                    // 状态
                    int status = userObj["status"].toInt();
                    QString statusStr = (status == 0) ? "正常" : "停用";
                    item = new QStandardItem(statusStr);
                    item->setTextAlignment(Qt::AlignCenter);
                    userRow << item;

                    // 操作列占位符
                    item = new QStandardItem("");
                    userRow << item;

                    // 添加行到表格
                    userModel->appendRow(userRow);
                }

                // 在数据显示完成后添加操作按钮
                QTimer::singleShot(100, this, [this, wasSortingEnabled, sortColumn, sortOrder]() {
                    for (int row = 0; row < userModel->rowCount(); ++row) {
                        addOperationButtons(row);
                    }

                    // 恢复排序状态
                    ui->userTableView->setSortingEnabled(wasSortingEnabled);
                    if (wasSortingEnabled) {
                        ui->userTableView->sortByColumn(sortColumn, sortOrder);
                    }

                    // 强制更新视图
                    ui->userTableView->viewport()->update();
                });

                qDebug() << "成功加载" << items.size() << "个用户";
            } else {
                QMessageBox::warning(this, "加载失败",
                                     QString("无法加载用户数据，错误代码：%1，错误信息：%2").arg(code).arg(message));

                // 添加一个默认的管理员行，以便界面不为空
                addDefaultAdminRow();
            }
        } else {
            QMessageBox::critical(this, "网络错误",
                                  QString("发送请求时出现错误：%1").arg(reply->errorString()));

            // 添加一个默认的管理员行，以便界面不为空
            addDefaultAdminRow();
        }

        // 清理资源
        reply->deleteLater();
        networkManager->deleteLater();
    });
}

// 添加默认的管理员行，用于网络请求失败时显示
void UserManagement::addDefaultAdminRow()
{
    QList<QStandardItem*> adminRow;
    QStandardItem* item;

    // 账号
    item = new QStandardItem("admin");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 姓名
    item = new QStandardItem("admin");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 联系电话
    item = new QStandardItem("");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 证件号码
    item = new QStandardItem("");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 角色
    item = new QStandardItem("管理员");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 状态
    item = new QStandardItem("正常");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 操作列占位符
    item = new QStandardItem("");
    adminRow << item;

    userModel->appendRow(adminRow);

    // 添加操作按钮
    QTimer::singleShot(100, this, [this]() {
        addOperationButtons(0);
        ui->userTableView->viewport()->update();
    });
}

void UserManagement::addOperationButtons(int row)
{
    // 首先检查表格的行数是否足够
    if (row >= userModel->rowCount())
        return;

    // 创建一个新的小部件来容纳所有按钮
    QWidget* widget = new QWidget();
    widget->setObjectName("operationButtonsWidget");

    // 使用水平布局来排列按钮
    QHBoxLayout* layout = new QHBoxLayout(widget);
    layout->setContentsMargins(5, 0, 5, 0);  // 增加左右外边距
    layout->setSpacing(10);  // 增加按钮间距到10px

    // 创建各种操作按钮
    QPushButton* editBtn = new QPushButton("编辑", widget);
    editBtn->setObjectName("editBtn");
    editBtn->setProperty("row", row);
    editBtn->setProperty("action", "edit");
    editBtn->setFixedSize(60, 28);  // 恢复更大的按钮尺寸
    editBtn->setCursor(Qt::PointingHandCursor);
    editBtn->setStyleSheet("QPushButton#editBtn { color: white; background-color: #1890ff; border: none; border-radius: 4px; font-weight: bold; } "
                           "QPushButton#editBtn:hover { background-color: #40a9ff; }");

    QPushButton* resetPwdBtn = new QPushButton("重置密码", widget);
    resetPwdBtn->setObjectName("resetPwdBtn");
    resetPwdBtn->setProperty("row", row);
    resetPwdBtn->setProperty("action", "resetPassword");
    resetPwdBtn->setFixedSize(80, 28);  // 给完整文本足够空间
    resetPwdBtn->setCursor(Qt::PointingHandCursor);
    resetPwdBtn->setStyleSheet("QPushButton#resetPwdBtn { color: white; background-color: #1890ff; border: none; border-radius: 4px; font-weight: bold; } "
                               "QPushButton#resetPwdBtn:hover { background-color: #40a9ff; }");

    QPushButton* disableBtn = new QPushButton("停用", widget);
    disableBtn->setObjectName("disableBtn");
    disableBtn->setProperty("row", row);
    disableBtn->setProperty("action", "disable");
    disableBtn->setFixedSize(60, 28);  // 恢复更大的按钮尺寸
    disableBtn->setCursor(Qt::PointingHandCursor);
    disableBtn->setStyleSheet("QPushButton#disableBtn { color: white; background-color: #faad14; border: none; border-radius: 4px; font-weight: bold; } "
                              "QPushButton#disableBtn:hover { background-color: #ffc53d; }");

    QPushButton* deleteBtn = new QPushButton("删除", widget);
    deleteBtn->setObjectName("deleteBtn");
    deleteBtn->setProperty("row", row);
    deleteBtn->setProperty("action", "delete");
    deleteBtn->setFixedSize(60, 28);  // 恢复更大的按钮尺寸
    deleteBtn->setCursor(Qt::PointingHandCursor);
    deleteBtn->setStyleSheet("QPushButton#deleteBtn { color: white; background-color: #f5222d; border: none; border-radius: 4px; font-weight: bold; } "
                             "QPushButton#deleteBtn:hover { background-color: #ff4d4f; }");

    // 将按钮添加到水平布局
    layout->addWidget(editBtn);
    layout->addWidget(resetPwdBtn);
    layout->addWidget(disableBtn);
    layout->addWidget(deleteBtn);

    // 设置小部件的布局
    widget->setLayout(layout);

    // 连接按钮信号
    connect(editBtn, &QPushButton::clicked, this, &UserManagement::onOperationButtonClicked);
    connect(resetPwdBtn, &QPushButton::clicked, this, &UserManagement::onOperationButtonClicked);
    connect(disableBtn, &QPushButton::clicked, this, &UserManagement::onOperationButtonClicked);
    connect(deleteBtn, &QPushButton::clicked, this, &UserManagement::onOperationButtonClicked);

    // 将小部件设置到表格的单元格中
    QModelIndex index = proxyModel->index(row, 6);
    ui->userTableView->setIndexWidget(index, widget);

    // 强制更新表格视图，确保显示
    ui->userTableView->viewport()->update();
}

void UserManagement::onSearchClicked()
{
    QString username = ui->usernameEdit->text().trimmed();
    QString phone = ui->phoneEdit->text().trimmed();
    QString role = ui->roleComboBox->currentIndex() > 0 ? ui->roleComboBox->currentText() : "";

    // 在实际应用中，这里应该执行数据库查询或其他搜索操作
    // 为了演示，我们重新加载示例数据
    userModel->removeRows(0, userModel->rowCount());

    // 添加基础数据
    QList<QStandardItem*> adminRow;
    QStandardItem* item;

    // 账号
    item = new QStandardItem("admin");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 姓名
    item = new QStandardItem("admin");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 联系电话
    item = new QStandardItem("");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 证件号码
    item = new QStandardItem("");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 角色
    item = new QStandardItem("管理员");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 状态
    item = new QStandardItem("正常");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 操作列占位符
    item = new QStandardItem("");
    adminRow << item;

    userModel->appendRow(adminRow);

    // 如果有搜索条件，添加一条匹配记录
    if (!username.isEmpty() || !phone.isEmpty() || !role.isEmpty()) {
        QList<QStandardItem*> searchRow;

        // 账号
        item = new QStandardItem(username.isEmpty() ? "user1" : username);
        item->setTextAlignment(Qt::AlignCenter);
        searchRow << item;

        // 姓名
        item = new QStandardItem("测试用户");
        item->setTextAlignment(Qt::AlignCenter);
        searchRow << item;

        // 联系电话
        item = new QStandardItem(phone.isEmpty() ? "13800138000" : phone);
        item->setTextAlignment(Qt::AlignCenter);
        searchRow << item;

        // 证件号码
        item = new QStandardItem("110101199001011234");
        item->setTextAlignment(Qt::AlignCenter);
        searchRow << item;

        // 角色
        item = new QStandardItem(role.isEmpty() ? "普通用户" : role);
        item->setTextAlignment(Qt::AlignCenter);
        searchRow << item;

        // 状态
        item = new QStandardItem("正常");
        item->setTextAlignment(Qt::AlignCenter);
        searchRow << item;

        // 操作列占位符
        item = new QStandardItem("");
        searchRow << item;

        userModel->appendRow(searchRow);
    }

    // 在搜索结果显示后，添加操作按钮
    QTimer::singleShot(100, this, [this]() {
        for (int row = 0; row < userModel->rowCount(); ++row) {
            addOperationButtons(row);
        }
        // 强制更新视图
        ui->userTableView->viewport()->update();
    });
}

void UserManagement::onResetClicked()
{
    // 清空搜索条件
    ui->usernameEdit->clear();
    ui->phoneEdit->clear();
    ui->roleComboBox->setCurrentIndex(0);

    // 清空当前数据
    userModel->removeRows(0, userModel->rowCount());

    // 添加基础数据
    QList<QStandardItem*> adminRow;
    QStandardItem* item;

    // 账号
    item = new QStandardItem("admin");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 姓名
    item = new QStandardItem("admin");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 联系电话
    item = new QStandardItem("");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 证件号码
    item = new QStandardItem("");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 角色
    item = new QStandardItem("管理员");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 状态
    item = new QStandardItem("正常");
    item->setTextAlignment(Qt::AlignCenter);
    adminRow << item;

    // 操作列占位符
    item = new QStandardItem("");
    adminRow << item;

    userModel->appendRow(adminRow);

    // 延迟添加操作按钮，确保UI更新后再添加
    QTimer::singleShot(100, this, [this]() {
        for (int row = 0; row < userModel->rowCount(); ++row) {
            addOperationButtons(row);
        }
        // 强制更新视图
        ui->userTableView->viewport()->update();
    });
}

void UserManagement::onAddUserClicked()
{
    // 创建并显示添加用户对话框
    UserDialog dialog(this);

    // 如果用户点击确认按钮
    if (dialog.exec() == QDialog::Accepted) {
        // 获取对话框中的用户信息
        QString username = dialog.getUsername();
        QString name = dialog.getName();
        QString password = dialog.getPassword();
        QString role = dialog.getRole();

        // 创建网络请求管理器
        QNetworkAccessManager* networkManager = new QNetworkAccessManager(this);

        // 创建请求对象
        QString token = UserSession::getInstance().getToken();
        QNetworkRequest request(ApiUrlManager::getInstance().getUserAddUrl());
        request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
        request.setRawHeader("accept", "*/*");
        if (!token.isEmpty()) {
            request.setRawHeader("Authorization", token.toUtf8());
        }
        // 确定角色ID
        int roleId = 0;
        if (role == "管理员") {
            roleId = 1;
        }
        else if (role == "普通用户") {
            roleId = 2;
        }
        else if (role == "访客") {
            roleId = 3;
        }
        else {
            roleId = 2;
        }

        // 构建JSON请求体
        QJsonObject jsonObj;
        jsonObj["username"] = username;
        jsonObj["name"] = name;
        jsonObj["password"] = password;
        jsonObj["roleId"] = roleId;
        jsonObj["phone"] = "";  // 可以根据需要添加电话号码
        jsonObj["identification"] = "";  // 可以根据需要添加证件号码
        jsonObj["description"] = "";  // 可以根据需要添加描述信息

        QJsonDocument jsonDoc(jsonObj);
        QByteArray jsonData = jsonDoc.toJson();

        // 发送POST请求
        QNetworkReply* reply = networkManager->post(request, jsonData);

        // 连接请求完成信号
        connect(reply, &QNetworkReply::finished, [this, reply, networkManager, username, name, role]() {

            if (reply->error() == QNetworkReply::NoError) {
                // 读取响应数据
                QByteArray responseData = reply->readAll();
                QJsonDocument jsonResponse = QJsonDocument::fromJson(responseData);
                QJsonObject jsonObj = jsonResponse.object();

                bool success = jsonObj["success"].toBool();
                QString code = jsonObj["code"].toString();
                QString message = jsonObj["message"].toString();

                if (success) {
                    QMessageBox::information(this, "成功", QString("已成功添加用户：%1").arg(username));

                    // 在表格中添加新用户前，临时禁用排序
                    bool wasSortingEnabled = ui->userTableView->isSortingEnabled();
                    ui->userTableView->setSortingEnabled(false);

                    // 记录当前排序状态
                    int sortColumn = ui->userTableView->horizontalHeader()->sortIndicatorSection();
                    Qt::SortOrder sortOrder = ui->userTableView->horizontalHeader()->sortIndicatorOrder();

                    // 在表格中添加新用户
                    QList<QStandardItem*> userRow;
                    QStandardItem* item;

                    // 账号
                    item = new QStandardItem(username);
                    item->setTextAlignment(Qt::AlignCenter);
                    userRow << item;

                    // 姓名
                    item = new QStandardItem(name);
                    item->setTextAlignment(Qt::AlignCenter);
                    userRow << item;

                    // 联系电话 (暂时为空)
                    item = new QStandardItem("");
                    item->setTextAlignment(Qt::AlignCenter);
                    userRow << item;

                    // 证件号码 (暂时为空)
                    item = new QStandardItem("");
                    item->setTextAlignment(Qt::AlignCenter);
                    userRow << item;

                    // 角色
                    item = new QStandardItem(role);
                    item->setTextAlignment(Qt::AlignCenter);
                    userRow << item;

                    // 状态
                    item = new QStandardItem("正常");
                    item->setTextAlignment(Qt::AlignCenter);
                    userRow << item;

                    // 操作列占位符
                    item = new QStandardItem("");
                    userRow << item;

                    // 添加新行到表格
                    userModel->appendRow(userRow);

                    // 为新添加的行添加操作按钮
                    int newRow = userModel->rowCount() - 1;
                    QTimer::singleShot(100, this, [this, newRow]() {
                        addOperationButtons(newRow);
                        // 强制更新视图
                        ui->userTableView->viewport()->update();

                        // 将代理模型中的索引映射到源模型中的索引
                        QModelIndex sourceIndex = userModel->index(newRow, 0);
                        QModelIndex proxyIndex = proxyModel->mapFromSource(sourceIndex);

                        // 滚动到新添加的行并选中它
                        // if(proxyIndex.isValid()) {
                        //     ui->userTableView->scrollTo(proxyIndex);
                        //     ui->userTableView->selectRow(proxyIndex.row());
                        // }

                        // 恢复排序状态
                        // ui->userTableView->setSortingEnabled(wasSortingEnabled);
                        // if (wasSortingEnabled) {
                        //     ui->userTableView->sortByColumn(sortColumn, sortOrder);
                        // }
                    });
                } else {
                    QMessageBox::warning(this, "添加失败",
                                         QString("无法添加用户，错误代码：%1，错误信息：%2").arg(code).arg(message));
                }
            } else {
                QMessageBox::critical(this, "网络错误",
                                      QString("发送请求时出现错误：%1").arg(reply->errorString()));
            }

            // 清理资源
            reply->deleteLater();
            networkManager->deleteLater();
        });
    }
}

void UserManagement::onOperationButtonClicked()
{
    // 获取发送信号的按钮
    QPushButton* button = qobject_cast<QPushButton*>(sender());
    if (!button)
        return;

    // 获取行号和操作类型
    int row = button->property("row").toInt() - 1;
    QString action = button->property("action").toString();

    // 通过代理模型获取实际行索引（如果有排序或过滤）
    QModelIndex index = proxyModel->index(row, 0);
    if (!index.isValid())
        return;

    // 获取用户账号
    QString username = proxyModel->data(proxyModel->index(row, 0)).toString();

    // 根据操作类型执行不同的操作
    if (action == "edit") {
        QMessageBox::information(this, "编辑用户", QString("即将编辑用户: %1").arg(username));
        // TODO: 打开编辑用户对话框
    }
    else if (action == "resetPassword") {
        // 创建并显示添加用户对话框
        ResetDialog dialog(this);

        // 如果用户点击确认按钮
        if (dialog.exec() == QDialog::Accepted) {
            // 获取对话框中的用户信息
            QString passwordNew = dialog.getNewPassword();
            QString passwordConfirm = dialog.getconfirmPassword();
            if (passwordNew != passwordConfirm) {
                QMessageBox::information(this, tr("重置密码"), tr("密码输入不一致"));
                return;
            }

            // 创建请求对象
            QString token = UserSession::getInstance().getToken();
            QNetworkRequest request(ApiUrlManager::getInstance().getUrl(ApiUrlManager::RESET_USER));
            request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
            request.setRawHeader("accept", "*/*");
            if (!token.isEmpty()) {
                request.setRawHeader("Authorization", token.toUtf8());
            }

            // 构建JSON请求体
            QJsonObject jsonObj;
            jsonObj["newPassword"] = passwordNew;
            jsonObj["userId"] = m_allUser[username]["userId"].toVariant().toString();

            QJsonDocument jsonDoc(jsonObj);
            QByteArray jsonData = jsonDoc.toJson();
            // 创建网络请求管理器
            QNetworkAccessManager* networkManager = new QNetworkAccessManager(this);
            // 发送POST请求
            QNetworkReply* reply = networkManager->post(request, jsonData);

            // 连接请求完成的信号
            connect(reply, &QNetworkReply::finished, [this, reply, username, row]() {
                if (reply->error() == QNetworkReply::NoError) {
                    QByteArray response = reply->readAll();
                    QJsonDocument doc = QJsonDocument::fromJson(response);
                    QJsonObject obj = doc.object();

                    if (obj["success"].toBool()) {
                        QMessageBox::information(this, tr("重置密码"), tr("已重置用户： %1 ").arg(username));
                    }
                    else {
                        QMessageBox::information(this, tr("重置密码"), tr("重置用户 %1 失败").arg(username));
                    }
                }
                else {
                    QMessageBox::warning(this, tr("重置密码"), tr("重置用户失败: %1").arg(reply->errorString()));
                }
                reply->deleteLater();
            });
        }
    }
    else if (action == "disable") {
        // 检查是否可以停用（管理员账户不能停用）
        if (username == "admin") {
            QMessageBox::warning(this, "操作失败", "管理员账号无法停用！");
            return;
        }

        int reply = QMessageBox::question(this, "确认操作",
                                          QString("确定要停用用户 %1 吗?").arg(username),
                                          QMessageBox::Yes | QMessageBox::No);

        if (reply == QMessageBox::Yes) {
            QMessageBox::information(this, "停用用户", QString("已停用用户: %1").arg(username));
            // TODO: 实际停用用户的逻辑
        }
    }
    else if (action == "delete") {
        // 检查是否可以删除（管理员账户不能删除）
        if (username == "admin") {
            QMessageBox::warning(this, "操作失败", "管理员账号无法删除！");
            return;
        }

        int reply = QMessageBox::question(this, "确认操作",
                                          QString("确定要删除用户 %1 吗? 此操作不可撤销!").arg(username),
                                          QMessageBox::Yes | QMessageBox::No);

        if (reply == QMessageBox::Yes) {
            // 创建参数映射
            QMap<QString, QString> params;
            // QJsonObject temp = m_allUser[username];
            params["userId"] = m_allUser[username]["userId"].toVariant().toString();
            // 构建带参数的URL
            QNetworkRequest request;
            request = QNetworkRequest(ApiUrlManager::getInstance().getUrlWithParams(ApiUrlManager::USER_DELETE, params));
            // 创建请求对象
            QString token = UserSession::getInstance().getToken();
            request.setRawHeader("accept", "*/*");
            if (!token.isEmpty()) {
                request.setRawHeader("Authorization", token.toUtf8());
            }
            QNetworkAccessManager* networkManager = new QNetworkAccessManager(this);
            QNetworkReply* reply = networkManager->deleteResource(request);

            // 连接请求完成的信号
            connect(reply, &QNetworkReply::finished, [this, reply, username, row]() {
                if (reply->error() == QNetworkReply::NoError) {
                    QByteArray response = reply->readAll();
                    QJsonDocument doc = QJsonDocument::fromJson(response);
                    QJsonObject obj = doc.object();

                    if (obj["success"].toBool()) {
                        // 清除掉此用户数据
                        m_allUser.remove(username);
                        // 删除此行
                        userModel->removeRow(row);
                        QMessageBox::information(this, tr("删除用户"), tr("已删除用户： %1 ").arg(username));
                    }
                    else {
                        QMessageBox::information(this, tr("删除用户"), tr("删除用户 %1 失败").arg(username));
                    }
                }
                else {
                    QMessageBox::warning(this, tr("删除失败"), tr("删除用户失败: %1").arg(reply->errorString()));
                }
                reply->deleteLater();
            });
        }
    }
}
