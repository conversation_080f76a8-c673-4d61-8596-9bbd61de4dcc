#ifndef FC_DATA_PARSER_H
#define FC_DATA_PARSER_H

#include <QObject>
#include <QString>
#include <QByteArray>
#include <QXmlStreamReader>
#include <QMap>
#include <QVector>

#include "fc_data_structures.h"

/**
 * @brief FC数据解析器
 * 负责解析FC原始数据，包括XML格式的功能单元消息和主题数据
 * 以及运行时的二进制数据解析
 */
class FcDataParser : public QObject
{
    Q_OBJECT

public:
    explicit FcDataParser(QObject *parent = nullptr);
    ~FcDataParser();

    // XML配置数据解析
    bool parseSchemeXml(const QString& xmlContent, QVector<FcSchemeData>& schemes);



    // 运行时数据解析
    struct ParsedFrameData {
        QString messageId;              // 消息ID
        QString sourceFuncId;           // 源功能ID
        QString topicIdentifier;        // 主题标识符
        QMap<QString, QString> topicValues;  // 主题值映射
        QDateTime timestamp;            // 时间戳
        bool isValid;                   // 数据是否有效
        
        ParsedFrameData() : isValid(false) {}
    };

    // 解析原始帧数据
    ParsedFrameData parseRawFrameData(const QByteArray& frameData);
    
    // 解析FC消息数据
    bool parseFcMessageData(const QByteArray& messageData, 
                           const QString& expectedMessageId,
                           QMap<QString, QString>& topicValues);

    // 数据验证
    bool validateXmlStructure(const QString& xmlContent);
    bool validateFrameData(const QByteArray& frameData);

    // 工具方法
    static QString extractMessageIdFromFrame(const QByteArray& frameData);
    static QString extractSourceFuncIdFromFrame(const QByteArray& frameData);
    static QDateTime extractTimestampFromFrame(const QByteArray& frameData);

signals:
    void parseProgress(int percentage);
    void parseError(const QString& errorMessage);
    void parseWarning(const QString& warningMessage);

private:
    // 二进制数据解析辅助方法
    bool parseFrameHeader(const QByteArray& frameData, int& headerSize, QString& messageId);
    bool parseMessagePayload(const QByteArray& payload, const QString& messageId, 
                            QMap<QString, QString>& topicValues);
    bool parseTopicValue(const QByteArray& data, int offset, int length, 
                        const QString& dataType, QString& value);
    
    // 数据转换方法
    QString convertBinaryToString(const QByteArray& data, const QString& dataType);
    double convertBinaryToDouble(const QByteArray& data, const QString& dataType);
    int convertBinaryToInt(const QByteArray& data, const QString& dataType);
    
    // 验证方法
    bool isValidXmlElement(const QString& elementName);
    bool isValidDataType(const QString& dataType);
    bool isValidMessageId(const QString& messageId);
    
    // 错误处理
    void reportError(const QString& context, const QString& error);
    void reportWarning(const QString& context, const QString& warning);
    
    // 内部状态
    int m_currentParseProgress;
    QString m_currentContext;
    QMap<QString, QString> m_knownMessageTypes;  // 已知的消息类型映射
    QMap<QString, QString> m_knownDataTypes;    // 已知的数据类型映射
};

#endif // FC_DATA_PARSER_H
