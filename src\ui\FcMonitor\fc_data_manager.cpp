﻿#include "fc_data_manager.h"
#include "fc_data_parser.h"
#include <QDebug>
#include <QFile>
#include <QXmlStreamReader>
#include <QMutexLocker>
#include <QDateTime>
#include <QTimer>

#include "fc_data_parser.h"
#include "utils/file_list_service.h"
#include "third/parseAndAnalysis.h"

FcDataManager::FcDataManager(QObject *parent)
    : QObject(parent)
    , m_updateTimer(new QTimer(this))
{
    // 设置定时器，用于定期更新数据统计，但不自动启动
    m_updateTimer->setInterval(1000); // 1秒更新一次
    connect(m_updateTimer, &QTimer::timeout, this, &FcDataManager::onDataUpdateTimer);
    // 注意：不自动启动定时器，避免在没有数据时造成不必要的CPU占用
}

FcDataManager::~FcDataManager()
{
    clearAllSchemes();
}

void FcDataManager::addScheme(const FcSchemeData& scheme)
{
    QMutexLocker locker(&m_dataMutex);

    if (!validateSchemeData(scheme)) {
        emit dataError(scheme.schemeName, "方案数据验证失败");
        return;
    }

    // 复制方案数据并构建索引
    FcSchemeData newScheme = scheme;
    newScheme.buildIndices();
    newScheme.schemeStartTime = QDateTime::currentDateTime();

    m_schemes[scheme.schemeName] = newScheme;
    m_monitoringStates[scheme.schemeName] = false;
    m_pausedStates[scheme.schemeName] = false;

    emit schemeDataChanged(scheme.schemeName);
    qDebug() << "Added FC scheme:" << scheme.schemeName << "unit message count:" << scheme.unitMessages.size();
}

FcSchemeData* FcDataManager::getScheme(const QString& schemeName)
{
    QMutexLocker locker(&m_dataMutex);
    return m_schemes.contains(schemeName) ? &m_schemes[schemeName] : nullptr;
}

QStringList FcDataManager::getSchemeNames() const
{
    QMutexLocker locker(&m_dataMutex);
    return m_schemes.keys();
}

void FcDataManager::removeScheme(const QString& schemeName)
{
    QMutexLocker locker(&m_dataMutex);

    if (m_schemes.contains(schemeName)) {
        cleanupScheme(schemeName);
        m_schemes.remove(schemeName);
        m_monitoringStates.remove(schemeName);
        m_pausedStates.remove(schemeName);

        emit schemeDataChanged(schemeName);
        qDebug() << "Removed FC scheme:" << schemeName;
    }
}

void FcDataManager::clearAllSchemes()
{
    QMutexLocker locker(&m_dataMutex);

    for (const QString& schemeName : m_schemes.keys()) {
        cleanupScheme(schemeName);
    }

    m_schemes.clear();
    m_monitoringStates.clear();
    m_pausedStates.clear();
}

//解析方案xml，构造所有方案m_schemes数据
bool FcDataManager::parseXmlData(const QString& xmlPath)
{
    QMutexLocker locker(&m_dataMutex);
    for (const QString& schemeName : m_schemes.keys()) {
        cleanupScheme(schemeName);
    }

    m_schemes.clear();
    m_monitoringStates.clear();
    m_pausedStates.clear();


    // 使用FcDataParser解析XML
    FcDataParser parser;
    QVector<FcSchemeData> schemes;

    // 连接解析进度信号
    connect(&parser, &FcDataParser::parseProgress, this, [this](int progress) {
        qDebug() << "Parsing progress:" << progress << "%";
    });

    connect(&parser, &FcDataParser::parseError, this, [this](const QString& error) {
        qWarning() << "Parsing error:" << error;
    });

    connect(&parser, &FcDataParser::parseWarning, this, [this](const QString& warning) {
        qDebug() << "Parsing warning:" << warning;
    });

    // 解析XML内容，获取所有方案
    if (!parser.parseSchemeXml(xmlPath, schemes)) {
        qWarning() << "Failed to parse XML";
        return false;
    }

    if (schemes.isEmpty()) {
        qWarning() << "No schemes found in XML";
        return false;
    }

    qDebug() << "Successfully parsed XML file, found" << schemes.size() << "schemes";

    // 处理每个解析出的方案
    int addedCount = 0;

    std::string icdVersion;
    void *icdtoken = FileListService::getInstance().getParseandanalysisP();
    icdVersion = getCurrentIcdVersion(icdtoken);


    for (auto& scheme : schemes) {
        // 确保方案名称不为空
        if (scheme.schemeName.isEmpty()) {
            continue;
        }

        if(scheme.icdName == QString::fromStdString(icdVersion))
        {
            // 添加到方案列表中
            m_schemes[scheme.schemeName] = scheme;
            m_monitoringStates[scheme.schemeName] = false;
            m_pausedStates[scheme.schemeName] = false;
        }

    }

    qDebug() << "Successfully added" << addedCount << "schemes to list, total schemes:" << m_schemes.size();

    return m_schemes.size();
}

bool FcDataManager::loadSchemeFromFile(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        emit parseError(QString("无法打开文件: %1").arg(filePath));
        return false;
    }

    QString xmlContent = file.readAll();
    file.close();

    return parseXmlData(filePath);
}

void FcDataManager::updateMessageData(const QString& schemeName, const QString& messageId,
                                     const QMap<QString, QString>& signalValues)
{
    QMutexLocker locker(&m_dataMutex);

    if (!m_schemes.contains(schemeName)) {
        return;
    }

    FcSchemeData& scheme = m_schemes[schemeName];
    FcUnitMessageData* unitMessage = scheme.findUnitMessage(messageId);

    if (!unitMessage) {
        return;
    }

    // 更新消息统计
    unitMessage->messageCount++;
    unitMessage->lastReceiveTime = QDateTime::currentDateTime();

    // 更新信号数据
    for (auto it = signalValues.constBegin(); it != signalValues.constEnd(); ++it) {
        // 信号值格式：topicShortName.signalId = value
        QStringList parts = it.key().split('.');
        if (parts.size() == 2) {
            QString topicShortName = parts[0];
            QString signalId = parts[1];

            // 使用unitMessage的topicData
            FcTopicData* topic = &unitMessage->topicData;
            if (topic && (topic->topicIdentifier == topicShortName || topic->topicName == topicShortName)) {
                FcSignalData* signal = topic->signalIndex.value(signalId, nullptr);
                if (signal) {
                    signal->currentValue = it.value();
                    signal->lastUpdateTime = QDateTime::currentDateTime();
                    signal->isActive = true;

                    // 更新主题状态
                    topic->lastUpdateTime = QDateTime::currentDateTime();
                    topic->isActive = true;

                    emit topicDataUpdated(schemeName, messageId, topicShortName);
                }
            }
        }
    }

    emit messageDataUpdated(schemeName, messageId);
}

void FcDataManager::updateSignalData(const QString& schemeName, const QString& messageId,
                                    const QString& topicId, const QString& signalId, const QString& value)
{
    QMutexLocker locker(&m_dataMutex);

    if (!m_schemes.contains(schemeName)) {
        return;
    }

    FcSchemeData& scheme = m_schemes[schemeName];
    FcUnitMessageData* unitMessage = scheme.findUnitMessage(messageId);

    if (!unitMessage) {
        return;
    }

    // 使用unitMessage的topicData
    FcTopicData* topic = &unitMessage->topicData;
    if (!topic || topic->topicIdentifier != topicId) {
        // 如果topicId不匹配，尝试在独立主题中查找
        topic = scheme.findTopic(topicId);
        if (!topic) {
            return;
        }
    }

    FcSignalData* signal = topic->signalIndex.value(signalId, nullptr);
    if (!signal) {
        return;
    }

    // 更新信号数据
    signal->currentValue = value;
    signal->lastUpdateTime = QDateTime::currentDateTime();
    signal->isActive = true;

    // 更新主题和消息的时间戳
    topic->lastUpdateTime = QDateTime::currentDateTime();
    topic->isActive = true;
    unitMessage->lastReceiveTime = QDateTime::currentDateTime();
    unitMessage->messageCount++;

    emit topicDataUpdated(schemeName, messageId, topicId);
}

void FcDataManager::startMonitoring(const QString& schemeName)
{
    QMutexLocker locker(&m_dataMutex);

    if (m_schemes.contains(schemeName)) {
        m_monitoringStates[schemeName] = true;
        m_pausedStates[schemeName] = false;

        // 设置所有功能单元消息为监控状态
        FcSchemeData& scheme = m_schemes[schemeName];
        for (auto& unitMessage : scheme.unitMessages) {
            unitMessage.isMonitoring = true;
        }
        scheme.updateActiveMessageCount();

        emit monitoringStarted(schemeName);
        qDebug() << "Started monitoring FC scheme:" << schemeName;
    }
}

void FcDataManager::stopMonitoring(const QString& schemeName)
{
    QMutexLocker locker(&m_dataMutex);

    if (m_schemes.contains(schemeName)) {
        m_monitoringStates[schemeName] = false;
        m_pausedStates[schemeName] = false;

        // 停止所有功能单元消息的监控
        FcSchemeData& scheme = m_schemes[schemeName];
        for (auto& unitMessage : scheme.unitMessages) {
            unitMessage.isMonitoring = false;
        }
        scheme.updateActiveMessageCount();

        emit monitoringStopped(schemeName);
        qDebug() << "Stopped monitoring FC scheme:" << schemeName;
    }
}

void FcDataManager::pauseMonitoring(const QString& schemeName)
{
    QMutexLocker locker(&m_dataMutex);

    if (m_schemes.contains(schemeName) && m_monitoringStates[schemeName]) {
        m_pausedStates[schemeName] = true;
        emit monitoringPaused(schemeName);
        qDebug() << "Paused monitoring FC scheme:" << schemeName;
    }
}

void FcDataManager::resumeMonitoring(const QString& schemeName)
{
    QMutexLocker locker(&m_dataMutex);

    if (m_schemes.contains(schemeName) && m_monitoringStates[schemeName]) {
        m_pausedStates[schemeName] = false;
        emit monitoringResumed(schemeName);
        qDebug() << "Resumed monitoring FC scheme:" << schemeName;
    }
}

QVector<FcUnitMessageData> FcDataManager::getActiveUnitMessages(const QString& schemeName) const
{
    QMutexLocker locker(&m_dataMutex);
    QVector<FcUnitMessageData> activeMessages;

    if (m_schemes.contains(schemeName)) {
        const FcSchemeData& scheme = m_schemes[schemeName];
        for (const auto& unitMessage : scheme.unitMessages) {
            if (unitMessage.isMonitoring) {
                activeMessages.append(unitMessage);
            }
        }
    }

    return activeMessages;
}

QVector<FcTopicData> FcDataManager::getActiveTopics(const QString& schemeName) const
{
    QMutexLocker locker(&m_dataMutex);
    QVector<FcTopicData> activeTopics;

    if (m_schemes.contains(schemeName)) {
        const FcSchemeData& scheme = m_schemes[schemeName];
        // 从功能单元消息中获取活跃主题
        for (const auto& unitMessage : scheme.unitMessages) {
            if (unitMessage.isMonitoring && unitMessage.topicData.isActive) {
                activeTopics.append(unitMessage.topicData);
            }
        }
        // 从独立主题中获取活跃主题
        for (const auto& topic : scheme.topics) {
            if (topic.isActive) {
                activeTopics.append(topic);
            }
        }
    }

    return activeTopics;
}

QVector<FcSignalData> FcDataManager::getActiveSignals(const QString& schemeName) const
{
    QMutexLocker locker(&m_dataMutex);
    QVector<FcSignalData> activeSignals;

    if (m_schemes.contains(schemeName)) {
        const FcSchemeData& scheme = m_schemes[schemeName];
        // 从功能单元消息中获取活跃信号
        for (const auto& unitMessage : scheme.unitMessages) {
            if (unitMessage.isMonitoring && unitMessage.topicData.isActive) {
                for (const auto& signal : unitMessage.topicData.signalList) {
                    if (signal.isActive) {
                        activeSignals.append(signal);
                    }
                }
            }
        }
        // 从独立主题中获取活跃信号
        for (const auto& topic : scheme.topics) {
            if (topic.isActive) {
                for (const auto& signal : topic.signalList) {
                    if (signal.isActive) {
                        activeSignals.append(signal);
                    }
                }
            }
        }
    }

    return activeSignals;
}

int FcDataManager::getMessageCount(const QString& schemeName) const
{
    QMutexLocker locker(&m_dataMutex);
    return m_schemes.contains(schemeName) ? m_schemes[schemeName].totalMessages : 0;
}

int FcDataManager::getActiveMessageCount(const QString& schemeName) const
{
    QMutexLocker locker(&m_dataMutex);
    return m_schemes.contains(schemeName) ? m_schemes[schemeName].activeMessages : 0;
}

void FcDataManager::onDataUpdateTimer()
{
    QMutexLocker locker(&m_dataMutex);

    // 定期更新各方案的统计信息
    for (auto& scheme : m_schemes) {
        updateSchemeStatistics(scheme.schemeName);
    }
}

// 这些方法已经不需要了，因为我们使用了新的解析逻辑

bool FcDataManager::validateSchemeData(const FcSchemeData& scheme) const
{
    if (scheme.schemeName.isEmpty()) {
        qWarning() << "方案名称不能为空";
        return false;
    }

    // 验证每个功能单元消息
    for (const auto& unitMessage : scheme.unitMessages) {
        if (!validateUnitMessageData(unitMessage)) {
            return false;
        }
    }

    return true;
}

bool FcDataManager::validateUnitMessageData(const FcUnitMessageData& unitMessage) const
{
    if (unitMessage.messageName.isEmpty()) {
        qWarning() << "功能单元消息名称不能为空";
        return false;
    }

    if (unitMessage.sourceFuncId <= 0) {
        qWarning() << "源功能ID必须大于0";
        return false;
    }

    // 验证主题数据
    if (!unitMessage.topicData.topicName.isEmpty()) {
        if (!validateTopicData(unitMessage.topicData)) {
            return false;
        }
    }

    return true;
}

bool FcDataManager::validateTopicData(const FcTopicData& topic) const
{
    if (topic.topicIdentifier.isEmpty()) {
        qWarning() << "主题shortName不能为空";
        return false;
    }

    // 验证每个信号
    for (const auto& signal : topic.signalList) {
        if (!validateSignalData(signal)) {
            return false;
        }
    }

    return true;
}

bool FcDataManager::validateSignalData(const FcSignalData& signal) const
{
    if (signal.signalIdentifier.isEmpty() || signal.signalName.isEmpty()) {
        qWarning() << "信号ID和信号名称不能为空";
        return false;
    }

    return true;
}

void FcDataManager::cleanupScheme(const QString& schemeName)
{
    // 停止监控
    if (m_monitoringStates.value(schemeName, false)) {
        stopMonitoring(schemeName);
    }

    // 清理相关资源
    qDebug() << "清理FC方案资源:" << schemeName;
}

void FcDataManager::updateSchemeStatistics(const QString& schemeName)
{
    if (!m_schemes.contains(schemeName)) {
        return;
    }

    FcSchemeData& scheme = m_schemes[schemeName];
    scheme.updateActiveMessageCount();

    // 可以在这里添加更多统计信息的更新逻辑
}

QString FcDataManager::generateUniqueUnitMessageId(const FcUnitMessageData& unitMessage) const
{
    return QString("%1_%2_%3").arg(unitMessage.messageName)
                              .arg(unitMessage.sourceFuncId)
                              .arg(unitMessage.pubSubTopicId);
}

QString FcDataManager::generateUniqueTopicId(const FcTopicData& topic, const QString& messageId) const
{
    return QString("%1_%2").arg(messageId, topic.topicIdentifier);
}

QString FcDataManager::generateUniqueSignalId(const FcSignalData& signal, const QString& topicId) const
{
    return QString("%1_%2").arg(topicId, signal.signalIdentifier);
}

SUBSCRIBE_PACKAGE_CMD_SUB FcDataManager::getCurrentSubData(QString schemeName)
{
    SUBSCRIBE_PACKAGE_CMD_SUB data = {};
    FcSchemeData currentScheme ;
    auto schemeData = getScheme(schemeName);
    if(schemeData == nullptr)
    {
        return data;
    }
    if(schemeData->unitMessages.size() == 0)
    {
        return data;
    }

    int ruleCount = schemeData->unitMessages.size();

    for (int i=0;i<schemeData->unitMessages.size();i++) {
        SUBSCRIBE_PACKAGE_SUB_RULE subRule;
        subRule.sourceId = schemeData->unitMessages.at(i).sourceFuncId;
        subRule.topicId = schemeData->unitMessages.at(i).pubSubTopicId;
        data.subRuleVec.push_back(subRule);
    }
    // 如何计算长度
    data.header.packLength = sizeof(SUBSCRIBE_PACKAGE_CMD_SUB) + ruleCount * sizeof(SUBSCRIBE_PACKAGE_SUB_RULE);
    data.header.command = CMD_SUBSCRIBE;
    return data;
}
