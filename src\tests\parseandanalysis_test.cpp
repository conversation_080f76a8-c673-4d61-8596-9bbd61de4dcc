#include "parseandanalysis_test.h"
#include <QDir>
#include <QFileInfo>
#include <QCoreApplication>
#include <QProcess>
#include <QStringList>

ParseAndAnalysisTest::ParseAndAnalysisTest(QObject *parent) : QObject(parent)
{
}

ParseAndAnalysisTest::~ParseAndAnalysisTest()
{
}

// 辅助函数，用于检查DLL是否有效
bool ParseAndAnalysisTest::checkDllValidity(const QString& dllPath)
{
    QProcess dumpbin;
    QString output;
    
    // 尝试使用dumpbin检查DLL（如果系统上有Visual Studio）
    dumpbin.setProgram("dumpbin");
    QStringList args;
    args << "/EXPORTS" << dllPath;
    dumpbin.setArguments(args);
    
    dumpbin.start();
    if (dumpbin.waitForStarted() && dumpbin.waitForFinished()) {
        output = dumpbin.readAllStandardOutput();
        qDebug() << "DLL exports according to dumpbin:";
        qDebug() << output;
        return !output.isEmpty();
    }
    
    // 如果没有dumpbin，使用file命令检查文件类型
    dumpbin.setProgram("file");
    args.clear();
    args << dllPath;
    dumpbin.setArguments(args);
    
    dumpbin.start();
    if (dumpbin.waitForStarted() && dumpbin.waitForFinished()) {
        output = dumpbin.readAllStandardOutput();
        qDebug() << "File type according to 'file' command:";
        qDebug() << output;
        return output.contains("PE32") || output.contains("DLL");
    }
    
    // 如果都不行，至少检查文件大小是否合理
    QFileInfo fileInfo(dllPath);
    qint64 size = fileInfo.size();
    qDebug() << "DLL file size:" << size << "bytes";
    return size > 10000; // 合理的DLL文件至少应该有10KB以上
}

void ParseAndAnalysisTest::initTestCase()
{
    qDebug() << "Starting ParseAndAnalysis tests";
    
    // 尝试多个可能的DLL路径
    // QStringList possiblePaths;
    // possiblePaths
    //     << QCoreApplication::applicationDirPath() + "/libparseAndAnalysis.dll"
    //     << QDir::currentPath() + "/libparseAndAnalysis.dll"
    //     << QDir::currentPath() + "/third/libparseAndAnalysis.dll"
    //     << QCoreApplication::applicationDirPath() + "/../third/libparseAndAnalysis.dll"
    //     << QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "/libparseAndAnalysis.dll";
        
    // // 在调试模式下输出当前目录和应用程序目录，帮助诊断路径问题
    // qDebug() << "Current path:" << QDir::currentPath();
    // qDebug() << "Application dir:" << QCoreApplication::applicationDirPath();
    
    // QString dllPath;
    // bool dllFound = false;
    
    // // 检查每个可能的路径
    // for (const QString &path : possiblePaths) {
    //     QFileInfo dllFile(path);
    //     if (dllFile.exists() && dllFile.isFile()) {
    //         dllPath = path;
    //         dllFound = true;
    //         qDebug() << "Found DLL at:" << dllPath;
            
    //         // 检查DLL是否有效
    //         if (checkDllValidity(dllPath)) {
    //             qDebug() << "DLL appears to be valid.";
    //         } else {
    //             qWarning() << "DLL found but may not be valid. Will attempt to load anyway.";
    //         }
    //         break;
    //     }
    // }
    
    // // 如果找不到DLL，则测试失败
    // QVERIFY2(dllFound, "libparseAndAnalysis.dll not found in any of the searched paths!");
    
#ifdef PARSE_ANALYSIS_DLL_LOAD
    // 动态加载DLL，使用单例模式
    // bool loaded = DllParseandanalysis::getInstance().init(dllPath);
    bool loaded = DllParseandanalysis::getInstance().init("libparseAndAnalysis.dll");
    
    // 即使加载失败，也尝试继续测试 - 只要有add函数可用就行
    if (!loaded) {
        qWarning() << "DLL functions only partially loaded - some tests may fail.";
        
        // 尝试调用add函数检查是否可用
        bool addFunctionWorks = false;
        try {
            int result = DllParseandanalysis::getInstance().add(1, 1);
            addFunctionWorks = (result == 2);
            qInfo() << "Basic 'add' function is available, continuing with limited testing.";
        } catch(const std::exception&) {
            QSKIP("Essential function 'add' not available. Skipping all tests.");
        }
    }
#endif
}

void ParseAndAnalysisTest::cleanupTestCase()
{
    // 如果解码器已初始化，则销毁它
    if (m_decoderToken) {
#ifdef PARSE_ANALYSIS_DLL_LOAD
        try {
            int result = DllParseandanalysis::getInstance().destroyDecoder(m_decoderToken);
            QVERIFY2(result == 0, "Failed to destroy decoder");
        } catch(const std::exception&) {
            qWarning() << "destroyDecoder function not available. Skipping cleanup.";
        }
#else
        int result = destroyDecoder(m_decoderToken);
        QVERIFY2(result == 0, "Failed to destroy decoder");
#endif
        m_decoderToken = nullptr;
    }
    
#ifdef PARSE_ANALYSIS_DLL_LOAD
    // 释放DLL（注意：在单例模式下，我们通常会让单例的析构函数负责释放，
    // 但这里为了明确测试结束时的行为，手动调用release）
    DllParseandanalysis::getInstance().release();
#endif

    qDebug() << "ParseAndAnalysis tests completed";
}

void ParseAndAnalysisTest::testAdd()
{
    // 测试基础的add函数
    int result;
    
#ifdef PARSE_ANALYSIS_DLL_LOAD
    try {
        result = DllParseandanalysis::getInstance().add(3, 5);
        QCOMPARE(result, 8);
        
        result = DllParseandanalysis::getInstance().add(-2, 7);
        QCOMPARE(result, 5);
        
        result = DllParseandanalysis::getInstance().add(0, 0);
        QCOMPARE(result, 0);
    } catch(const std::exception& e) {
        QFAIL(qPrintable(QString("add function failed: %1").arg(e.what())));
    }
#else
    result = add(3, 5);
    QCOMPARE(result, 8);
    
    result = add(-2, 7);
    QCOMPARE(result, 5);
    
    result = add(0, 0);
    QCOMPARE(result, 0);
#endif
}

void ParseAndAnalysisTest::testInitDecoder()
{
#ifdef PARSE_ANALYSIS_DLL_LOAD
    // 检查函数是否可用
    bool decoderFunctionsAvailable = true;
    try {
        // 尝试调用函数检查是否会抛出异常
        DllParseandanalysis::getInstance().initDecoder(m_decoderToken);
    } catch(const std::exception&) {
        decoderFunctionsAvailable = false;
    }
    
    if (!decoderFunctionsAvailable) {
        QSKIP("Decoder functions not available. Skipping test.");
    }
    
    // 重置token避免重复初始化
    m_decoderToken = nullptr;
#endif

    // 初始化解码器
    int result;
    
#ifdef PARSE_ANALYSIS_DLL_LOAD
    try {
        result = DllParseandanalysis::getInstance().initDecoder(m_decoderToken);
        QVERIFY2(result == 0, "Failed to initialize decoder");
        QVERIFY2(m_decoderToken != nullptr, "Decoder token is null after initialization");
        
        // 测试当前ICD版本获取
        std::string version = DllParseandanalysis::getInstance().getCurrentIcdVersion(m_decoderToken);
        qDebug() << "Current ICD version:" << QString::fromStdString(version);
        QVERIFY(!version.empty());
    } catch(const std::exception& e) {
        QFAIL(qPrintable(QString("Decoder functions failed: %1").arg(e.what())));
    }
#else
    result = initDecoder(m_decoderToken);
    QVERIFY2(result == 0, "Failed to initialize decoder");
    QVERIFY2(m_decoderToken != nullptr, "Decoder token is null after initialization");
    
    // 测试当前ICD版本获取
    std::string version = getCurrentIcdVersion(m_decoderToken);
    qDebug() << "Current ICD version:" << QString::fromStdString(version);
    QVERIFY(!version.empty());
#endif
}

void ParseAndAnalysisTest::testGetFuncUnitList()
{
#ifdef PARSE_ANALYSIS_DLL_LOAD
    // 检查函数是否可用
    bool funcUnitListAvailable = true;
    try {
        // 仅测试函数可用性，不传递参数
        DllParseandanalysis::getInstance().getFuncUnitList(m_decoderToken, *(new std::vector<FuncUnit>()));
    } catch(const std::exception&) {
        funcUnitListAvailable = false;
    }
    
    if (!funcUnitListAvailable) {
        QSKIP("getFuncUnitList function not available. Skipping test.");
    }
#endif

    // 确保解码器已初始化
    if (!m_decoderToken) {
#ifdef PARSE_ANALYSIS_DLL_LOAD
        bool initDecoderAvailable = true;
        try {
            int result = DllParseandanalysis::getInstance().initDecoder(m_decoderToken);
            QVERIFY2(result == 0, "Failed to initialize decoder");
        } catch(const std::exception&) {
            initDecoderAvailable = false;
        }
        
        if (!initDecoderAvailable) {
            QSKIP("initDecoder function not available. Skipping test.");
        }
#else
        int result = initDecoder(m_decoderToken);
        QVERIFY2(result == 0, "Failed to initialize decoder");
#endif
    }
    
    // 获取功能单元列表
    std::vector<FuncUnit> funcUnitVec;
    int result;
    
#ifdef PARSE_ANALYSIS_DLL_LOAD
    try {
        result = DllParseandanalysis::getInstance().getFuncUnitList(m_decoderToken, funcUnitVec);
        // 验证调用成功
        QVERIFY2(result == 0, "getFuncUnitList failed");
        
        // 打印获取的功能单元信息
        qDebug() << "Found" << funcUnitVec.size() << "functional units:";
        for (const auto& unit : funcUnitVec) {
            qDebug() << "  - Name:" << QString::fromStdString(unit.name)
                    << ", Short Name:" << QString::fromStdString(unit.shortName);
        }
    } catch(const std::exception& e) {
        QFAIL(qPrintable(QString("getFuncUnitList failed: %1").arg(e.what())));
    }
#else
    //解析库修改，该用法参数不对 暂时屏蔽
    //result = getFuncUnitList(m_decoderToken, funcUnitVec);
    // 验证调用成功
    QVERIFY2(result == 0, "getFuncUnitList failed");
    
    // 打印获取的功能单元信息
    qDebug() << "Found" << funcUnitVec.size() << "functional units:";
    for (const auto& unit : funcUnitVec) {
        qDebug() << "  - Name:" << QString::fromStdString(unit.name)
                << ", Short Name:" << QString::fromStdString(unit.shortName);
    }
#endif
}

void ParseAndAnalysisTest::testSearchInterfaces()
{
#ifdef PARSE_ANALYSIS_DLL_LOAD
    // 检查函数是否可用
    bool searchFunctionAvailable = true;
    try {
        // 仅测试函数可用性
        const char* testTerm = "test";
        DllParseandanalysis::getInstance().searchInterfacesByNameFuzzyMatch(m_decoderToken, testTerm, *(new std::vector<Interface>()));
    } catch(const std::exception&) {
        searchFunctionAvailable = false;
    }
    
    if (!searchFunctionAvailable) {
        QSKIP("searchInterfacesByNameFuzzyMatch function not available. Skipping test.");
    }
#endif

    // 确保解码器已初始化
    if (!m_decoderToken) {
#ifdef PARSE_ANALYSIS_DLL_LOAD
        bool initDecoderAvailable = true;
        try {
            int result = DllParseandanalysis::getInstance().initDecoder(m_decoderToken);
            QVERIFY2(result == 0, "Failed to initialize decoder");
        } catch(const std::exception&) {
            initDecoderAvailable = false;
        }
        
        if (!initDecoderAvailable) {
            QSKIP("initDecoder function not available. Skipping test.");
        }
#else
        int result = initDecoder(m_decoderToken);
        QVERIFY2(result == 0, "Failed to initialize decoder");
#endif
    }
    
    // 使用模糊匹配搜索接口
    const char* searchTerm = "data";  // 使用一个通用术语测试搜索
    std::vector<Interface> interfaceVec;
    int result;
    
#ifdef PARSE_ANALYSIS_DLL_LOAD
    try {
        result = DllParseandanalysis::getInstance().searchInterfacesByNameFuzzyMatch(m_decoderToken, searchTerm, interfaceVec);
        
        // 验证调用成功
        QVERIFY2(result == 0, "searchInterfacesByNameFuzzyMatch failed");
        
        // 打印搜索结果
        qDebug() << "Found" << interfaceVec.size() << "interfaces matching '" << searchTerm << "':";
        for (const auto& iface : interfaceVec) {
            qDebug() << "  - Interface:" << QString::fromStdString(iface.name)
                    << ", Short Name:" << QString::fromStdString(iface.shortName)
                    << ", Format:" << QString::fromStdString(iface.format);
        }
    } catch(const std::exception& e) {
        QFAIL(qPrintable(QString("searchInterfacesByNameFuzzyMatch failed: %1").arg(e.what())));
    }
#else
    result = searchInterfacesByNameFuzzyMatch(m_decoderToken, searchTerm, interfaceVec);
    
    // 验证调用成功
    QVERIFY2(result == 0, "searchInterfacesByNameFuzzyMatch failed");
    
    // 打印搜索结果
    qDebug() << "Found" << interfaceVec.size() << "interfaces matching '" << searchTerm << "':";
    for (const auto& iface : interfaceVec) {
        qDebug() << "  - Interface:" << QString::fromStdString(iface.name)
                << ", Short Name:" << QString::fromStdString(iface.shortName)
                << ", Format:" << QString::fromStdString(iface.format);
    }
#endif
}

// 只有当进行独立测试运行时才包含main函数
#ifdef STANDALONE_TEST
// 添加程序主入口，使测试可独立运行
QTEST_MAIN(ParseAndAnalysisTest)
#endif 
