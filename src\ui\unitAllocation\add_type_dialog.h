#ifndef ADD_TYPE_DIALOG_H
#define ADD_TYPE_DIALOG_H

#include <QDialog>
#include <QLineEdit>
#include <QPushButton>

class AddTypeDialog : public QDialog
{
    Q_OBJECT

public:
    explicit AddTypeDialog(QWidget *parent = nullptr);
    QString getType() const;
    QString getCode() const;
    QString getName() const;
    QString getCoefficient() const;

private:
    void setupUI();
    void setupConnections();

    QLineEdit *typeEdit;
    QLineEdit *codeEdit;
    QLineEdit *nameEdit;
    QLineEdit *coefficientEdit;
    QPushButton *confirmButton;
    QPushButton *cancelButton;
};

#endif // ADD_TYPE_DIALOG_H 