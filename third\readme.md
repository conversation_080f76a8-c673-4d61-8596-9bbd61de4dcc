## 导入

-   `include`下为要导入的头文件，其中`parseAndAnalysis.h`为接口入口
-   `debug`和`release`中为解析插件的debug与release版本。

## 接口使用

### 初始化

在进行初始化之前必须加载icd文件，统一使用json格式，文件需要存于本地目录，本地若无对应版本则需要从接口服务中获取。

```cpp
/**
 * @brief 加载icd-json文件
 * @param file json文件(包含路径)
 * @return
 */
MY_LIBRARY_API int loadIcdJson(const std::string& file);
```

初始化decoder指针，后续的接口都需要传入decoder指针做为参数

```cpp
/**
 * @brief 初始化decoder对象指针
 * @param token decoder对象指针
 * @return
 */
MY_LIBRARY_API int initDecoder(void*& token);
```

销毁decoder指针

```cpp
/**
 * @brief 销毁decoder对象指针
 * @param token decoder对象指针
 * @return
 */
MY_LIBRARY_API int destroyDecoder(void*& token);
```

### 方案相关

#### 新增方案

在选择完成icd版本后，进入监控界面或者新建方案窗口时需要调用功能单元列表接口，将功能单元与服务展示在左侧。

```cpp
/**
 * @brief 获取功能单元列表
 * @param token [in]  decoder对象指针
 * @param funcUnitVec [out] 功能单元列表
 * @return 0-成功
 */
MY_LIBRARY_API int getFuncUnitList(void*& token, std::vector<FuncUnit>& funcUnitVec);
```

点击功能单元下的服务，调用服务接口。

```cpp
/**
 * brief 根据服务名称获取接口
 * @param token [in] decoder对象指针
 * @param serviceName [in] 服务名称
 * @param interfaceVec [out] 接口列表
 * @return 0-成功
 */
MY_LIBRARY_API int getInterfacesByServiceName(void*& token, const std::string& serviceName, std::vector<Interface>& interfaceVec);

/**
 * @brief 根据服务缩略名名称获取接口
 * @param token [in] decoder对象指针
 * @param serviceShortName [in] 服务缩略名
 * @param interfaceVec [out] 接口列表
 * @return 0-成功
 */
MY_LIBRARY_API int getInterfacesByServiceShortName(void*& token, const std::string& serviceShortName, std::vector<Interface>& interfaceVec);

```

选择具体的接口后，可根据接口下的主题缩略名获取主题详情。

```cpp
/**
 * @brief 根据主题缩略名获取主题详情
 * @param token [in] decoder对象指针
 * @param shortName [in] 主题缩略名
 * @param topicInfo [out] 接口列表
 * @return
 */
MY_LIBRARY_API int getTopicInfoByShortName(void*& token, const std::string& shortName, Topic& topicInfo);
```

搜索时可调用对应的查询接口

```cpp
/**
 * @brief 根据接口名称(部分)查询接口
 * @param token
 * @param interfaceName
 * @param interfaceVec
 * @return
 */
MY_LIBRARY_API int searchInterfacesByNameFuzzyMatch(void*& token, const std::string& interfaceName, std::vector<Interface>& interfaceVec);
```

### icd上传

在icd上传功能时，需要先将用户上传的icd解析生成对应的json，在通过接口服务上传到服务器

```cpp
/**
 * @brief 解析icd文件，生成json文件
 * @param icdFile icd文件地址，绝对路径
 * @param outputPath 生成的数据文件的路径
 * @param version OUT icd文件的版本，默认使用icd文件内的<版本>标签
 * @return 0~成功 -1~失败
 */
MY_LIBRARY_API int parseIcdAndGenerateDataFile(const std::string& icdFile,const std::string& outputPath, std::string& version);
```

### 数据解析相关

收到数据后需要先将数据反序列化。

```cpp
/**
 * @brief 反序列化数据，同时会解析变长数据
 * @param token     decoder对象指针
 * @param buffer    数据负载
 * @param data      反序列化后的数据
 * @return
 */
MY_LIBRARY_API int unpackMuseData(void*& token, const DataBuffer& buffer, UnpackData& data);
```

根据`data.exeEvFrameHeader.serializedTag`，为0则是定长数据，需要进行定长数据解析；变长数据则从`data.m_signalResult`

```cpp
/**
 * @brief 解析定长数据
 * @param token decoder对象指针
 * @param data  反序列化后的数据
 * @param result 解析结果
 * @return
 */
MY_LIBRARY_API int parseFixedLengthData(void*& token, const UnpackData& data, std::vector<SignalData>& result);
```