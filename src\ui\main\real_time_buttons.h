#ifndef REAL_TIME_BUTTONS_H
#define REAL_TIME_BUTTONS_H

#include <QWidget>
#include <QPushButton>
#include <QButtonGroup>
#include <QVBoxLayout>

class RealTimeButtons
{
public:
    static void createMonitorButtons(QWidget* parent, QVBoxLayout* sideMenuLayout, QPushButton* homeButton,
                                   QButtonGroup*& buttonGroup,
                                   QPushButton*& valueMonitorButton,
                                   QPushButton*& graphMonitorButton,
                                   QPushButton*& timeSeriesButton,
                                   QPushButton*& dataLoadButton,
                                   QPushButton*& unitConfigButton);

private:
    static QString getButtonStyle();
};

#endif // REAL_TIME_BUTTONS_H 