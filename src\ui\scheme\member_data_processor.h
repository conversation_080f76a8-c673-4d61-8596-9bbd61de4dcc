#ifndef MEMBER_DATA_PROCESSOR_H
#define MEMBER_DATA_PROCESSOR_H

#include <QObject>
#include <QMutex>
#include <QHash>
#include <QMap>
#include <QByteArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QThreadPool>
#include <QElapsedTimer>
#include <QtConcurrent>
// 避免循环引用，直接包含成员数据结构定义
#include "member_data.h"

class MemberDataProcessor : public QObject {
    Q_OBJECT
public:
    explicit MemberDataProcessor(QObject* parent = nullptr);
    ~MemberDataProcessor();
    
public slots:
    // 处理单个成员数据更新
    void processMemberUpdate(const QString& memberId, const MemberData& data);
    
    // 处理批量成员数据更新（主要使用场景）
    void processBatchUpdate(const QMap<QString, MemberData>& updates);
    
    // 处理TCP/网络接收的原始数据包
    void processRawDataPacket(const QByteArray& packet);
    
signals:
    // 优化的信号 - 只在数据有实际变化时发出
    void memberDataChanged(const QString& memberId, const MemberData& newData);
    void batchDataChanged(const QMap<QString, MemberData>& changedData);
    
    // 统计和诊断信号
    void processingStats(int totalUpdates, int actualChanges, double milliseconds);
    void processingError(const QString& errorMessage);
    
private:
    // 快速处理原始数据包并提取成员数据
    QMap<QString, MemberData> extractMemberDataFromPacket(const QByteArray& packet);
    
    // 检测实际变化
    QMap<QString, MemberData> filterSignificantChanges(const QMap<QString, MemberData>& updates);
    
    // 计算变化统计
    void updateStatistics(int totalCount, int changedCount, qint64 processingTime);
    
    // 内部缓存
    QMutex dataMutex;
    QHash<QString, MemberData> memberDataCache;
    
    // 性能统计
    QElapsedTimer perfTimer;
    int totalProcessedCount;
    int totalChangedCount;
    qint64 totalProcessingTime;
    
    // 本地线程池（用于并行处理大量数据）
    QThreadPool localThreadPool;
};

#endif // MEMBER_DATA_PROCESSOR_H 