#ifndef USER_SESSION_H
#define USER_SESSION_H

#include <QString>
#include <QJsonObject>
#include <QDateTime>
#include <QMap>
#include <QVariant>
#include <QThread>

/**
 * @brief 用户会话数据管理单例类
 *
 * 这个类用于保存用户登录后的会话数据，并提供统一的访问接口。
 * 使用单例模式设计，确保系统中只有一个用户会话实例。
 */
class UserSession {
public:
    /**
     * @brief 获取单例实例
     * @return UserSession的单例对象引用
     */
    static UserSession& getInstance();

    // 禁止拷贝和移动
    UserSession(const UserSession&) = delete;
    UserSession& operator=(const UserSession&) = delete;
    UserSession(UserSession&&) = delete;
    UserSession& operator=(UserSession&&) = delete;

    // 设置用户信息
    /**
     * @brief 设置完整的用户信息对象
     * @param userInfo JSON对象，包含用户的所有信息
     */
    void setUserInfo(const QJsonObject& userInfo);

    /**
     * @brief 设置用户令牌
     * @param token 用户的认证令牌
     */
    void setToken(const QString& token);

    /**
     * @brief 设置用户名
     * @param username 用户名
     */
    void setUsername(const QString& username);

    /**
     * @brief 设置用户ID
     * @param userId 用户唯一标识符
     */
    void setUserId(int userId);

    /**
     * @brief 设置用户角色
     * @param role 用户角色名称
     */
    void setUserRole(const QString& role);

    /**
     * @brief 添加自定义用户属性
     * @param key 属性名
     * @param value 属性值
     */
    void setCustomProperty(const QString& key, const QVariant& value);

    /**
     * @brief 设置数据处理线程
     * @param thread 数据处理线程指针
     */
    void setDataProcessorThread(QThread* thread);

    // 获取用户信息
    /**
     * @brief 获取用户认证令牌
     * @return 用户令牌字符串
     */
    QString getToken() const;

    /**
     * @brief 获取用户名
     * @return 用户名字符串
     */
    QString getUsername() const;

    /**
     * @brief 获取用户ID
     * @return 用户ID整数
     */
    int getUserId() const;

    /**
     * @brief 获取用户角色
     * @return 用户角色字符串
     */
    QString getUserRole() const;

    /**
     * @brief 获取自定义用户属性
     * @param key 属性名
     * @param defaultValue 默认值，如果属性不存在则返回此值
     * @return 属性值
     */
    QVariant getCustomProperty(const QString& key, const QVariant& defaultValue = QVariant()) const;

    /**
     * @brief 获取完整的用户信息对象
     * @return 包含所有用户数据的JSON对象
     */
    QJsonObject getAllUserInfo() const;

    /**
     * @brief 获取数据处理线程
     * @return 数据处理线程指针
     */
    QThread* getDataProcessorThread() const;

    // 会话管理
    /**
     * @brief 检查用户是否已登录
     * @return 如果用户已登录则返回true，否则返回false
     */
    bool isLoggedIn() const;

    /**
     * @brief 退出登录，清除所有会话数据
     */
    void logout();

private:
    /**
     * @brief 私有构造函数，防止外部创建实例
     */
    UserSession();

    bool m_isLoggedIn = false;      // 用户是否已登录
    QString m_token;                // 用户认证令牌
    QString m_username;             // 用户名
    int m_userId = -1;              // 用户ID
    QString m_userRole;             // 用户角色
    QJsonObject m_userInfo;         // 完整的用户信息JSON
    QDateTime m_loginTime;          // 登录时间
    QMap<QString, QVariant> m_customProperties; // 自定义属性
    QThread* m_dataProcessorThread = nullptr; // 数据处理线程
};

#endif // USER_SESSION_H
