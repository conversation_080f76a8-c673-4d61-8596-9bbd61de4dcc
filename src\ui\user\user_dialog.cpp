﻿#include "user_dialog.h"
#include <QMessageBox>

UserDialog::UserDialog(QWidget *parent)
    : QDialog(parent)
    , usernameEdit(new QLineEdit(this))
    , nameEdit(new QLineEdit(this))
    , passwordEdit(new QLineEdit(this))
    , roleComboBox(new QComboBox(this))
    , confirmBtn(new QPushButton("确认", this))
    , cancelBtn(new QPushButton("取消", this))
    , mainLayout(new QVBoxLayout(this))
    , formLayout(new QFormLayout())
    , buttonLayout(new QHBoxLayout())
{
    setupUI();
    setupConnections();
}

UserDialog::~UserDialog()
{

}

void UserDialog::setupUI()
{
    // 设置窗口标题和属性
    setWindowTitle("添加用户");
    setFixedSize(400, 300);
    setModal(true);
    // setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);
    setWindowFlags(Qt::Dialog | Qt::WindowTitleHint | Qt::WindowCloseButtonHint);
    // 设置表单布局
    formLayout->setContentsMargins(5, 5, 5, 5);
    formLayout->setSpacing(10);  // 减小表单项之间的间距

    // 创建并设置每个输入项的标签样式
    QLabel *usernameLabel = new QLabel("账号", this);
    QLabel *nameLabel = new QLabel("姓名", this);
    QLabel *passwordLabel = new QLabel("密码", this);
    QLabel *roleLabel = new QLabel("角色", this);

    // 设置标签样式 - 蓝色背景，白色文本，固定高度
    QString labelStyle = "QLabel { background-color: #1890ff; color: white; padding: 6px; min-width: 60px; min-height: 20px; font-weight: bold; text-align: center; }";
    usernameLabel->setStyleSheet(labelStyle);
    nameLabel->setStyleSheet(labelStyle);
    passwordLabel->setStyleSheet(labelStyle);
    roleLabel->setStyleSheet(labelStyle);

    // 固定标签大小
    usernameLabel->setFixedSize(60, 32);
    nameLabel->setFixedSize(60, 32);
    passwordLabel->setFixedSize(60, 32);
    roleLabel->setFixedSize(60, 32);

    // 设置标签对齐方式
    usernameLabel->setAlignment(Qt::AlignCenter);
    nameLabel->setAlignment(Qt::AlignCenter);
    passwordLabel->setAlignment(Qt::AlignCenter);
    roleLabel->setAlignment(Qt::AlignCenter);

    // 设置输入框样式和大小
    QString inputStyle = "QLineEdit { border: 1px solid #d9d9d9; border-radius: 0; padding: 6px; min-height: 20px; }";
    usernameEdit->setStyleSheet(inputStyle);
    nameEdit->setStyleSheet(inputStyle);
    passwordEdit->setStyleSheet(inputStyle);
    passwordEdit->setEchoMode(QLineEdit::Password);  // 密码输入框显示为圆点

    // 固定输入框高度
    usernameEdit->setFixedHeight(32);
    nameEdit->setFixedHeight(32);
    passwordEdit->setFixedHeight(32);

    // 设置下拉框样式
    roleComboBox->setStyleSheet("QComboBox { border: 1px solid #d9d9d9; border-radius: 0; padding: 6px; min-height: 20px; }");
    roleComboBox->addItem("请选择角色");
    roleComboBox->addItem("管理员");
    roleComboBox->addItem("普通用户");
    roleComboBox->addItem("访客");
    roleComboBox->setFixedHeight(32);

    // 添加表单项
    formLayout->addRow(usernameLabel, usernameEdit);
    formLayout->addRow(nameLabel, nameEdit);
    formLayout->addRow(passwordLabel, passwordEdit);
    formLayout->addRow(roleLabel, roleComboBox);

    // 设置按钮样式
    confirmBtn->setStyleSheet(
        "QPushButton { background-color: #1890ff; color: white; border: none; border-radius: 4px; padding: 6px 16px; font-weight: bold; min-width: 80px; min-height: 32px; }"
        "QPushButton:hover { background-color: #40a9ff; }"
        "QPushButton:pressed { background-color: #096dd9; }"
        );

    cancelBtn->setStyleSheet(
        "QPushButton { background-color: white; color: #595959; border: 1px solid #d9d9d9; border-radius: 4px; padding: 6px 16px; min-width: 80px; min-height: 32px; }"
        "QPushButton:hover { background-color: #fafafa; color: #40a9ff; border-color: #40a9ff; }"
        );

    // 固定按钮高度
    confirmBtn->setFixedHeight(32);
    cancelBtn->setFixedHeight(32);

    // 设置按钮布局
    buttonLayout->addStretch();
    buttonLayout->addWidget(cancelBtn);
    buttonLayout->addWidget(confirmBtn);
    buttonLayout->setSpacing(10);
    buttonLayout->setContentsMargins(20, 10, 20, 20);

    // 设置主布局
    mainLayout->addLayout(formLayout);
    mainLayout->addStretch();
    mainLayout->addLayout(buttonLayout);
    setLayout(mainLayout);

    // 设置整体窗口样式
    setStyleSheet("QDialog { background-color: white; }");
}

void UserDialog::setupConnections()
{
    connect(confirmBtn, &QPushButton::clicked, this, &UserDialog::onConfirmClicked);
    connect(cancelBtn, &QPushButton::clicked, this, &UserDialog::onCancelClicked);
}

void UserDialog::onConfirmClicked()
{
    // 验证输入
    if (usernameEdit->text().isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请输入用户账号");
        usernameEdit->setFocus();
        return;
    }

    if (nameEdit->text().isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请输入用户姓名");
        nameEdit->setFocus();
        return;
    }

    if (passwordEdit->text().isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请输入用户密码");
        passwordEdit->setFocus();
        return;
    }

    if (roleComboBox->currentIndex() == 0) {
        QMessageBox::warning(this, "输入错误", "请选择用户角色");
        roleComboBox->setFocus();
        return;
    }

    // 输入验证通过，接受对话框
    accept();
}

void UserDialog::onCancelClicked()
{
    // 取消对话框
    reject();
}

QString UserDialog::getUsername() const
{
    return usernameEdit->text();
}

QString UserDialog::getName() const
{
    return nameEdit->text();
}

QString UserDialog::getPassword() const
{
    return passwordEdit->text();
}

QString UserDialog::getRole() const
{
    return roleComboBox->currentText();
}
