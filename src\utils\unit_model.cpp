/************************************************
*************************************************
*	COPYRIGHT NOTICE			*
*	Copyright (c) 2025		*
*	All right reserved.			*
*						*
*	@file	UnitModel.cpp				*
*	@brif	单位转换模块			*
*						*
*************************************************
************************************************/

#include <QCoreApplication>
#include <QDebug>
#include "unit_model.h"
//唯一对象的指针
UnitModel* UnitModel::pInst = NULL;
/*!
*@brief			构造函数
*
*
*/
UnitModel::UnitModel(void)
{
    IsAlreadyParsed = false;
    InitPath("");
}
/*!
*@brief			析构函数
*
*
*/
UnitModel::~UnitModel(void)
{
    m_mapIDToName.clear();
    m_mapIDToTypeID.clear();
    m_mapTypeIDToUnitNameList.clear();
}
/*!
*@brief			解析单位文件
*
*@param QString strFullPath	    单位文件所在路径
*/
void UnitModel::InitPath(QString strFullPath)
{
    m_Mutex.lock();
    if (IsAlreadyParsed)
    {
        m_Mutex.unlock();
        return;
    }
    QDir dir;
    QString strPath = QCoreApplication::applicationDirPath();
    strPath += "/ICDUnit.xml";
    m_strUnitFileName = strPath;
    ReadDataFromXml();
    IsAlreadyParsed = true;
    m_Mutex.unlock();
}
/*!
*@brief			获取单实例指针
*
*
*@param return UnitModel*	    单实例指针
*/
UnitModel* UnitModel::Instance()
{
    if (pInst == NULL)
    {
        pInst = new UnitModel();
    }
    return pInst;
}
/*!
*@brief			删除单实例指针
*
*
*
*/
void UnitModel::DeleteInstance()
{
    if (NULL != pInst)
    {
        delete pInst;
        pInst = NULL;
    }
}
/*!
*@brief			解析单位文件
*
*
*
*/
void UnitModel::ReadDataFromXml()
{
    QFile file(m_strUnitFileName);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        qDebug() << "Failed to read unit file:" << m_strUnitFileName;
        return;
    }

    QXmlStreamReader reader(&file);
    QString currentTypeID;
    QString currentUnitID;
    QString currentUnitName;
    bool inUnitType = false;
    bool inUnit = false;

    while (!reader.atEnd() && !reader.hasError())
    {
        QXmlStreamReader::TokenType token = reader.readNext();

        if (token == QXmlStreamReader::StartElement)
        {
            QString elementName = reader.name().toString();

            if (elementName == "单位类型")
            {
                inUnitType = true;
                // Get the ID attribute from 单位类型 element
                QXmlStreamAttributes attributes = reader.attributes();
                if (attributes.hasAttribute("ID"))
                {
                    currentTypeID = attributes.value("ID").toString();
                }
            }
            else if (elementName == "单位" && inUnitType)
            {
                inUnit = true;
                currentUnitID.clear();
                currentUnitName.clear();
            }
            else if (elementName == "ID" && inUnit)
            {
                currentUnitID = reader.readElementText();
            }
            else if (elementName == "显示名称" && inUnit)
            {
                currentUnitName = reader.readElementText();
            }
        }
        else if (token == QXmlStreamReader::EndElement)
        {
            QString elementName = reader.name().toString();

            if (elementName == "单位类型")
            {
                inUnitType = false;
                currentTypeID.clear();
            }
            else if (elementName == "单位" && inUnit)
            {
                inUnit = false;

                // Process the unit data if we have both ID and name
                if (!currentUnitID.isEmpty() && !currentUnitName.isEmpty() && !currentTypeID.isEmpty())
                {
                    m_mapIDToName[currentUnitID] = currentUnitName;
                    m_mapNameToID[currentUnitName] = currentUnitID.toUInt();
                    m_mapIDToTypeID[currentUnitID] = currentTypeID;
                    m_mapTypeIDToUnitNameList[currentTypeID].append(currentUnitName);
                }
            }
        }
    }

    if (reader.hasError())
    {
        qDebug() << "XML parsing error:" << reader.errorString();
    }

    file.close();
}
/*!
*@brief			根据单位ID获取单位名
*
*@param QString strID	    单位ID
*
*@param return QString	    单位名
*/
QString UnitModel::GetNameFromID(QString strID)
{
    //
    if(m_mapIDToName.count(strID) > 0)
    {
        return m_mapIDToName[strID];
    }
    else
    {
        return "";
    }
}
/*!
*@brief			根据单位ID获取单位所在类型的单位名称列表
*
*@param QString strID	    单位ID
*
*@param return QStringList	    单位名称列表
*/
QStringList UnitModel::GetUnitNameListByUnitID(QString strID)
{
    QString TypeID = m_mapIDToTypeID[strID];
    return m_mapTypeIDToUnitNameList[TypeID];
}
/*!
*@brief			根据单位名获取单位ID
*
*@param QString strID	    单位名
*
*@param return unsigned int	    单位ID
*/
unsigned int UnitModel::GetUnitIDFromName(QString UnitName)
{
    return m_mapNameToID[UnitName];
}


//派
#define  D_PI     3.14159265358979324

const double unitrate_length[7] =
{
    1.0/0.3048,    // ft
    1.0,           // m
    1.0/1852,      // N_m
    12.0/0.3048,   // in
    0.001,         // km
    1.0,           // n/a
    1000.0,        // mm
};

const double unitrate_velocity[7] =
{
    1.0/0.3048,    // ft/s
    3600.0/1852,   // knt
    1.0,           // m/s
    60.0/0.3048,   // ft/m
    3.6,           // km/h
    1000.0,        // mm/s
    1.0/340.29     // Mach
};

const double unitrate_angle[6] =
{
    D_PI/180,      // Rad
    1.0,           // Deg
    1.0,           // n/a(Dms)
    D_PI*1000/180, // mrad
    1.0/180,        // S_c
    1.0
};

const double unitrate_temperature[4][2] =
{
    { 1.8, 0.0    }, // Ran
    { 1.0, 273.15 }, // Cel
    { 1.8, 459.67 }, // Far
    { 1.0, 0.0    }  // Kel
};

const double unitrate_pressure[12] =
{
    1.0,          // Atm
    29.921,       // I_Hg
    76.0,         // C_Hg
    14.696,       // psi
    1013.25,      // Mbar
    10332.0,      // kg/m2
    103320000.0,  // kg/cm2
    1013.25,      // hPa
    1.03322745,      // kgf/cm2
    101.325,      // kPa
    0.101325,     // MPa
    101325
};

const double unitrate_time[8] =
{
    1.0,          // Sec
    20.0,         // Cyc
    1.0,          // n/a(Hms)
    1000.0,       // M_sc
    1000000.0,    // Mic
    1.0/3600,     // h
    1.0/60,       // Min
    1000000000.0  // NS
};

const double unitrate_mass[3] =
{
    2.2046,       // Lbr
    1.0,          // kg
    1000.0           // g
};

const double unitrate_anglevel[5] =
{
    1.0,          // D/s
    1.0/180,      // S/s
    D_PI/180,     // R/s
    D_PI*1000/180,// mrad/s
    3600          // D/h
};

const double unitrate_accel[3] =
{
    1.0/0.3048,   // ft/s2
    1.0,          // m/s2
    1.0/9.8067    // g
};

const double unitrate_frequence[3] =
{
    1.0,          // Hz
    0.001,        // KHz
    0.000001      // MHz
};

const double unitrate_volume[2] =
{
    1.0,          // l
    1000          // ml
};

const double unitrate_angleacc[5] =
{
    1.0,          // D/s2
    1.0/180,      // S_c/s2
    D_PI/180,     // R/s2
    D_PI*1000/180,// mrad/s2
    3600.0        // Sec/s2
};

const double unitrate_memory[3] =
{
    1.0*0x100000, // b
    1.0*0x400,    // Kb
    1.0           // Mb
};

#define PROCESS_UNITCLASS(i, table) \
case i: \
{ \
    int size = sizeof(table) / sizeof(double); \
    if (tin < size && tout < size) \
    return value * table[tout] / table[tin]; \
    break; \
    }

#define PROCESS_UNITCLASS2(i, table) \
case i: \
{ \
    int size = sizeof(table) / (sizeof(double) * 2); \
    if (tin < size && tout < size) \
    return (value + table[tin][1]) * table[tout][0] / table[tin][0] - table[tout][1]; \
    break; \
    }
/*!
*@brief			进行单位转换
*
*@param int unit_in	    源单位ID
*@param int unit_out	目的单位ID
*@param double value	值
*
*@param return double	  转换后的值
*/
double UnitModel::unit_change(int unit_in, int unit_out, double value)
{
    int unit_class = unit_in / 100;
    int tin  = unit_in  % 100 - 1;
    int tout = unit_out % 100 - 1;

    if (unit_class == unit_out/100 && tin != tout && tin >= 0 && tout >= 0)
    {
        switch(unit_class)
        {
            PROCESS_UNITCLASS  ( 1, unitrate_length)
                PROCESS_UNITCLASS  ( 2, unitrate_velocity)
                PROCESS_UNITCLASS  ( 3, unitrate_angle)
                PROCESS_UNITCLASS2 ( 4, unitrate_temperature)
                PROCESS_UNITCLASS  ( 5, unitrate_pressure)
                PROCESS_UNITCLASS  ( 6, unitrate_time)
                PROCESS_UNITCLASS  ( 7, unitrate_mass)
                PROCESS_UNITCLASS  ( 8, unitrate_anglevel)
                PROCESS_UNITCLASS  ( 9, unitrate_accel)
                PROCESS_UNITCLASS  (10, unitrate_frequence)
                PROCESS_UNITCLASS  (11, unitrate_volume)
                PROCESS_UNITCLASS  (12, unitrate_angleacc)
                PROCESS_UNITCLASS  (51, unitrate_memory)
        }
    }

    return value;
}
