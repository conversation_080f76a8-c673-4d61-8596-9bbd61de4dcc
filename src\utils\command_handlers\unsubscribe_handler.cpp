#include "unsubscribe_handler.h"
#include <QDebug>

/**
 * @brief 构造函数
 */
UnsubscribeCommandHandler::UnsubscribeCommandHandler(QObject *parent) 
    : CommandHandler(parent) 
{
    qDebug() << "创建取消订阅命令处理器";
}

/**
 * @brief 析构函数
 */
UnsubscribeCommandHandler::~UnsubscribeCommandHandler() 
{
    qDebug() << "销毁取消订阅命令处理器";
}

/**
 * @brief 处理订阅命令并返回详细结果
 */
CommandHandler::CommandResult UnsubscribeCommandHandler::processCommand(const QByteArray &data)
{
    // 使用基类的新通用命令处理方法，返回详细结果
    return processCommonCommand(data, CMD_SUBSCRIBE, "订阅");
}
