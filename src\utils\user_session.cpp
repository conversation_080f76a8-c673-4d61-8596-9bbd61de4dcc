#include "user_session.h"
#include <QDebug>

/**
 * 获取单例实例
 */
UserSession& UserSession::getInstance() {
    static UserSession instance;
    return instance;
}

/**
 * 私有构造函数
 */
UserSession::UserSession() : m_isLoggedIn(false), m_userId(-1), m_dataProcessorThread(nullptr) {
    qDebug() << "UserSession initialized";
}

/**
 * 设置完整的用户信息对象
 */
void UserSession::setUserInfo(const QJsonObject& userInfo) {
    m_userInfo = userInfo;
    m_isLoggedIn = true;
    m_loginTime = QDateTime::currentDateTime();
    qDebug() << "User session info set, logged in at:" << m_loginTime.toString();
}

/**
 * 设置用户令牌
 */
void UserSession::setToken(const QString& token) {
    m_token = token;
    qDebug() << "User token set, length:" << token.length();
}

/**
 * 设置用户名
 */
void UserSession::setUsername(const QString& username) {
    m_username = username;
    qDebug() << "Username set:" << username;
}

/**
 * 设置用户ID
 */
void UserSession::setUserId(int userId) {
    m_userId = userId;
    qDebug() << "User ID set:" << userId;
}

/**
 * 设置用户角色
 */
void UserSession::setUserRole(const QString& role) {
    m_userRole = role;
    qDebug() << "User role set:" << role;
}

/**
 * 添加自定义用户属性
 */
void UserSession::setCustomProperty(const QString& key, const QVariant& value) {
    m_customProperties[key] = value;
    qDebug() << "Set custom property:" << key << "=" << value.toString();
}

/**
 * 获取用户认证令牌
 */
QString UserSession::getToken() const {
    return m_token;
}

/**
 * 获取用户名
 */
QString UserSession::getUsername() const {
    return m_username;
}

/**
 * 获取用户ID
 */
int UserSession::getUserId() const {
    return m_userId;
}

/**
 * 获取用户角色
 */
QString UserSession::getUserRole() const {
    return m_userRole;
}

/**
 * 获取自定义用户属性
 */
QVariant UserSession::getCustomProperty(const QString& key, const QVariant& defaultValue) const {
    return m_customProperties.value(key, defaultValue);
}

/**
 * 获取完整的用户信息对象
 */
QJsonObject UserSession::getAllUserInfo() const {
    return m_userInfo;
}

/**
 * 检查用户是否已登录
 */
bool UserSession::isLoggedIn() const {
    return m_isLoggedIn;
}

/**
 * 设置数据处理线程
 */
void UserSession::setDataProcessorThread(QThread* thread) {
    m_dataProcessorThread = thread;
    qDebug() << "Data processor thread set";
}

/**
 * 获取数据处理线程
 */
QThread* UserSession::getDataProcessorThread() const {
    return m_dataProcessorThread;
}

/**
 * 退出登录，清除所有会话数据
 * 注意：不清除数据处理线程，由MainWindow负责管理线程生命周期
 */
void UserSession::logout() {
    qDebug() << "Logging out user:" << m_username;
    m_isLoggedIn = false;
    m_token.clear();
    m_username.clear();
    m_userId = -1;
    m_userRole.clear();
    m_userInfo = QJsonObject();
    m_customProperties.clear();
    // 不清除m_dataProcessorThread，由MainWindow负责管理线程生命周期
    qDebug() << "User session cleared (thread references maintained)";
}
