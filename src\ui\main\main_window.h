#ifndef MAIN_WINDOW_H
#define MAIN_WINDOW_H

#include <QMainWindow>
#include <QStackedWidget>
#include <QMenu>
#include <QLabel>
#include <QPushButton>
#include <QHBoxLayout>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QThread>
#include <QButtonGroup>
#include "utils/tcp_data_processor.h"
#include "real_time_buttons.h"

namespace Ui {
class MainWindow;
}

class DocumentWindow;
class MonitorWindow;
class UserManagement;
class PermissionManagement;
class unitAllocation;
class QNetworkReply;
class FcMonitorWidget;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(const QString &username = "admin", QWidget *parent = nullptr);
    ~MainWindow();

signals:
    void logoutSuccess(); // 登出成功信号

protected:
    bool eventFilter(QObject *watched, QEvent *event) override;

private slots:
    void onOverviewButtonClicked();
    void onDocumentButtonClicked();
    void onManagementButtonClicked();
    void onHomeButtonClicked();

    void onValueMonitorTriggered(bool isReviewMode);
    void onGraphMonitorTriggered();
    void onUnitConfigTriggered();
    void onUserManagementTriggered();
    void onPermissionManagementTriggered();
    void onSchemeManagementTriggered();
    void onReplayManagementTriggered(const QDateTime& startTime, const QDateTime& endTime);
    void onTimeSeriesTriggered();
    void onDataLoadTriggered();
    void onLogoutTriggered();
    void onLogoutReplyFinished(QNetworkReply *reply);

    // TCP 数据处理相关槽函数
    void onTcpResponseReceived(const QByteArray &response);
    void onTcpCommandProcessed(SUBSCRIBE_COMMAND cmdType, bool success, const QString &message);
    void onTcpCommandError(SUBSCRIBE_COMMAND cmdType, const QString &errorMessage);

private:
    void setupUI();
    void setupConnections();
    void setupSideMenu();
    void createSubMenus();
    void setupCustomTitleBar();
    void loadPage(int index);
    void initTcpClientAndProcessor();
    void initMonitorButtons();
    void createMonitorButtons();
    void createRealTimeButtons();
    void createReplayButtons();
    void updateSidePanel(bool isMain);

    Ui::MainWindow *ui;
    MonitorWindow *monitorWindow;
    QStackedWidget *contentStack;
    DocumentWindow *documentWindow;
    UserManagement *userManagementWindow;
    unitAllocation *unitAllocationWindow;
    PermissionManagement *permissionManagementWindow;
    FcMonitorWidget *schemeManagementWindow;
    //FcMonitorWidget *m_fcMonitorWindows;

    QMenu *overviewMenu;
    QMenu *managementMenu;
    QMenu *userMenu;

    QPushButton *overviewButton;
    QPushButton *documentButton;
    QPushButton *managementButton;
    QPushButton *homeButton;

    // 实时按钮相关
    QPushButton *valueMonitorButton;
    QPushButton *graphMonitorButton;
    QPushButton *timeSeriesButton;
    QPushButton *dataLoadButton;
    QPushButton *unitConfigButton;
    QButtonGroup *monitorButtonGroup;
    QButtonGroup *mainButtonGroup;
    
    // 回放按钮相关
    QPushButton *replayValueMonitorButton;
    QPushButton *replayGraphMonitorButton;
    QPushButton *replayTimeSeriesButton;
    QPushButton *replayDataLoadButton;
    QPushButton *replayUnitConfigButton;
    QButtonGroup *replayButtonGroup;

    // false 表示实时，true 表示回访
    bool m_isReviewMode = false;

    QLabel *usernameLabel;
    QPushButton *titleBarUsernameLabel;
    QString m_username;
    int currentPageIndex;

    QNetworkAccessManager *networkManager;  // 网络请求管理器
    QThread *m_dataProcessorThread; // 数据处理线程
};

#endif // MAIN_WINDOW_H
