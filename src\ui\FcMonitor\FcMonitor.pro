QT += core widgets

CONFIG += c++17

TARGET = FcMonitorTest
TEMPLATE = app

# 源文件
SOURCES += \
    fc_data_manager.cpp \
    fc_data_parser.cpp \
    fc_monitor_widget.cpp \
    fc_monitor.cpp \
    test_fc_monitor.cpp

# 头文件
HEADERS += \
    fc_data_structures.h \
    fc_data_manager.h \
    fc_data_parser.h \
    fc_monitor_widget.h \
    fc_monitor.h

# UI文件
FORMS += \
    fc_monitor.ui \
    fc_monitor_widget.ui

# 编译选项
QMAKE_CXXFLAGS += -Wall -Wextra

# 输出目录
DESTDIR = ../../../bin
OBJECTS_DIR = ../../../build/obj
MOC_DIR = ../../../build/moc

# 定义
DEFINES += QT_DEPRECATED_WARNINGS

# 如果需要调试信息
CONFIG(debug, debug|release) {
    DEFINES += DEBUG
    TARGET = $$join(TARGET,,,_debug)
}

# 如果需要发布版本优化
CONFIG(release, debug|release) {
    DEFINES += NDEBUG
    QMAKE_CXXFLAGS += -O2
}
