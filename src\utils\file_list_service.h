#ifndef FILE_LIST_SERVICE_H
#define FILE_LIST_SERVICE_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QMutex>
#include <QMap>
#include <QVariant>
#include <QSettings>
#include <QStandardPaths>
#include <QDir>

/**
 * @brief 文件项结构体，存储文件的详细信息
 */
struct FileItem {
    int id;
    QString icdName;
    QString icdVersion;
    QString userVersion;
    QString url;
    QVariant componentVos;
    
    FileItem() : id(0) {}
};

/**
 * @brief 文件列表服务单例类
 *
 * 这个类用于从服务器获取文件列表数据，并提供访问接口。
 * 使用单例模式设计，确保系统中只有一个服务实例。
 */
class FileListService : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 获取单例实例
     * @return FileListService的单例对象引用
     */
    static FileListService& getInstance();

    // 禁止拷贝和移动
    FileListService(const FileListService&) = delete;
    FileListService& operator=(const FileListService&) = delete;
    FileListService(FileListService&&) = delete;
    FileListService& operator=(FileListService&&) = delete;

    ~FileListService();

    /**
     * @brief 从服务器获取文件列表
     * @param callback 获取完成后的回调函数
     */
    void fetchFileList(std::function<void(const QStringList&, bool)> callback);

    /**
     * @brief 获取最近一次获取的文件列表
     * @return 文件名字符串列表
     */
    QStringList getFileList() const;

    /**
     * @brief 检查是否已经获取过文件列表
     * @return 如果已获取过返回true，否则返回false
     */
    bool hasFileList() const;

    /**
     * @brief 强制刷新文件列表
     * @param callback 获取完成后的回调函数
     */
    void refreshFileList(std::function<void(const QStringList&, bool)> callback);

    /**
     * @brief 获取指定文件名的详细信息
     * @param fileName 文件名
     * @return 文件详细信息结构体
     */
    FileItem getFileItemByName(const QString& fileName) const;

    /**
     * @brief 获取所有文件的详细信息
     * @return 文件名到文件详细信息的映射
     */
    QMap<QString, FileItem> getAllFileItems() const;

    /**
     * @brief 获取默认选中的ICD文件名
     * @return 默认选中的ICD文件名，如果没有则返回空字符串
     */
    QString getDefaultSelectedIcdFile() const;

    /**
     * @brief 设置默认选中的ICD文件名
     * @param fileName 要设置为默认选中的ICD文件名
     */
    void setDefaultSelectedIcdFile(const QString& fileName);

    /**
     * @brief 生成操作icd解析库的操作指针
     * @param fileName ICD文件名
     * @return dll指针是否创建成功（int 类型）
     */
    int createParseandanalysisP(const QString& fileName);

    /**
     * @brief 获取操作icd解析库的操作指针
     */
    void* getParseandanalysisP();

    /**
     * @brief 获取icd版本
     */
    std::string GetICDVersion();


signals:
    /**
     * @brief 默认选中的ICD文件发生变化时发出的信号
     * @param fileName 新的默认选中ICD文件名
     */
    void defaultSelectedIcdFileChanged(const QString& fileName);

private:
    /**
     * @brief 私有构造函数，防止外部创建实例
     */
    FileListService();

    /**
     * @brief 处理网络请求的响应
     * @param reply 网络请求的响应对象
     */
    void handleNetworkReply(QNetworkReply* reply);

    /**
     * @brief 从配置文件中加载默认选中的ICD文件
     */
    void loadDefaultSelectedIcdFile();

    /**
     * @brief 将默认选中的ICD文件保存到配置文件
     */
    void saveDefaultSelectedIcdFile();

    QNetworkAccessManager* m_networkManager;
    QStringList m_fileList;           // 存储文件名列表
    QMap<QString, FileItem> m_fileItems; // 存储文件名到文件详细信息的映射
    bool m_hasFileList;               // 标记是否已获取过文件列表
    mutable QMutex m_mutex;           // 同步锁，防止多线程并发访问
    QString m_defaultSelectedIcdFile; // 默认选中的ICD文件名
    std::string m_icd_version;        // icd 版本

    // 回调函数列表，用于存储等待响应的回调
    QList<std::function<void(const QStringList&, bool)>> m_pendingCallbacks;

    void *m_parseandanalysisP = nullptr; // 操作icd解析功能的指针
};

#endif // FILE_LIST_SERVICE_H 
