#ifndef DATAJOINTYPE_H
#define DATAJOINTYPE_H


#include <vector>
#include <cstring>
#include <QMap>
#include <QString>

//定义从C端接收到的原始二进制数据
class JoinMsgData
{
public:
    ///消息数据的长度（字节）。
    int DataSize;
    ///消息数据内容
    char* pDataBuffer;
public:
    /**
     * @brief 构造函数
     *
     * JoinMsgData类的构造函数
     *
     * @param 无。
     */
    JoinMsgData(void)
        : DataSize(0), pDataBuffer(NULL)
    {
    }

    /**
     * @brief 析构函数
     *
     * JoinMsgData类的析构函数
     *
     * @param 无。
     */
    ~JoinMsgData(void)
    {
        if (pDataBuffer != NULL)
        {
            delete[] pDataBuffer;
            pDataBuffer = NULL;
        }
    }

    //深拷贝
    void PushData(unsigned char* data, int dataLen )
    {
        DataSize = dataLen;
        pDataBuffer = new char[dataLen];
        memcpy(pDataBuffer, data, dataLen);
    }

    char* GetFrameContent()
    {
        return pDataBuffer;
    }

    unsigned int GetFrameLen()
    {
        return DataSize;
    }
};


typedef struct
{
    QString signalValue;
    QString signalHexValue;
    QString unit; //单位
}SignalItemValue;

//一帧数据解析出来之后存储的结构
class MonitorMessage
{
public:
    //消息的源服务的ID（ICD文档中定义的服务ID）。
    unsigned int srcID;
    //消息的调用服务的ID（ICD文档中定义的服务ID）。
    unsigned int dstID;
    //接口ID（ICD文档中定义的接口ID）。
    unsigned int interfaceID;
    //消息时间戳。执行环境头里的时间
    unsigned long long messageTime;
    //消息时间戳 ZMQ头里的时间（埃威收到数据的时间）
    unsigned long long realTime;

    //信号和值
    // key:每个Item对应的唯一Key value:信号值_原始值
    std::map<std::string, std::string> signalMap;

    QMap<QString,QString> m_signalsMap;

public:
    //构造函数
    MonitorMessage()
    {
        srcID = 0;
        dstID = 0;
        messageTime = 0;
        realTime = 0;
    }

    QString GetValueBySignalID(QString )
    {

    }

    //根据信号的序列话编码获取原始值
    QString GetHexValueBySignalId(QString)
    {

    }

    //根据源和目的ID，接口id，信号Id构造唯一 ID ,获取界面树上item
    QString GetSignalItemIdById()
    {

    }
};


#endif // DATAJOINTYPE_H
