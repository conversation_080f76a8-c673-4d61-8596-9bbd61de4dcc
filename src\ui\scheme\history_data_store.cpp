#include "history_data_store.h"
#include <QMutexLocker>
#include <QDebug>

HistoryDataStore::HistoryDataStore(QObject *parent)
    : QObject(parent), m_totalItems(0)
{
}

HistoryDataStore& HistoryDataStore::getInstance()
{
    static HistoryDataStore instance;
    return instance;
}

void HistoryDataStore::addData(const ParsedData& parsedData)
{
    QMutexLocker locker(&m_mutex);

    // 创建历史数据项
    HistoryDataItem dataItem(parsedData);

    // 生成key
    QString key = generateKey(parsedData);
    if (key.isEmpty()) {
        qWarning() << "Unable to generate valid key, skipping data storage";
        return;
    }

    // 获取或创建该key的数据列表
    QList<HistoryDataItem>& dataList = m_dataStore[key];

    // 添加新数据
    dataList.append(dataItem);
    m_totalItems++;

    // 如果超过每个key的最大数量，移除最旧的数据
    while (dataList.size() > MAX_ITEMS_PER_KEY) {
        dataList.removeFirst();
        m_totalItems--;
    }

    // 如果总数据量超过限制，清理旧数据
    if (m_totalItems > MAX_TOTAL_ITEMS) {
        cleanupOldData();
    }

    qDebug() << "Added history data, key:" << key << "current count:" << dataList.size() << "total count:" << m_totalItems;

    // 发射信号
    emit dataAdded(key, dataItem);
}

QList<HistoryDataStore::HistoryDataItem> HistoryDataStore::getData(const QString& key) const
{
    QMutexLocker locker(&m_mutex);
    return m_dataStore.value(key);
}

QList<HistoryDataStore::HistoryDataItem> HistoryDataStore::getData(int srcUnitId, int msgId) const
{
    QString key = generateKey(srcUnitId, msgId);
    return getData(key);
}

HistoryDataStore::HistoryDataItem HistoryDataStore::getLatestData(const QString& key) const
{
    QMutexLocker locker(&m_mutex);
    QList<HistoryDataItem> dataList = m_dataStore.value(key);

    if (dataList.isEmpty()) {
        return HistoryDataItem();
    }

    return dataList.last();
}

HistoryDataStore::HistoryDataItem HistoryDataStore::getLatestData(int srcUnitId, int msgId) const
{
    QString key = generateKey(srcUnitId, msgId);
    return getLatestData(key);
}

QStringList HistoryDataStore::getAllKeys() const
{
    QMutexLocker locker(&m_mutex);
    return m_dataStore.keys();
}

int HistoryDataStore::getDataCount(const QString& key) const
{
    QMutexLocker locker(&m_mutex);
    return m_dataStore.value(key).size();
}

int HistoryDataStore::getTotalDataCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_totalItems;
}

void HistoryDataStore::clearData(const QString& key)
{
    QMutexLocker locker(&m_mutex);

    if (m_dataStore.contains(key)) {
        int removedCount = m_dataStore[key].size();
        m_totalItems -= removedCount;
        m_dataStore.remove(key);

        qDebug() << "Cleared history data, key:" << key << "removed count:" << removedCount;

        emit dataCleared(key, removedCount);
    }
}

void HistoryDataStore::clearAllData()
{
    QMutexLocker locker(&m_mutex);

    int totalRemoved = m_totalItems;
    m_dataStore.clear();
    m_totalItems = 0;

    qDebug() << "Cleared all history data, total removed count:" << totalRemoved;

    emit dataCleared("", totalRemoved);
}

QString HistoryDataStore::generateKey(const ParsedData& parsedData)
{
    int srcUnitId, msgId;
    if (!extractIds(parsedData, srcUnitId, msgId)) {
        return QString();
    }

    return generateKey(srcUnitId, msgId);
}

QString HistoryDataStore::generateKey(int srcUnitId, int msgId)
{
    return QString("%1_%2").arg(srcUnitId).arg(msgId);
}

bool HistoryDataStore::extractIds(const ParsedData& parsedData, int& srcUnitId, int& msgId)
{
    // 获取总线类型
    uint32_t dataType = parsedData.header.zmqWrapHeader.data_bus_type;

    if (dataType == 1) {
        // FC总线
        srcUnitId = parsedData.header.headerFc.aoxeHeader.source & 0xffff;
        msgId = parsedData.header.headerFc.aoxeHeader.topicId & 0xffff;
        return true;
    } else if (dataType == 2 || dataType == 3) {
        // 任务1394或飞管1394总线
        srcUnitId = parsedData.header.header1394.aoxeHeader.source & 0xffff;
        msgId = parsedData.header.header1394.aoxeHeader.topicId & 0xffff;
        return true;
    } else {
        qWarning() << "Unsupported bus type:" << dataType;
        srcUnitId = 0;
        msgId = 0;
        return false;
    }
}

void HistoryDataStore::cleanupOldData()
{
    // 简单的清理策略：移除每个key中最旧的一半数据
    for (auto it = m_dataStore.begin(); it != m_dataStore.end(); ++it) {
        QList<HistoryDataItem>& dataList = it.value();
        int removeCount = dataList.size() / 2;

        for (int i = 0; i < removeCount; ++i) {
            if (!dataList.isEmpty()) {
                dataList.removeFirst();
                m_totalItems--;
            }
        }
    }

    qDebug() << "清理旧数据完成，当前总数量:" << m_totalItems;
}
