#ifndef SUBSCRIBE_HANDLER_H
#define SUBSCRIBE_HANDLER_H

#include "command_handler.h"

/**
 * @brief 订阅命令处理器
 */
class SubscribeCommandHandler : public CommandHandler
{
    Q_OBJECT

public:
    explicit SubscribeCommandHandler(QObject *parent = nullptr);
    ~SubscribeCommandHandler() override;
    CommandResult processCommand(const QByteArray &data) override;

private:
    // 可以添加特定于订阅命令的私有成员和方法
};

#endif // SUBSCRIBE_HANDLER_H
