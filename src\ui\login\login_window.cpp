#include "login_window.h"
#include "ui_loginwindow.h"
#include "ui/main/main_window.h"
#include "utils/user_session.h"
#include "utils/api_url_manager.h"

#include <QPainter>
#include <QGraphicsDropShadowEffect>
#include <QSettings>
#include <QStandardPaths>
#include <QDir>
#include <QFile>
#include <QTextStream>
#include <QDebug>
#include <QMessageBox>
#include <QJsonObject>
#include <QJsonDocument>
#include <QNetworkRequest>
#include <QUrlQuery>
#include <QApplication>

LoginWindow::LoginWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::LoginWindow)
    , isPasswordVisible(false)
    , networkManager(new QNetworkAccessManager(this))
{
    ui->setupUi(this);

    // 窗口最大化
    showMaximized();

    // 添加阴影效果
    QGraphicsDropShadowEffect *shadow = new QGraphicsDropShadowEffect(this);
    shadow->setBlurRadius(20);
    shadow->setColor(QColor(0, 0, 0, 60));
    shadow->setOffset(0, 4);
    ui->loginCard->setGraphicsEffect(shadow);

    // 获取配置文件路径
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QDir dir(configPath);
    if (!dir.exists()) {
        dir.mkpath(".");
    }
    QString configFile = dir.filePath("MicroserviceMonitor.conf");

    // 检查配置文件是否存在，如果不存在则创建默认配置文件
    QFile file(configFile);
    if (!file.exists()) {
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QTextStream stream(&file);
            stream << "[Server]\n"
                   << "address=\n\n"
                   << "[Login]\n"
                   << "username=\n"
                   << "password=\n"
                   << "rememberPassword=false\n\n"
                   << "[ICD]\n"
                   << "defaultSelectedFile=\n";
            file.close();
            qDebug() << "Created default configuration file at:" << configFile;
        } else {
            qWarning() << "Failed to create configuration file at:" << configFile;
        }
    }

    // 加载配置
    QSettings settings(configFile, QSettings::IniFormat);

    // 加载上次保存的服务器地址
    QString savedAddress = settings.value("Server/address", "").toString();
    ui->serverAddressEdit->setText(savedAddress);

    // 加载保存的用户名和密码
    if (settings.value("Login/rememberPassword", false).toBool()) {
        QString savedUsername = settings.value("Login/username", "").toString();
        QString savedPassword = settings.value("Login/password", "").toString();
        ui->usernameEdit->setText(savedUsername);
        ui->passwordEdit->setText(savedPassword);
        ui->rememberPwdCheck->setChecked(true);
    }

    // 初始化密码显示状态
    updatePasswordVisibilityIcon();

    setupConnections();

    // 设置网络连接信号槽
    connect(networkManager, &QNetworkAccessManager::finished,
            this, &LoginWindow::onLoginResponse);
}

LoginWindow::~LoginWindow()
{
    delete ui;
}

void LoginWindow::setupConnections()
{
    connect(ui->loginButton, &QPushButton::clicked, this, &LoginWindow::onLoginClicked);
    connect(ui->serverAddressEdit, &QLineEdit::textChanged, this, &LoginWindow::onServerAddressChanged);
    connect(ui->togglePasswordButton, &QPushButton::clicked, this, &LoginWindow::onTogglePasswordVisibility);
}

void LoginWindow::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    drawBackground(painter);
    QMainWindow::paintEvent(event);
}

void LoginWindow::drawBackground(QPainter &painter)
{
    painter.setRenderHint(QPainter::Antialiasing);

    // 创建背景渐变
    QLinearGradient gradient(0, 0, 0, height());
    gradient.setColorAt(0, QColor(24, 144, 255));
    gradient.setColorAt(1, QColor(64, 169, 255));

    painter.fillRect(rect(), gradient);

    // 绘制装饰圆
    painter.setPen(Qt::NoPen);
    painter.setBrush(QColor(255, 255, 255, 30));

    // 添加多个半透明圆形作为背景装饰
    painter.drawEllipse(QPoint(100, 100), 50, 50);
    painter.drawEllipse(QPoint(width() - 150, 200), 80, 80);
    painter.drawEllipse(QPoint(200, height() - 100), 60, 60);
    painter.drawEllipse(QPoint(width() - 100, height() - 150), 40, 40);
}

void LoginWindow::onLoginClicked()
{
    QString serverAddress = getServerAddress();
    QString username = ui->usernameEdit->text();
    QString password = ui->passwordEdit->text();

    // 基本验证
    if (serverAddress.isEmpty()) {
        QMessageBox::warning(this, "错误", "请输入服务器地址");
        return;
    }
    if (username.isEmpty()) {
        QMessageBox::warning(this, "错误", "请输入用户名");
        return;
    }
    if (password.isEmpty()) {
        QMessageBox::warning(this, "错误", "请输入密码");
        return;
    }

    // 获取配置文件路径并保存设置
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QString configFile = QDir(configPath).filePath("MicroserviceMonitor.conf");
    QSettings settings(configFile, QSettings::IniFormat);

    // 保存服务器地址
    settings.setValue("Server/address", serverAddress);

    // 只在勾选"记住密码"时保存信息
    if (ui->rememberPwdCheck->isChecked()) {
        settings.setValue("Login/username", username);
        settings.setValue("Login/password", password);
        settings.setValue("Login/rememberPassword", true);
    } else {
        // 只更新记住密码的状态，不清除已保存的信息
        settings.setValue("Login/rememberPassword", false);
    }

    // 确保从配置文件重新加载服务器地址
    ApiUrlManager::getInstance().reloadFromConfig();

    // 准备登录请求
    QUrl url = ApiUrlManager::getInstance().getUrl(ApiUrlManager::USER_LOGIN);
    QNetworkRequest request(url);
    request.setRawHeader("accept", "*/*");
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    // 准备登录数据
    QString ip = url.host();
    QJsonObject jsonObj;
    jsonObj["currentIp"] = ip;
    jsonObj["username"] = username;
    jsonObj["password"] = password;
    QJsonDocument doc(jsonObj);
    QByteArray jsonData = doc.toJson();

    // 发送POST请求
    networkManager->post(request, jsonData);

    // 禁用登录按钮，显示加载状态
    ui->loginButton->setEnabled(false);
    ui->loginButton->setText("登录中...");
}

void LoginWindow::onLoginResponse(QNetworkReply *reply)
{
    // 恢复登录按钮状态
    ui->loginButton->setEnabled(true);
    ui->loginButton->setText("登录");

    // 获取用户输入的用户名
    QString username = ui->usernameEdit->text();
    // 创建并显示主界面

    // MainWindow *mainWindow = new MainWindow("admin");

    // // 连接登出成功信号
    // connect(mainWindow, &MainWindow::logoutSuccess, this, [this]() {
    //     // 当收到登出信号时，清除UserSession数据
    //     UserSession::getInstance().logout();

    //     qDebug() << "收到登出成功信号，准备重新显示登录窗口";
    //     QApplication::exit(1); // 退出当前事件循环，但返回代码为1表示需要重新登录
    // });


    //mainWindow->show();

    // 隐藏登录窗口，但不关闭它
    //    this->hide();
    //    reply->deleteLater();
    //    return;
    if (reply->error() == QNetworkReply::NoError) {
        // 读取响应数据
        QByteArray response = reply->readAll();
        QJsonDocument doc = QJsonDocument::fromJson(response);
        QJsonObject jsonObj = doc.object();

        qDebug() << "Login response received:" << doc.toJson(QJsonDocument::Compact);

        // 检查响应是否成功
        if (jsonObj["success"].toBool()) {
            QJsonObject userData;
            if (jsonObj.contains("data") && jsonObj["data"].isObject()) {
                userData = jsonObj["data"].toObject();
            } else {
                qWarning() << "Login response missing 'data' object";
                QMessageBox::warning(this, tr("登录失败"), tr("服务器响应格式错误: 缺少数据字段"));
                reply->deleteLater();
                return;
            }

            // 获取UserSession单例
            UserSession& session = UserSession::getInstance();
            // 保存整个用户信息对象
            session.setUserInfo(userData);

            // 按照要求保存指定字段：token、name、id、icdVersion

            // 1. 保存token
            if (userData.contains("token")) {
                session.setToken(userData["token"].toString());
                qDebug() << "Token saved to session";
            } else {
                qWarning() << "Response missing 'token' field";
            }

            QJsonObject userVo;
            userVo = userData["userVo"].toObject();

            // 2. 保存name
            if (userVo.contains("userName")) {
                session.setUsername(userVo["userName"].toString());
                qDebug() << "Name saved to session:" << userData["name"].toString();
            } else {
                // 如果没有name字段，则使用登录时输入的用户名
                session.setUsername(username);
                qDebug() << "Using login username as name:" << username;
            }

            // 3. 保存id
            if (userVo.contains("userId")) {
                session.setUserId(userVo["userId"].toInt());
                qDebug() << "User ID saved to session:" << userData["id"].toInt();
            } else {
                qWarning() << "Response missing 'id' field";
            }

            // 4. 保存icdVersion作为自定义属性
            if (userData.contains("icdVersion")) {
                session.setCustomProperty("icdVersion", userData["icdVersion"].toString());
                qDebug() << "ICD Version saved to session:" << userData["icdVersion"].toString();
            } else {
                qWarning() << "Response missing 'icdVersion' field";
            }

            // 保存其他可能有用的字段作为自定义属性
            if (userData.contains("digitalModel")) {
                session.setCustomProperty("digitalModel", userData["digitalModel"].toString());
            }

            if (userData.contains("type")) {
                session.setCustomProperty("type", userData["type"].toString());
            }

            if (userData.contains("url")) {
                session.setCustomProperty("url", userData["url"].toString());
            }

            // 打印登录成功信息
            qDebug() << "Login successful. User data saved to session.";

            // 创建并显示主界面
            MainWindow *mainWindow = new MainWindow(session.getUsername());

            // 连接登出成功信号
            connect(mainWindow, &MainWindow::logoutSuccess, this, [this]() {
                // 当收到登出信号时，清除UserSession数据
                UserSession::getInstance().logout();

                qDebug() << "收到登出成功信号，准备重新显示登录窗口";
                QApplication::exit(1); // 退出当前事件循环，但返回代码为1表示需要重新登录
            });

            mainWindow->show();

            // 隐藏登录窗口，但不关闭它
            this->hide();
        } else {
            // 登录失败，显示错误信息
            QString errorMsg = jsonObj["message"].toString("登录失败");
            QMessageBox::warning(this, tr("登录失败"), tr("服务器响应错误: ") + errorMsg);
        }
    } else {
        // 网络错误处理
        QString errorMsg = reply->errorString();
        QMessageBox::warning(this, tr("登录失败"), tr("网络错误: ") + errorMsg);
    }

    // 释放资源
    reply->deleteLater();
}

void LoginWindow::onServerAddressChanged(const QString &address)
{
    // 获取配置文件路径
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QString configFile = QDir(configPath).filePath("MicroserviceMonitor.conf");
    QSettings settings(configFile, QSettings::IniFormat);

    // 保存服务器地址到设置
    settings.setValue("Server/address", address);

    // 同步更新ApiUrlManager的服务器地址
    ApiUrlManager::getInstance().reloadFromConfig();
}

void LoginWindow::onTogglePasswordVisibility()
{
    isPasswordVisible = !isPasswordVisible;
    ui->passwordEdit->setEchoMode(isPasswordVisible ? QLineEdit::Normal : QLineEdit::Password);
    updatePasswordVisibilityIcon();
}

void LoginWindow::updatePasswordVisibilityIcon()
{
    QIcon icon(isPasswordVisible ? ":/images/eye-on.png" : ":/images/eye-off.png");
    ui->togglePasswordButton->setIcon(icon);
    ui->togglePasswordButton->setToolTip(isPasswordVisible ? "隐藏密码" : "显示密码");
}

QString LoginWindow::getServerAddress() const
{
    return ui->serverAddressEdit->text().trimmed();
}
