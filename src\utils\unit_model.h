#ifndef UNITMODEL_H
#define UNITMODEL_H

#include<QtCore/QXmlStreamReader>
#include<QtCore/QFile>
#include<QtCore/QDir>
#include<QtCore/QString>
#include<QtCore/QIODevice>
#include<map>
#include<QtCore/QMutex>

class UnitModel
{
public:
    //析构函数
    ~UnitModel(void);
    //获取单实例指针
    static UnitModel* Instance();
    //删除单实例指针
    static void DeleteInstance();
    //根据单位ID获取单位名
    QString GetNameFromID(QString strID);
    ////根据单位ID获取单位所在类型的单位名称列表
    QStringList GetUnitNameListByUnitID(QString strID);
    //根据单位名获取单位ID
    unsigned int GetUnitIDFromName(QString UnitName);
    //解析单位文件
    void InitPath(QString strFullPath);
    //进行单位转换
    static double unit_change(int unit_in, int unit_out, double value);
private:
    //构造函数
    UnitModel(void);
    //解析单位文件
    void ReadDataFromXml();
private:
    //单位文件所在的路径
    QString m_strUnitFileName;
    //单位的ID和名称对应关系
    std::map<QString,QString> m_mapIDToName;
    //单位的名称和ID对应关系
    std::map<QString,unsigned int> m_mapNameToID;
    //单位的ID和单位的类型ID对应关系
    std::map<QString,QString> m_mapIDToTypeID;
    //单位的类型ID对应的单位名称列表
    std::map<QString,QStringList> m_mapTypeIDToUnitNameList;
    //单实例指针
    static UnitModel* pInst;
    //解析单位文件时用的信号量
    QMutex m_Mutex;
    //是否已经解析过文件了
    bool IsAlreadyParsed;

};

#endif // UNITMODEL_H
