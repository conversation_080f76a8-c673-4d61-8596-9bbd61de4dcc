<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DateTimePickerDialog</class>
 <widget class="QDialog" name="DateTimePickerDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>223</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>选择时间</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QFrame" name="timeRangeFrame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <widget class="QLabel" name="titleLabel">
        <property name="text">
         <string>请选择回放的时间范围</string>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QGridLayout" name="gridLayout">
        <item row="0" column="0">
         <widget class="QLabel" name="startTimeLabel">
          <property name="text">
           <string>开始时间:</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QDateTimeEdit" name="startDateTimeEdit">
          <property name="displayFormat">
           <string>yyyy-MM-dd HH:mm:ss</string>
          </property>
          <property name="calendarPopup">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="endTimeLabel">
          <property name="text">
           <string>结束时间:</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QDateTimeEdit" name="endDateTimeEdit">
          <property name="displayFormat">
           <string>yyyy-MM-dd HH:mm:ss</string>
          </property>
          <property name="calendarPopup">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QLabel" name="noteLabel">
        <property name="text">
         <string>注意: 只能选择最近30天内的时间范围</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="buttonLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="cancelButton">
       <property name="text">
        <string>取消</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="okButton">
       <property name="text">
        <string>确定</string>
       </property>
       <property name="default">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
