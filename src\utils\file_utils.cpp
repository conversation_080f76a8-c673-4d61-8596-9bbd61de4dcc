#include "file_utils.h"
#include <QFile>
#include <QTextStream>
#include <QStandardPaths>

bool FileUtils::ensureDirectoryExists(const QString &dirPath)
{
    QDir dir(dirPath);
    if (!dir.exists()) {
        return dir.mkpath(".");
    }
    return true;
}

bool FileUtils::ensureFileExists(const QString &filePath, const QString &defaultContent)
{
    QFile file(filePath);
    if (!file.exists()) {
        // 确保文件所在目录存在
        QFileInfo fileInfo(filePath);
        ensureDirectoryExists(fileInfo.absolutePath());
        
        // 创建并写入默认内容
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            if (!defaultContent.isEmpty()) {
                QTextStream stream(&file);
                stream << defaultContent;
            }
            file.close();
            return true;
        }
        return false;
    }
    return true;
}

QString FileUtils::getConfigPath(const QString &appName)
{
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    if (!appName.isEmpty()) {
        configPath = QDir(configPath).filePath(appName);
    }
    return configPath;
} 