<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>unitAllocation</class>
 <widget class="QWidget" name="unitAllocation">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>980</width>
    <height>642</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>单位配置</string>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <property name="spacing">
    <number>10</number>
   </property>
   <property name="leftMargin">
    <number>10</number>
   </property>
   <property name="topMargin">
    <number>10</number>
   </property>
   <property name="rightMargin">
    <number>10</number>
   </property>
   <property name="bottomMargin">
    <number>10</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <property name="spacing">
      <number>10</number>
     </property>
     <property name="leftMargin">
      <number>10</number>
     </property>
     <property name="topMargin">
      <number>10</number>
     </property>
     <item>
      <widget class="QPushButton" name="addButton">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>32</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>80</width>
         <height>32</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
    background-color: #1890ff;
    color: white;
    border-radius: 4px;
    font-weight: bold;
    padding: 6px 10px;
}
QPushButton:hover {
    background-color: #40a9ff;
}
QPushButton:pressed {
    background-color: #096dd9;
}</string>
       </property>
       <property name="text">
        <string>添加</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="uploadButton">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>32</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>80</width>
         <height>32</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
    background-color: #1890ff;
    color: white;
    border-radius: 4px;
    font-weight: bold;
    padding: 6px 10px;
}
QPushButton:hover {
    background-color: #40a9ff;
}
QPushButton:pressed {
    background-color: #096dd9;
}</string>
       </property>
       <property name="text">
        <string>上传</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeType">
        <enum>QSizePolicy::Expanding</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QTableWidget" name="tableWidget">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="styleSheet">
      <string notr="true">QTableWidget {
    border: 1px solid #e8e8e8;
    background-color: white;
    gridline-color: #f0f0f0;
}
QTableWidget::item {
    padding: 5px;
}
QHeaderView::section {
    background-color: #f5f5f5;
    padding: 8px;
    border: none;
    border-right: 1px solid #e8e8e8;
    border-bottom: 1px solid #e8e8e8;
    font-weight: bold;
}</string>
     </property>
     <column>
      <property name="text">
       <string>类型</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>code</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>名称</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>系数</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>操作</string>
      </property>
     </column>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
