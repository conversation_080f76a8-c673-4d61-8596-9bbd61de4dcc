<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ICDViewDialog</class>
 <widget class="QWidget" name="ICDViewDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>900</width>
    <height>700</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>ICD文件管理</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget {
    background-color: white;
    font-family: Microsoft YaHei, Arial, sans-serif;
}

QLabel#titleLabel {
    color: #333333;
    font-size: 18px;
    font-weight: bold;
}

QLineEdit {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 6px 12px;
    background-color: white;
    min-height: 32px;
}

QLineEdit:focus {
    border-color: #40a9ff;
}

QPushButton {
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    background-color: #1890ff;
    color: white;
    min-height: 32px;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #40a9ff;
}

QPushButton:pressed {
    background-color: #096dd9;
}

QPushButton#closeButton {
    background-color: #ff4d4f;
    min-width: 30px;
    max-width: 30px;
    border-radius: 15px;
}

QPushButton#closeButton:hover {
    background-color: #ff7875;
}

QPushButton#closeButton:pressed {
    background-color: #d9363e;
}

QPushButton#saveButton {
    background-color: #52c41a;
    font-weight: bold;
}

QPushButton#saveButton:hover {
    background-color: #73d13d;
}

QPushButton#saveButton:pressed {
    background-color: #389e0d;
}

QFrame#separatorLine, QFrame#leftSeparatorLine {
    background-color: #e8e8e8;
    max-height: 1px;
}

QTreeView, QListWidget {
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: white;
    selection-background-color: #e6f7ff;
    selection-color: #1890ff;
}

QTreeView::item, QListWidget::item {
    padding: 8px 5px;
    border-bottom: 1px solid #f0f0f0;
    min-height: 28px;
}

QTreeView::item:selected, QListWidget::item:selected {
    background-color: #e6f7ff;
}

QHeaderView::section {
    background-color: #fafafa;
    padding: 8px;
    border: none;
    border-right: 1px solid #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
    font-weight: bold;
}

QSplitter::handle {
    background-color: #e8e8e8;
    width: 1px;
}

QSplitter::handle:hover {
    background-color: #1890ff;
}

QWidget#topBar {
    background-color: #f0f2f5;
    border-bottom: 1px solid #e0e0e0;
    min-height: 50px;
    max-height: 50px;
    padding: 0px 10px;
}
</string>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="topBar" native="true">
     <layout class="QHBoxLayout" name="titleBarLayout">
      <property name="spacing">
       <number>15</number>
      </property>
      <property name="leftMargin">
       <number>20</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>20</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QLabel" name="titleLabel">
        <property name="text">
         <string>ICD文件管理</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="titleSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="closeButton">
        <property name="minimumSize">
         <size>
          <width>30</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>30</width>
          <height>30</height>
         </size>
        </property>
        <property name="toolTip">
         <string>关闭</string>
        </property>
        <property name="text">
         <string>X</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QSplitter" name="mainSplitter">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="handleWidth">
      <number>1</number>
     </property>
     <property name="childrenCollapsible">
      <bool>false</bool>
     </property>
     <widget class="QWidget" name="leftWidget" native="true">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>1</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>100</width>
        <height>0</height>
       </size>
      </property>
      <layout class="QVBoxLayout" name="leftLayout">
       <property name="spacing">
        <number>10</number>
       </property>
       <property name="leftMargin">
        <number>10</number>
       </property>
       <property name="topMargin">
        <number>10</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>10</number>
       </property>
       <item>
        <layout class="QHBoxLayout" name="headerLayout">
         <property name="spacing">
          <number>10</number>
         </property>
         <item>
          <widget class="QLabel" name="leftTitleLabel">
           <property name="text">
            <string>ICD配置文件</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignLeft|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="importButton">
           <property name="maximumSize">
            <size>
             <width>80</width>
             <height>32</height>
            </size>
           </property>
           <property name="text">
            <string>导入</string>
           </property>
           <property name="icon">
            <iconset resource="../../resources/resources.qrc">
             <normaloff>:/images/import.png</normaloff>:/images/import.png</iconset>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="addButton">
           <property name="maximumSize">
            <size>
             <width>80</width>
             <height>32</height>
            </size>
           </property>
           <property name="text">
            <string>新增</string>
           </property>
           <property name="icon">
            <iconset resource="../../resources/resources.qrc">
             <normaloff>:/images/add.png</normaloff>:/images/add.png</iconset>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QFrame" name="leftSeparatorLine">
         <property name="frameShape">
          <enum>QFrame::HLine</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Sunken</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="searchEdit">
         <property name="placeholderText">
          <string>搜索文件...</string>
         </property>
         <property name="clearButtonEnabled">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QListWidget" name="fileListWidget">
         <property name="iconSize">
          <size>
           <width>24</width>
           <height>24</height>
          </size>
         </property>
         <property name="textElideMode">
          <enum>Qt::ElideMiddle</enum>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="rightWidget" native="true">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>9</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <layout class="QVBoxLayout" name="rightLayout">
       <property name="spacing">
        <number>10</number>
       </property>
       <property name="leftMargin">
        <number>5</number>
       </property>
       <property name="topMargin">
        <number>10</number>
       </property>
       <property name="rightMargin">
        <number>10</number>
       </property>
       <property name="bottomMargin">
        <number>10</number>
       </property>
       <item>
        <widget class="QLabel" name="contentLabel">
         <property name="font">
          <font>
           <pointsize>10</pointsize>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
         <property name="text">
          <string>文件内容</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTreeView" name="contentTreeView">
         <property name="alternatingRowColors">
          <bool>true</bool>
         </property>
         <property name="selectionMode">
          <enum>QAbstractItemView::SingleSelection</enum>
         </property>
         <property name="selectionBehavior">
          <enum>QAbstractItemView::SelectItems</enum>
         </property>
         <property name="uniformRowHeights">
          <bool>true</bool>
         </property>
         <property name="sortingEnabled">
          <bool>false</bool>
         </property>
         <property name="animated">
          <bool>true</bool>
         </property>
         <property name="wordWrap">
          <bool>true</bool>
         </property>
         <property name="headerHidden">
          <bool>false</bool>
         </property>
         <property name="expandsOnDoubleClick">
          <bool>true</bool>
         </property>
         <attribute name="headerMinimumSectionSize">
          <number>50</number>
         </attribute>
         <attribute name="headerShowSortIndicator" stdset="0">
          <bool>false</bool>
         </attribute>
         <attribute name="headerStretchLastSection">
          <bool>true</bool>
         </attribute>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="buttonLayout">
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="saveButton">
           <property name="minimumSize">
            <size>
             <width>100</width>
             <height>36</height>
            </size>
           </property>
           <property name="text">
            <string>保存</string>
           </property>
           <property name="icon">
            <iconset resource="../../resources/resources.qrc">
             <normaloff>:/images/save.png</normaloff>:/images/save.png</iconset>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../resources/resources.qrc"/>
 </resources>
 <connections/>
</ui>