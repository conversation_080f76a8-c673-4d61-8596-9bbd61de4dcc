<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MonitorWindow</class>
 <widget class="QWidget" name="MonitorWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>监控界面</string>
  </property>
  <property name="styleSheet">
   <string>QWidget {
    background-color: #f0f2f5;
}

QRadioButton {
    font-size: 14px;
    padding: 5px;
    color: #333333;
}

QRadioButton:checked {
    color: #1890ff;
    font-weight: bold;
}

QPushButton {
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
}

QPushButton:hover {
    background-color: #40a9ff;
}

QPushButton:pressed {
    background-color: #096dd9;
}

QLabel#titleLabel {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

QLabel#dateTimeLabel {
    font-size: 14px;
    color: #333;
}

QLabel#consoleTitleLabel, QLabel#configTitleLabel {
    font-size: 16px;
    font-weight: bold;
    color: white;
    background-color: #1890ff;
    padding: 10px;
}

QLabel#sectionLabel {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-top: 10px;
}

#leftConsole {
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    background-color: white;
}

#rightConfig {
    background-color: white;
}</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout" stretch="9,1">
   <property name="spacing">
    <number>5</number>
   </property>
   <property name="leftMargin">
    <number>10</number>
   </property>
   <property name="topMargin">
    <number>10</number>
   </property>
   <property name="rightMargin">
    <number>10</number>
   </property>
   <property name="bottomMargin">
    <number>10</number>
   </property>
   <item>
    <widget class="QWidget" name="leftConsole" native="true">
     <layout class="QVBoxLayout" name="verticalLayout">
      <property name="spacing">
       <number>5</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QLabel" name="consoleTitleLabel">
        <property name="styleSheet">
         <string notr="true">padding-left: 10px;</string>
        </property>
        <property name="text">
         <string>控制台</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="consoleInfoWidget" native="true">
        <property name="styleSheet">
         <string notr="true">
QLabel {
  color: #333333;
  font-size: 14px;
  margin: 0px;
  padding: 0px;
}
         </string>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>2</number>
         </property>
         <property name="rightMargin">
          <number>10</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QLabel" name="titleLabel">
           <property name="layoutDirection">
            <enum>Qt::LeftToRight</enum>
           </property>
           <property name="styleSheet">
            <string notr="true">padding-left: 0px; margin-left: 0px; font-size: 14px;</string>
           </property>
           <property name="lineWidth">
            <number>0</number>
           </property>
           <property name="text">
            <string>控制台：</string>
           </property>
           <property name="margin">
            <number>0</number>
           </property>
           <property name="indent">
            <number>0</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="consoleNumberLabel">
           <property name="font">
            <font>
             <pointsize>-1</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string>padding-left: 0px; margin-left: 0px; padding-right: 10px; margin-right: 10px;</string>
           </property>
           <property name="text">
            <string>一号台 </string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="dateTimeLabel">
           <property name="styleSheet">
            <string>padding-left: 0px; margin-left: 0px;</string>
           </property>
           <property name="text">
            <string>2024-06-26 10:37:16</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="radioButtonsWidget" native="true">
        <property name="styleSheet">
         <string notr="true">
QRadioButton {
  padding: 2px 0px;
  margin: 0px;
  font-size: 14px;
  text-indent: 15px;
}
         </string>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>10</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QRadioButton" name="fcRadio">
           <property name="minimumSize">
            <size>
             <width>100</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>100</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">padding-left: 0px; margin-left: 0px;</string>
           </property>
           <property name="text">
            <string>FC网络图</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QRadioButton" name="protocolRadio">
           <property name="minimumSize">
            <size>
             <width>120</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>120</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>1394网络配分</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QRadioButton" name="fcSettingRadio">
           <property name="minimumSize">
            <size>
             <width>110</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>110</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>FC设置状态</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QRadioButton" name="functionDisplayRadio">
           <property name="minimumSize">
            <size>
             <width>120</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>120</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="text">
            <string>功能显示状态</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="displayArea" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="styleSheet">
         <string notr="true">background-color: #f5f5f5; border: 1px solid #e0e0e0;</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="rightConfig" native="true">
     <property name="styleSheet">
      <string notr="true">
#rightConfig {
  padding: 0;
  margin: 0;
  border: none;
}

QLabel#configTitleLabel {
  background-color: #2196F3;
  color: white;
  padding: 10px;
  font-weight: bold;
  text-align: center;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

QWidget#configItemsWidget {
  background-color: white;
}

QHBoxLayout#configItemLayout {
  margin: 0;
  padding: 5px 10px;
}

QLabel.configLabel {
  color: #333;
  font-size: 14px;
}

QPushButton.uploadButton {
  background-color: #2196F3;
  color: white;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  min-width: 60px;
}

QScrollArea {
  border: none;
  background-color: #F5F5F5;
}

QScrollArea &gt; QWidget &gt; QWidget {
  background-color: #F5F5F5;
}

QRadioButton {
  padding: 8px;
  margin-left: 5px;
}

QWidget#bottomButtonsWidget {
  background-color: #F5F5F5;
  border-top: 1px solid #E0E0E0;
  padding: 5px;
}

QWidget#statusBar {
  background-color: #E3F2FD;
  border-radius: 4px;
  border: 1px solid #BBDEFB;
  color: #2196F3;
  padding: 5px;
}

QPushButton#realtimeBtn, QPushButton#replayBtn {
  background-color: #1890ff !important;
  color: white;
  min-width: 80px;
  min-height: 30px;
  max-height: 30px;
  border-radius: 4px;
  font-size: 15px;
  font-weight: bold;
  padding: 2px 10px;
  border: 1px solid #1976D2;
  text-align: center;
  letter-spacing: 1px;
}

QPushButton#realtimeBtn:hover, QPushButton#replayBtn:hover {
  background-color: #40a9ff !important;
}

QPushButton#realtimeBtn:pressed, QPushButton#replayBtn:pressed {
  background-color: #096dd9 !important;
}
      </string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2" stretch="0,0,1,0,0">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QLabel" name="configTitleLabel">
        <property name="text">
         <string>控制台配置和监控</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="configItemsWidget" native="true">
        <layout class="QVBoxLayout" name="verticalLayout_4">
         <property name="spacing">
          <number>5</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>10</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>10</number>
         </property>
         <item>
          <layout class="QHBoxLayout" name="fcConfigLayout">
           <property name="leftMargin">
            <number>10</number>
           </property>
           <property name="rightMargin">
            <number>10</number>
           </property>
           <item>
            <widget class="QLabel" name="fcConfigLabel">
             <property name="styleSheet">
              <string notr="true">color: #333; font-size: 14px;
background-color: rgb(255, 255, 255);</string>
             </property>
             <property name="text">
              <string>FC设备配置表</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_3">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="uploadFCButton">
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>30</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>60</width>
               <height>30</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">font-size: 13px; padding: 2px 5px;</string>
             </property>
             <property name="text">
              <string>上传</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="icdConfigLayout">
           <property name="leftMargin">
            <number>10</number>
           </property>
           <property name="rightMargin">
            <number>10</number>
           </property>
           <item>
            <widget class="QLabel" name="icdConfigLabel">
             <property name="styleSheet">
              <string notr="true">color: #333; font-size: 14px;
background-color: rgb(255, 255, 255);</string>
             </property>
             <property name="text">
              <string>ICD文件</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_4">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="uploadICDButton">
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>30</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>60</width>
               <height>30</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">font-size: 13px; padding: 2px 5px;</string>
             </property>
             <property name="text">
              <string>上传</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QScrollArea" name="fileListScrollArea">
        <property name="styleSheet">
         <string notr="true">background-color: #F5F5F5;</string>
        </property>
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <property name="verticalScrollBarPolicy">
         <enum>Qt::ScrollBarAsNeeded</enum>
        </property>
        <property name="horizontalScrollBarPolicy">
         <enum>Qt::ScrollBarAlwaysOff</enum>
        </property>
        <property name="widgetResizable">
         <bool>true</bool>
        </property>
        <widget class="QWidget" name="scrollAreaWidgetContents">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>0</y>
           <width>244</width>
           <height>575</height>
          </rect>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: #F5F5F5;</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>5</number>
          </property>
          <property name="topMargin">
           <number>5</number>
          </property>
          <property name="rightMargin">
           <number>5</number>
          </property>
          <property name="bottomMargin">
           <number>5</number>
          </property>
          <item>
           <widget class="QRadioButton" name="file1Radio">
            <property name="styleSheet">
             <string notr="true">padding: 10px;</string>
            </property>
            <property name="text">
             <string>文件1.icd</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="file2Radio">
            <property name="styleSheet">
             <string notr="true">padding: 10px;</string>
            </property>
            <property name="text">
             <string>文件2.icd</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="file3Radio">
            <property name="styleSheet">
             <string notr="true">padding: 10px;</string>
            </property>
            <property name="text">
             <string>FC配置表.xlsx</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="verticalSpacer">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="statusBar" native="true">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>30</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">background-color: #E3F2FD; border: 1px solid #BBDEFB;</string>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_6">
         <property name="spacing">
          <number>5</number>
         </property>
         <property name="leftMargin">
          <number>10</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>10</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="bottomButtonsWidget" native="true">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>50</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>50</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">background-color: #F5F5F5;</string>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_5">
         <property name="spacing">
          <number>10</number>
         </property>
         <property name="leftMargin">
          <number>10</number>
         </property>
         <property name="topMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>10</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <item>
          <spacer name="horizontalSpacer_5">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="realtimeBtn">
           <property name="minimumSize">
            <size>
             <width>102</width>
             <height>36</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: #1890ff; color: white; font-weight: bold;</string>
           </property>
           <property name="text">
            <string>实时</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="replayBtn">
           <property name="minimumSize">
            <size>
             <width>102</width>
             <height>36</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: #1890ff; color: white; font-weight: bold;</string>
           </property>
           <property name="text">
            <string>回放</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
