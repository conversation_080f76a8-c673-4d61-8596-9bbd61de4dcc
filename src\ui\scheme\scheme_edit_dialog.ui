<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SchemeEditDialog</class>
 <widget class="QDialog" name="SchemeEditDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>编辑方案</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QDialog {
    background-color: #f0f2f5;
    font-family: Microsoft YaHei, Arial, sans-serif;
}

QSplitter::handle {
    background-color: #e8e8e8;
    width: 2px;
}

QWidget#leftPanel, QWidget#middlePanel, QWidget#rightPanel {
    background-color: white;
    border-radius: 4px;
}

QLabel {
    color: #333333;
    font-size: 14px;
}

QLineEdit {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 6px 12px;
    background-color: white;
    min-height: 32px;
}

QLineEdit:focus {
    border-color: #40a9ff;
}

QPushButton {
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    background-color: #1890ff;
    color: white;
    min-height: 32px;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #40a9ff;
}

QPushButton:pressed {
    background-color: #096dd9;
}

QPushButton#cancelButton {
    background-color: white;
    border: 1px solid #d9d9d9;
    color: #333333;
}

QPushButton#cancelButton:hover {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
}

QPushButton#cancelButton:pressed {
    background-color: #e6e6e6;
}

QTreeView {
    border: none;
    background-color: white;
    selection-background-color: #e6f7ff;
    selection-color: #1890ff;
}

QTreeView::item {
    padding: 8px 5px;
    border-bottom: 1px solid #f0f0f0;
}

QListWidget {
    border: none;
    background-color: white;
}

QListWidget::item {
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
}

QListWidget::item:selected {
    background-color: #e6f7ff;
    color: #1890ff;
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui> 