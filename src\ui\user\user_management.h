#ifndef USER_MANAGEMENT_H
#define USER_MANAGEMENT_H

#include <QWidget>
#include <QStandardItemModel>
#include <QSortFilterProxyModel>

namespace Ui {
class UserManagement;
}

class UserManagement : public QWidget
{
    Q_OBJECT

public:
    explicit UserManagement(QWidget *parent = nullptr);
    ~UserManagement();

private slots:
    void onAddUserClicked();
    void onSearchClicked();
    void onResetClicked();
    void onOperationButtonClicked();

private:
    void setupUI();
    void setupConnections();
    void initializeUserTable();
    void loadUserData();
    void addOperationButtons(int row);
    void addDefaultAdminRow();

    Ui::UserManagement *ui;
    QStandardItemModel *userModel;
    QSortFilterProxyModel *proxyModel;
    QMap<QString, QJsonObject> m_allUser;
};

#endif // USER_MANAGEMENT_H
