<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FcMonitorWidget</class>
 <widget class="QWidget" name="FcMonitorWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1138</width>
    <height>725</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget {
    background-color: #f0f2f5;
    font-family: Microsoft YaHei, Arial, sans-serif;
}

QTabWidget::pane {
    border: none;
    background-color: white;
}



QTabBar::tab {
    background-color: #f0f0f0;
    color: #333333;
    padding: 8px 20px;
    border: none;
    min-width: 80px;
    font-size: 14px;
    text-align: left !important;
    margin-left: 0px !important;
}

QTabBar::tab:selected {
    background-color: white;
    border-bottom: 2px solid #1890ff;
    color: #1890ff;
}

QTabBar::tab:!selected {
    background-color: #f5f5f5;
}

QTreeView {
    border: none;
    background-color: white;
    selection-background-color: #e6f7ff;
    selection-color: #1890ff;
}

QTreeView::item {
    padding: 8px 5px;
    border-bottom: 1px solid #f0f0f0;
}

QPushButton {
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    background-color: #1890ff;
    color: white;
    min-height: 32px;
}

QPushButton:hover {
    background-color: #40a9ff;
}

QPushButton:pressed {
    background-color: #096dd9;
}

QLineEdit {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 6px 12px;
    background-color: white;
    min-height: 32px;
}

QLineEdit:focus {
    border-color: #40a9ff;
}

QToolButton {
    border: none;
    border-radius: 4px;
    padding: 4px;
}

QToolButton:hover {
    background-color: #e6f7ff;
}

QToolButton:pressed {
    background-color: #bae7ff;
}

#titleBar {
    background-color: #1890ff;
    color: white;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    padding: 0px;
}

#addButton {
    background-color: #1890ff;
    color: white;
    font-size: 18px;
    font-weight: bold;
    border-radius: 2px;
    min-width: 32px;
    max-width: 32px;
    min-height: 32px;
    max-height: 32px;
    padding: 0px;
}

#schemeTitleLabel {
    color: white;
    font-weight: bold;
    font-size: 15px;
    padding-left: 10px;
    background-color: #1890ff;
}

#toggleButton {
    color: white;
    background: transparent;
    border: none;
    font-size: 14px;
    min-width: 24px;
    max-width: 24px;
    padding-right: 10px;
}
</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <item>
    <widget class="QWidget" name="leftPanel" native="true">
     <property name="minimumSize">
      <size>
       <width>250</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>250</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QWidget#leftPanel {
    background-color: white;
    border: none;
}</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout" stretch="0,1">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>5</number>
      </property>
      <property name="topMargin">
       <number>5</number>
      </property>
      <property name="rightMargin">
       <number>5</number>
      </property>
      <property name="bottomMargin">
       <number>5</number>
      </property>
      <item>
       <widget class="QWidget" name="titleBar" native="true">
        <property name="styleSheet">
         <string notr="true">background-color: #1890ff;</string>
        </property>
        <layout class="QHBoxLayout" name="titleLayout">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QLabel" name="schemeTitleLabel">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>30</height>
            </size>
           </property>
           <property name="text">
            <string>可选方案</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="titleSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="toggleButton">
           <property name="text">
            <string>&lt;&lt;</string>
           </property>
           <property name="flat">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QTabWidget" name="tabWidget">
        <property name="styleSheet">
         <string notr="true">QTabWidget::pane {
    border: none;
    background-color: white;
}

QTabBar {
    alignment: left !important;
}
QTabBar::tab {
    background-color: #f0f0f0;
    color: #333333;
    padding: 8px 20px;
    border: none;
    min-width: 80px;
    font-size: 14px;
    text-align: left !important;
    margin-left: 0px !important;
}
QTabBar::tab:selected {
    background-color: white;
    border-bottom: 2px solid #1890ff;
    color: #1890ff;
}
QTabBar::tab:!selected {
    background-color: #f5f5f5;
}</string>
        </property>
        <property name="currentIndex">
         <number>0</number>
        </property>
        <property name="elideMode">
         <enum>Qt::ElideNone</enum>
        </property>
        <property name="usesScrollButtons">
         <bool>false</bool>
        </property>
        <property name="documentMode">
         <bool>false</bool>
        </property>
        <property name="tabsClosable">
         <bool>false</bool>
        </property>
        <property name="movable">
         <bool>false</bool>
        </property>
        <property name="tabBarAutoHide">
         <bool>false</bool>
        </property>
        <widget class="QWidget" name="schemeTab">
         <attribute name="title">
          <string>我的方案</string>
         </attribute>
         <layout class="QVBoxLayout" name="schemeTabLayout">
          <property name="spacing">
           <number>8</number>
          </property>
          <property name="leftMargin">
           <number>8</number>
          </property>
          <property name="topMargin">
           <number>8</number>
          </property>
          <property name="rightMargin">
           <number>8</number>
          </property>
          <property name="bottomMargin">
           <number>8</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="searchLayout">
            <property name="spacing">
             <number>8</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QLineEdit" name="schemeSearchEdit">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>46</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QLineEdit {
    border: 1px solid #d9d9d9;
    border-radius: 15px;
    padding-left: 10px;
    padding-right: 28px;
    background-color: #f5f5f5;
}</string>
              </property>
              <property name="placeholderText">
               <string>搜索方案</string>
              </property>
              <property name="clearButtonEnabled">
               <bool>false</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="schemeAddButton">
              <property name="minimumSize">
               <size>
                <width>30</width>
                <height>32</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton {
    border: none;
    border-radius: 15px;
    background-color: #1890ff;
    color: white;
    font-weight: bold;
    font-size: 18px;
    padding: 0px;
}
QPushButton:hover {
    background-color: #40a9ff;
}
QPushButton:pressed {
    background-color: #096dd9;
}</string>
              </property>
              <property name="text">
               <string>+</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QListWidget" name="schemeListWidget">
            <property name="styleSheet">
             <string notr="true">QListWidget {
  border: none;
  background-color: white;
}
QListWidget::item {
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
}
QListWidget::item:selected {
  background-color: #e6f7ff;
  color: #1890ff;
}</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="templateTab">
         <attribute name="title">
          <string>模板库</string>
         </attribute>
         <layout class="QVBoxLayout" name="templateTabLayout">
          <property name="spacing">
           <number>8</number>
          </property>
          <property name="leftMargin">
           <number>8</number>
          </property>
          <property name="topMargin">
           <number>8</number>
          </property>
          <property name="rightMargin">
           <number>8</number>
          </property>
          <property name="bottomMargin">
           <number>8</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="templateSearchLayout">
            <property name="spacing">
             <number>8</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QLineEdit" name="templateSearchEdit">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>46</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QLineEdit {
    border: 1px solid #d9d9d9;
    border-radius: 10px;
    padding-left: 10px;
    padding-right: 28px;
    background-color: #f5f5f5;
}</string>
              </property>
              <property name="placeholderText">
               <string>搜索模板</string>
              </property>
              <property name="clearButtonEnabled">
               <bool>false</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="templateAddButton">
              <property name="minimumSize">
               <size>
                <width>30</width>
                <height>32</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton {
    border: none;
    border-radius: 15px;
    background-color: #1890ff;
    color: white;
    font-weight: bold;
    font-size: 18px;
    padding: 0px;
}
QPushButton:hover {
    background-color: #40a9ff;
}
QPushButton:pressed {
    background-color: #096dd9;
}</string>
              </property>
              <property name="text">
               <string>+</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QWidget" name="templateListWgt" native="true">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QListWidget" name="templateListWidget">
            <property name="styleSheet">
             <string notr="true">QListWidget {
  border: none;
  background-color: white;
}
QListWidget::item {
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
}
QListWidget::item:selected {
  background-color: #e6f7ff;
  color: #1890ff;
}</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="rightPanel" native="true">
     <property name="styleSheet">
      <string notr="true">QWidget#rightPanel {
    background-color: white;
    border-radius: 4px;
}</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_4" stretch="0,1">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>5</number>
      </property>
      <property name="topMargin">
       <number>5</number>
      </property>
      <property name="rightMargin">
       <number>5</number>
      </property>
      <property name="bottomMargin">
       <number>5</number>
      </property>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_5">
        <property name="spacing">
         <number>0</number>
        </property>
        <item>
         <widget class="QWidget" name="topBar" native="true">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>40</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: #1890ff;</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_4">
           <property name="spacing">
            <number>8</number>
           </property>
           <property name="leftMargin">
            <number>10</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>10</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QLabel" name="dataMonitorLabel">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>30</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">font-size: 14px; color: white;</string>
             </property>
             <property name="text">
              <string>数值监控</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="icdFileLabel">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>30</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">font-size: 14px; color: white;</string>
             </property>
             <property name="text">
              <string>lcd文件:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="icdFileNameLabel">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>30</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">font-size: 14px; color: white;</string>
             </property>
             <property name="text">
              <string>test.lcd</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="consoleLabel">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>30</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">font-size: 14px; color: white;</string>
             </property>
             <property name="text">
              <string>控制台:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="consoleNumberLabel">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>30</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">font-size: 14px; color: white;</string>
             </property>
             <property name="text">
              <string>一号台</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="toolBar" native="true">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>40</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: white;</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_3">
           <item>
            <widget class="QPushButton" name="markButton">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>42</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>80</width>
               <height>32</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
   background-color: transparent;
   border: 1px solid #d9d9d9;
   border-radius: 4px;
   color: #333333;
    padding: 4px 8px 4px 8px;
  }
  QPushButton:hover {
     background-color: #40a9ff;
	 color: #1890ff;
  }
  QPushButton:pressed {
     background-color: #096dd9;
	 color: #1890ff;
  }</string>
             </property>
             <property name="text">
              <string>标记</string>
             </property>
             <property name="icon">
              <iconset resource="../../resources/resources.qrc">
               <normaloff>:/images/mark.png</normaloff>:/images/mark.png</iconset>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pauseButton">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>42</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>80</width>
               <height>32</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
   background-color: transparent;
   border: 1px solid #d9d9d9;
   border-radius: 4px;
   color: #333333;
    padding: 4px 8px 4px 8px;
  }
  QPushButton:hover {
     background-color: #40a9ff;
	 color: #1890ff;
  }
  QPushButton:pressed {
     background-color: #096dd9;
	 color: #1890ff;
  }</string>
             </property>
             <property name="text">
              <string>暂停</string>
             </property>
             <property name="icon">
              <iconset resource="../../resources/resources.qrc">
               <normaloff>:/images/pause.png</normaloff>:/images/pause.png</iconset>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="clearButton">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>42</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>80</width>
               <height>32</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
   background-color: transparent;
   border: 1px solid #d9d9d9;
   border-radius: 4px;
   color: #333333;
    padding: 4px 8px 4px 8px;
  }
  QPushButton:hover {
     background-color: #40a9ff;
	 color: #1890ff;
  }
  QPushButton:pressed {
     background-color: #096dd9;
	 color: #1890ff;
  }</string>
             </property>
             <property name="text">
              <string>清空</string>
             </property>
             <property name="icon">
              <iconset resource="../../resources/resources.qrc">
               <normaloff>:/images/clear.png</normaloff>:/images/clear.png</iconset>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="exportButton">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>42</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>80</width>
               <height>32</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
   background-color: transparent;
   border: 1px solid #d9d9d9;
   border-radius: 4px;
   color: #333333;
    padding: 4px 8px 4px 8px;
  }
  QPushButton:hover {
     background-color: #40a9ff;
	 color: #1890ff;
  }
  QPushButton:pressed {
     background-color: #096dd9;
	 color: #1890ff;
  }</string>
             </property>
             <property name="text">
              <string>导出</string>
             </property>
             <property name="icon">
              <iconset resource="../../resources/resources.qrc">
               <normaloff>:/images/export.png</normaloff>:/images/export.png</iconset>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_2">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QComboBox" name="viewModeButton">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>100</width>
               <height>42</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>32</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QComboBox {
    background-color: white;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    color: #333333;
    padding: 6px 24px 6px 12px;
    min-height: 28px;
    font-family: Microsoft YaHei, Arial, sans-serif;
    font-size: 14px;
}

QComboBox:hover {
    border-color: #40a9ff;
    color: #1890ff;
}

QComboBox:focus {
    border-color: #1890ff;
    outline: none;
}

QComboBox:on {
    border-color: #1890ff;
    background-color: #f6ffed;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: right center;
    width: 20px;
    border-left: none;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

QComboBox::down-arrow {
    image: url(:/images/arrow-down.png);
    width: 12px;
    height: 12px;
}

QComboBox::down-arrow:on {
    top: 1px;
    left: 1px;
}

QComboBox QAbstractItemView {
    border: 1px solid #d9d9d9;
    background-color: white;
    selection-background-color: #e6f7ff;
    selection-color: #1890ff;
    outline: none;
    border-radius: 4px;
    padding: 4px;
}

QComboBox QAbstractItemView::item {
    height: 32px;
    padding: 6px 12px;
    border: none;
    border-radius: 2px;
}

QComboBox QAbstractItemView::item:hover {
    background-color: #f0f9ff;
    color: #1890ff;
}

QComboBox QAbstractItemView::item:selected {
    background-color: #e6f7ff;
    color: #1890ff;
}</string>
             </property>
             <item>
              <property name="text">
               <string>全部</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>FC</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>1394</string>
              </property>
             </item>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="maxFontButton">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>42</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>32</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>Microsoft YaHei,Arial,sans-serif</family>
               <pointsize>9</pointsize>
               <weight>50</weight>
               <bold>false</bold>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">
               QPushButton {
                   background-color: transparent;
                   border: 1px solid #d9d9d9;
                   border-radius: 4px;
                   color: #333333;
                   padding: 4px 8px;
               }
               QPushButton:hover {
                   border-color: #1890ff;
                   color: #1890ff;
               }
              </string>
             </property>
             <property name="text">
              <string>MAX字体</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="layoutButton">
             <property name="minimumSize">
              <size>
               <width>70</width>
               <height>42</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>32</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>Microsoft YaHei,Arial,sans-serif</family>
               <weight>50</weight>
               <bold>false</bold>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">
               QPushButton {
                   background-color: transparent;
                   border: 1px solid #d9d9d9;
                   border-radius: 4px;
                   color: #333333;
                   padding: 4px 8px 4px 20px;
                   text-align: right;
               }
               QPushButton:hover {
                   border-color: #1890ff;
                   color: #1890ff;
               }
              </string>
             </property>
             <property name="text">
              <string>展开</string>
             </property>
             <property name="icon">
              <iconset resource="../../resources/resources.qrc">
               <normaloff>:/images/expand.png</normaloff>:/images/expand.png</iconset>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="showHeaderButton">
             <property name="minimumSize">
              <size>
               <width>90</width>
               <height>42</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>32</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>Microsoft YaHei,Arial,sans-serif</family>
               <weight>50</weight>
               <italic>false</italic>
               <bold>false</bold>
               <underline>false</underline>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">
               QPushButton {
                   background-color: transparent;
                   border: 1px solid #d9d9d9;
                   border-radius: 4px;
                   color: #333333;
                   padding: 4px 8px 4px 28px;
                   text-align: right;
               }
               QPushButton:hover {
                   border-color: #1890ff;
                   color: #1890ff;
               }
              </string>
             </property>
             <property name="text">
              <string>显示帧头</string>
             </property>
             <property name="icon">
              <iconset resource="../../resources/resources.qrc">
               <normaloff>:/images/eye-on.png</normaloff>:/images/eye-on.png</iconset>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QComboBox" name="settingsButton">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>100</width>
               <height>42</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>32</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QComboBox {
    background-color: white;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    color: #333333;
    padding: 6px 24px 6px 12px;
    min-height: 28px;
    font-family: Microsoft YaHei, Arial, sans-serif;
    font-size: 14px;
}

QComboBox:hover {
    border-color: #40a9ff;
    color: #1890ff;
}

QComboBox:focus {
    border-color: #1890ff;
    outline: none;
}

QComboBox:on {
    border-color: #1890ff;
    background-color: #f6ffed;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: right center;
    width: 20px;
    border-left: none;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

QComboBox::down-arrow {
    image: url(:/images/arrow-down.png);
    width: 12px;
    height: 12px;
}

QComboBox::down-arrow:on {
    top: 1px;
    left: 1px;
}

QComboBox QAbstractItemView {
    border: 1px solid #d9d9d9;
    background-color: white;
    selection-background-color: #e6f7ff;
    selection-color: #1890ff;
    outline: none;
    border-radius: 4px;
    padding: 4px;
}

QComboBox QAbstractItemView::item {
    height: 32px;
    padding: 6px 12px;
    border: none;
    border-radius: 2px;
}

QComboBox QAbstractItemView::item:hover {
    background-color: #f0f9ff;
    color: #1890ff;
}

QComboBox QAbstractItemView::item:selected {
    background-color: #e6f7ff;
    color: #1890ff;
}</string>
             </property>
             <item>
              <property name="text">
               <string>一号台</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>二号台</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>三号台</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>四号台</string>
              </property>
             </item>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QTabWidget" name="dataTabWidget">
          <property name="styleSheet">
           <string notr="true">QTabWidget::pane { border: none; background-color: white; }
QTabBar::tab { background-color: #f0f0f0; color: #333333; padding: 8px 20px; border: none; 
min-width: 80px; font-size: 14px; text-align: left !important; margin-left: 0px !important; }
QTabBar::tab:selected { background-color: white; border-bottom: 2px solid #1890ff; color: #1890ff; }
QTabBar::tab:!selected { background-color: #f5f5f5; }</string>
          </property>
          <property name="tabShape">
           <enum>QTabWidget::Rounded</enum>
          </property>
          <property name="usesScrollButtons">
           <bool>true</bool>
          </property>
          <property name="documentMode">
           <bool>true</bool>
          </property>
          <property name="tabsClosable">
           <bool>true</bool>
          </property>
          <property name="movable">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../resources/resources.qrc"/>
 </resources>
 <connections/>
</ui>
