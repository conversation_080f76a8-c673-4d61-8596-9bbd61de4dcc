#include "tcp_client.h"

#include <QNetworkProxy>
#include <QMutexLocker>

/**
 * 获取单例实例
 */
TcpClient& TcpClient::getInstance() {
    static TcpClient instance;
    return instance;
}

/**
 * 私有构造函数
 */
TcpClient::TcpClient() : 
    m_socket(nullptr),
    m_serverIp("127.0.0.1"),
    m_serverPort(8080),
    m_reconnectEnabled(true),
    m_reconnectInterval(5000),
    m_maxReconnectAttempts(5),
    m_reconnectAttempts(0),
    m_reconnectTimer(nullptr),
    m_receiveBuffer()
{
    // 创建套接字
    m_socket = new QTcpSocket(this);
    // 防止本地代理影响
    QNetworkProxy noProxy;
    noProxy.setType(QNetworkProxy::NoProxy);
    m_socket->setProxy(noProxy);

    // 连接信号和槽
    connect(m_socket, &QTcpSocket::readyRead, this, &TcpClient::onReadyRead);
    connect(m_socket, &QTcpSocket::connected, this, &TcpClient::onConnected);
    connect(m_socket, &QTcpSocket::disconnected, this, &TcpClient::onDisconnected);
#if QT_VERSION >= QT_VERSION_CHECK(5, 15, 0)
    connect(m_socket, &QAbstractSocket::errorOccurred, this, &TcpClient::onError);
#else
    connect(m_socket, SIGNAL(error(QAbstractSocket::SocketError)), this, SLOT(onError(QAbstractSocket::SocketError)));
#endif


    // 创建重连定时器
    m_reconnectTimer = new QTimer(this);
    connect(m_reconnectTimer, &QTimer::timeout, this, &TcpClient::tryReconnect);

    // 加载配置
    loadConfig(m_currentBenchId);

    qDebug() << "TcpClient 初始化完成，服务器地址:" << m_serverIp << "端口:" << m_serverPort;
}

/**
 * 析构函数
 */
TcpClient::~TcpClient() {
    if (m_socket && m_socket->state() == QAbstractSocket::ConnectedState) {
        m_socket->disconnectFromHost();
        if (m_socket->state() != QAbstractSocket::UnconnectedState) {
            m_socket->waitForDisconnected(1000);
        }
    }

    if (m_reconnectTimer) {
        m_reconnectTimer->stop();
    }

    qDebug() << "TcpClient 销毁";
}

/**
 * 连接到服务器
 */
bool TcpClient::connectToServer()
{
    QMutexLocker locker(&m_mutex);

    if (m_socket->state() == QAbstractSocket::ConnectedState) {
        qDebug() << "已经连接到服务器";
        return true;
    }

    // 从配置文件重新加载 IP 和端口
    reloadFromConfig();

    qDebug() << "尝试连接到服务器:" << m_serverIp << "端口:" << m_serverPort;
    m_socket->connectToHost(m_serverIp, m_serverPort);

    // 等待连接完成
    bool connected = m_socket->waitForConnected(5000);
    if (!connected) {
        qWarning() << "连接服务器失败:" << m_socket->errorString();
        if (m_reconnectEnabled && m_reconnectAttempts < m_maxReconnectAttempts) {
            m_reconnectTimer->start(m_reconnectInterval);
            qDebug() << "将在" << m_reconnectInterval / 1000 << "秒后尝试重连";
        }
    }

    return connected;
}

/**
 * 断开与服务器的连接
 */
void TcpClient::disconnectFromServer()
{
    QMutexLocker locker(&m_mutex);

    if (m_reconnectTimer->isActive()) {
        m_reconnectTimer->stop();
    }

    // 清空接收缓冲区
    m_receiveBuffer.clear();

    if (m_socket->state() == QAbstractSocket::ConnectedState) {
        qDebug() << "断开与服务器的连接";
        m_socket->disconnectFromHost();
        if (m_socket->state() != QAbstractSocket::UnconnectedState) {
            m_socket->waitForDisconnected(1000);
        }
    }
}

/**
 * 发送数据
 */
bool TcpClient::sendData(const QByteArray& data) {
    QMutexLocker locker(&m_mutex);

    if (m_socket->state() != QAbstractSocket::ConnectedState) {
        qWarning() << "未连接到服务器，无法发送数据";
        return false;
    }

    qint64 bytesWritten = m_socket->write(data);
    if (bytesWritten != data.size()) {
        qWarning() << "数据发送不完整，已发送:" << bytesWritten << "总大小:" << data.size();
        return false;
    }

    qDebug() << "成功发送数据，大小:" << bytesWritten << "字节";
    return true;
}

/**
 * 处理接收到的数据
 */
void TcpClient::onReadyRead() {
    QMutexLocker locker(&m_mutex);

    // 将新接收的数据追加到缓冲区
    m_receiveBuffer.append(m_socket->readAll());
    
    // 循环处理缓冲区中的完整数据包
    while (m_receiveBuffer.size() >= 4) { // 至少需要读取4字节的长度字段
        // 解析头部的长度字段（包含了内容长度+4），小端字节序
        quint32 packetSize = 0;
        memcpy(&packetSize, m_receiveBuffer.constData(), 4);
        
        // 检查长度字段是否合理，避免恶意数据导致的问题
        if (packetSize < 4 || packetSize > 1024 * 1024) { // 设置一个合理的最大包大小限制
            qWarning() << "接收到异常数据包大小:" << packetSize << "，清空缓冲区";
            m_receiveBuffer.clear();
            break;
        }
        
        // 检查缓冲区是否包含完整的数据包
        if (m_receiveBuffer.size() >= packetSize) {
            // 提取完整的数据包（不包括长度字段）
            QByteArray completePacket = m_receiveBuffer.mid(0, packetSize);
            
            // 从缓冲区移除已处理的数据包
            m_receiveBuffer.remove(0, packetSize);
            
            // 发送完整的数据包信号
            qDebug() << "接收到完整数据包，大小:" << completePacket.size() << "字节";
            emit dataReceived(completePacket);
        }
        else {
            // 数据包不完整，等待更多数据
            qDebug() << "数据包不完整，等待更多数据。当前缓冲区大小:" << m_receiveBuffer.size() 
                     << "，需要大小:" << packetSize;
            break;
        }
    }
}

/**
 * 处理连接状态改变
 */
void TcpClient::onConnected() {
    qDebug() << "已连接到服务器:" << m_serverIp << "端口:" << m_serverPort;
    
    m_reconnectAttempts = 0;
    if (m_reconnectTimer->isActive()) {
        m_reconnectTimer->stop();
    }

    emit connectionStatusChanged(true);
}

/**
 * 处理断开连接
 */
void TcpClient::onDisconnected() {
    qDebug() << "与服务器断开连接";

    emit connectionStatusChanged(false);

    // 如果启用了重连，则尝试重新连接
    if (m_reconnectEnabled && m_reconnectAttempts < m_maxReconnectAttempts) {
        m_reconnectTimer->start(m_reconnectInterval);
        qDebug() << "将在" << m_reconnectInterval / 1000 << "秒后尝试重连";
    }
}

/**
 * 处理连接错误
 */
void TcpClient::onError(QAbstractSocket::SocketError socketError) {
    QString errorMsg = m_socket->errorString();
    qWarning() << "TCP连接错误:" << errorMsg << "错误代码:" << socketError;

    emit connectionError(errorMsg);

    // 某些错误可能需要特殊处理
    if (socketError != QAbstractSocket::RemoteHostClosedError) {
        // 远程主机关闭不是真正的错误，不需要特殊处理
    }
}

/**
 * 尝试重新连接
 */
void TcpClient::tryReconnect() {
    QMutexLocker locker(&m_mutex);

    if (m_socket->state() == QAbstractSocket::ConnectedState) {
        qDebug() << "已经连接到服务器，取消重连";
        m_reconnectTimer->stop();
        return;
    }

    m_reconnectAttempts++;
    qDebug() << "尝试重新连接，第" << m_reconnectAttempts << "次，共" << m_maxReconnectAttempts << "次";

    if (m_reconnectAttempts > m_maxReconnectAttempts) {
        qWarning() << "达到最大重连次数，停止重连";
        m_reconnectTimer->stop();
        return;
    }

    // 重新尝试连接
    m_socket->connectToHost(m_serverIp, m_serverPort);
}

/**
 * 从配置文件重新加载 IP 和端口
 */
void TcpClient::reloadFromConfig() {
    // 确保配置文件存在
    ensureConfigExists();

    loadConfig(m_currentBenchId);
}

/**
 * 设置服务器 IP 地址
 */
void TcpClient::setServerIp(const QString& ip) {
    QMutexLocker locker(&m_mutex);
    m_serverIp = ip;
    saveConfig();
}

/**
 * 设置服务器端口
 */
void TcpClient::setServerPort(quint16 port) {
    QMutexLocker locker(&m_mutex);
    m_serverPort = port;
    saveConfig();
}

/**
 * 获取服务器 IP 地址
 */
QString TcpClient::getServerIp() const {
    QMutexLocker locker(&m_mutex);
    return m_serverIp;
}

/**
 * 获取服务器端口
 */
quint16 TcpClient::getServerPort() const {
    QMutexLocker locker(&m_mutex);
    return m_serverPort;
}

/**
 * 检查是否已连接
 */
bool TcpClient::isConnected() const {
    QMutexLocker locker(&m_mutex);
    return m_socket->state() == QAbstractSocket::ConnectedState;
}

/**
 * 加载配置
 */
void TcpClient::loadConfig(int benchId) {
    // 获取配置文件路径
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QString configFile = QDir(configPath).filePath("MicroserviceMonitor.conf");

    // 从配置文件中读取 TCP 配置
    QSettings settings(configFile, QSettings::IniFormat);

    QString srvIp = "TCP/serverIp" + QString::number(benchId);
    QString srvPort = "TCP/serverPort" + QString::number(benchId);
    // 读取服务器 IP 和端口
    m_serverIp = settings.value(srvIp, "127.0.0.1").toString();
    m_serverPort = settings.value(srvPort, 8080).toUInt();

    // 读取重连配置
    m_reconnectEnabled = settings.value("TCP/reconnectEnabled", true).toBool();
    m_reconnectInterval = settings.value("TCP/reconnectInterval", 5000).toInt();
    m_maxReconnectAttempts = settings.value("TCP/maxReconnectAttempts", 5).toInt();

    qDebug() << "从配置文件加载 TCP 配置 - IP:" << m_serverIp 
             << "端口:" << m_serverPort 
             << "重连:" << (m_reconnectEnabled ? "启用" : "禁用") 
             << "间隔:" << m_reconnectInterval 
             << "最大尝试次数:" << m_maxReconnectAttempts;
}

/**
 * 保存配置
 */
void TcpClient::saveConfig() {
    // 获取配置文件路径
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QString configFile = QDir(configPath).filePath("MicroserviceMonitor.conf");

    // 保存 TCP 配置到配置文件
    QSettings settings(configFile, QSettings::IniFormat);

    // 保存服务器 IP 和端口
    settings.setValue("TCP/serverIp", m_serverIp);
    settings.setValue("TCP/serverPort", m_serverPort);

    // 保存重连配置
    settings.setValue("TCP/reconnectEnabled", m_reconnectEnabled);
    settings.setValue("TCP/reconnectInterval", m_reconnectInterval);
    settings.setValue("TCP/maxReconnectAttempts", m_maxReconnectAttempts);

    settings.sync();

    qDebug() << "TCP 配置已保存到配置文件";
}

/**
 * 确保配置文件存在
 */
void TcpClient::ensureConfigExists() {
    // 获取配置文件路径
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QDir dir(configPath);
    if (!dir.exists()) {
        dir.mkpath(".");
    }
    QString configFile = dir.filePath("MicroserviceMonitor.conf");

    // 检查配置文件是否存在
    QFile file(configFile);
    if (!file.exists()) {
        qDebug() << "配置文件不存在，创建默认配置文件";
        
        // 创建默认配置文件
        QSettings settings(configFile, QSettings::IniFormat);
        
        // 添加默认的 TCP 配置段
        settings.setValue("TCP/serverIp", "127.0.0.1");
        settings.setValue("TCP/serverPort", 8080);
        settings.setValue("TCP/reconnectEnabled", true);
        settings.setValue("TCP/reconnectInterval", 5000);
        settings.setValue("TCP/maxReconnectAttempts", 5);
        
        settings.sync();
    } else {
        // 检查是否存在 TCP 配置段，如果不存在则添加
        QSettings settings(configFile, QSettings::IniFormat);
        if (!settings.childGroups().contains("TCP")) {
            qDebug() << "TCP 配置段不存在，添加默认配置";
            
            settings.setValue("TCP/serverIp", "127.0.0.1");
            settings.setValue("TCP/serverPort", 8080);
            settings.setValue("TCP/reconnectEnabled", true);
            settings.setValue("TCP/reconnectInterval", 5000);
            settings.setValue("TCP/maxReconnectAttempts", 5);
            
            settings.sync();
        }
    }
} 

//切换实验台号
int TcpClient::updaeteBench(int benchId)
{
    m_currentBenchId = benchId;
    //断开链接
    disconnectFromServer();
    //获取新台号ip和端口
    loadConfig(benchId);
    //下次订阅会重连
}
