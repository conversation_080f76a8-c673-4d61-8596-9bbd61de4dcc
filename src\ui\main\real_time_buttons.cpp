#include "real_time_buttons.h"
#include <QIcon>

QString RealTimeButtons::getButtonStyle()
{
    return "QPushButton {"
           "    border: none;"
           "    color: white;"
           "    font-size: 11px;"
           "    min-height: 60px;"
           "    max-height: 60px;"
           "    padding-top: 55px;"
           "    text-align: center;"
           "    border-radius: 4px;"
           "    margin: 2px 5px;"
           "    background-position: center 5px;"
           "    background-repeat: no-repeat;"
           "    background-size: 32px 32px;"
           "}"
           "QPushButton:hover {"
           "    background-color: rgba(255, 255, 255, 0.15);"
           "    border: 1px solid rgba(255, 255, 255, 0.3);"
           "}"
           "QPushButton:checked {"
           "    background-color: rgba(0, 0, 0, 0.2);"
           "    border: 1px solid rgba(255, 255, 255, 0.5);"
           "    box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.2);"
           "}";
}

void RealTimeButtons::createMonitorButtons(QWidget* parent, QVBoxLayout* sideMenuLayout, QPushButton* homeButton,
                                           QButtonGroup*& buttonGroup,
                                           QPushButton*& valueMonitorButton,
                                           QPushButton*& graphMonitorButton,
                                           QPushButton*& timeSeriesButton,
                                           QPushButton*& dataLoadButton,
                                           QPushButton*& unitConfigButton)
{
    // 保存需要保留的按钮
    // QList<QWidget*> preservedWidgets;
    // preservedWidgets << homeButton;

    // // 遍历布局中的所有项目
    // for (int i = 0; i < sideMenuLayout->count(); ++i) {
    //     QLayoutItem* item = sideMenuLayout->itemAt(i);
    //     if (!item) continue;

    //     QWidget* widget = item->widget();
    //     if (widget) {
    //         // 检查是否是需要保留的按钮
    //         if (widget->objectName() == "overviewButton" ||
    //             widget->objectName() == "documentButton" ||
    //             widget->objectName() == "managementButton") {
    //             preservedWidgets << widget;
    //         }
    //     }
    // }

    // // 清除布局中的所有项目
    // QLayoutItem* item;
    // while ((item = sideMenuLayout->takeAt(0)) != nullptr) {
    //     QWidget* widget = item->widget();
    //     if (widget && !preservedWidgets.contains(widget)) {
    //         delete widget;
    //     }
    //     delete item;
    // }

    // // 重新添加保留的按钮到布局
    // for (QWidget* widget : preservedWidgets) {
    //     if (widget != homeButton) {  // homeButton已经在布局中
    //         sideMenuLayout->addWidget(widget);
    //     }
    // }

    // 创建按钮组
    buttonGroup = new QButtonGroup(parent);
    buttonGroup->addButton(homeButton);

    // 创建数值监控按钮
    valueMonitorButton = new QPushButton("数值监控", parent);
    valueMonitorButton->setIcon(QIcon());
    valueMonitorButton->setStyleSheet(getButtonStyle() + "background-image: url(:/images/hostOverview1.png);");
    valueMonitorButton->setCheckable(true);
    buttonGroup->addButton(valueMonitorButton);
    // sideMenuLayout->addWidget(valueMonitorButton);

    // 创建图形监控按钮
    graphMonitorButton = new QPushButton("图形监控", parent);
    graphMonitorButton->setIcon(QIcon());
    graphMonitorButton->setStyleSheet(getButtonStyle() + "background-image: url(:/images/hostOverview2.png);");
    graphMonitorButton->setCheckable(true);
    buttonGroup->addButton(graphMonitorButton);
    // sideMenuLayout->addWidget(graphMonitorButton);

    // 创建时序图按钮
    timeSeriesButton = new QPushButton("时序图", parent);
    timeSeriesButton->setIcon(QIcon());
    timeSeriesButton->setStyleSheet(getButtonStyle() + "background-image: url(:/images/hostOverview3.png);");
    timeSeriesButton->setCheckable(true);
    buttonGroup->addButton(timeSeriesButton);
    // sideMenuLayout->addWidget(timeSeriesButton);

    // 创建数据加载监控按钮
    dataLoadButton = new QPushButton("数据加载监控", parent);
    dataLoadButton->setIcon(QIcon());
    dataLoadButton->setStyleSheet(getButtonStyle() + "background-image: url(:/images/hostOverview4.png);");
    dataLoadButton->setCheckable(true);
    buttonGroup->addButton(dataLoadButton);
    // sideMenuLayout->addWidget(dataLoadButton);

    // 创建单位配置按钮
    unitConfigButton = new QPushButton("单位配置", parent);
    unitConfigButton->setIcon(QIcon());
    unitConfigButton->setStyleSheet(getButtonStyle() + "background-image: url(:/images/unitConfig.png);");
    unitConfigButton->setCheckable(true);
    buttonGroup->addButton(unitConfigButton);
    // sideMenuLayout->addWidget(unitConfigButton);

    // 添加弹性空间
    // sideMenuLayout->addStretch();

    // 默认选中valueMonitorButton
    // valueMonitorButton->setChecked(true);
}
