#include "template_list_widget.h"
#include "ui_template_list_widget.h"

#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDateTime>
#include <QFile>
#include <QMessageBox>
#include <QFileInfo>
#include <QCoreApplication>

#include "utils/user_session.h"
#include "utils/api_url_manager.h"
#include "utils/file_list_service.h"
#include "third/parseAndAnalysis.h"

TemplateListWidget::TemplateListWidget(QWidget *parent)
    : QWidget(parent),
    ui(new Ui::TemplateListWidget)
{
    ui->setupUi(this);
    setupUI();
    loadTemplates();
}

void TemplateListWidget::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);

    m_scrollArea = new QScrollArea(this);
    m_scrollArea->setWidgetResizable(true);
    m_scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_scrollArea->setStyleSheet("QScrollArea { border: none; }");

    m_scrollContent = new QWidget(m_scrollArea);
    m_scrollContent->setStyleSheet("background:rgb(255, 255, 255);");
    m_scrollLayout = new QVBoxLayout(m_scrollContent);
    m_scrollLayout->setSpacing(8);
    m_scrollLayout->setContentsMargins(16, 16, 16, 16);

    m_scrollArea->setWidget(m_scrollContent);
    m_mainLayout->addWidget(m_scrollArea);

    m_networkManager = new QNetworkAccessManager(this);
    connect(m_networkManager, &QNetworkAccessManager::finished,
            this, &TemplateListWidget::onNetworkReply);
}

void TemplateListWidget::loadTemplates()
{
    QString token = UserSession::getInstance().getToken();

    // 获取用户名和ICD版本
    QString username = UserSession::getInstance().getUsername();
    void *m_decoderToken = FileListService::getInstance().getParseandanalysisP();
    // 从XML中获取ICD版本
    QString icdVersion = QString::fromStdString(getCurrentIcdVersion(m_decoderToken));

    // 创建参数映射
    QMap<QString, QString> params;
    params["icdVersion"] = icdVersion;

    // 构建带参数的URL
    QNetworkRequest request;
    if(!m_isGraph)
    {
        request = QNetworkRequest(ApiUrlManager::getInstance().getUrlWithParams(ApiUrlManager::GET_TEMPLATE_LIST,params));
    }
    else {
        request = QNetworkRequest(ApiUrlManager::getInstance().getUrlWithParams(ApiUrlManager::GRAPH_TEMPLATE_GET,params));
    }
    request.setRawHeader("accept", "*/*");
    if (!token.isEmpty()) {
        request.setRawHeader("Authorization", token.toUtf8());
    }

    m_networkManager->get(request);
}

void TemplateListWidget::onNetworkReply(QNetworkReply *reply)
{
    if (reply->error() == QNetworkReply::NoError) {
        QByteArray data = reply->readAll();
        QJsonDocument doc = QJsonDocument::fromJson(data);
        QJsonObject obj = doc.object();

        if (obj["success"].toBool()) {
            QJsonArray templateArray = obj["data"].toArray();
            parseTemplateData(templateArray);
        }
    }
    reply->deleteLater();
}

void TemplateListWidget::parseTemplateData(const QJsonArray &data)
{
    // Clear existing items
    QLayoutItem* item;
    while ((item = m_scrollLayout->takeAt(0)) != nullptr) {
        delete item->widget();
        delete item;
    }

    QString currentUserName = UserSession::getInstance().getUsername();

    // Add new items
    for (const QJsonValue &value : data) {
        QJsonObject obj = value.toObject();
        TemplateListItem *item = new TemplateListItem(this);

        // 转换时间戳为可读格式
        qint64 timestamp = obj["createTime"].toDouble();
        QDateTime dateTime = QDateTime::fromMSecsSinceEpoch(timestamp);
        QString formattedTime = dateTime.toString("yyyy-MM-dd HH:mm:ss");

        bool isCurrentUser = (obj["userName"].toString() == currentUserName);
        item->setTemplateInfo(
            obj["name"].toString(),
            obj["userName"].toString(),
            formattedTime,
            obj["id"].toInt(),
            isCurrentUser,
            obj["url"].toString()  // 添加url字段
        );

        connect(item, &TemplateListItem::downloadClicked,
                this, &TemplateListWidget::onDownloadClicked);
        connect(item, &TemplateListItem::deleteClicked,
                this, &TemplateListWidget::onDeleteClicked);

        m_scrollLayout->addWidget(item);
    }
    m_scrollLayout->addStretch();
}

void TemplateListWidget::onDownloadClicked(int id)
{
    emit templateDownload();
//    // 查找对应的模板项
//    for (int i = 0; i < m_scrollLayout->count(); i++) {
//        QLayoutItem* item = m_scrollLayout->itemAt(i);
//        if (item && item->widget()) {
//            TemplateListItem* templateItem = qobject_cast<TemplateListItem*>(item->widget());
//            if (templateItem && templateItem->getId() == id) {
//                QString url = templateItem->getUrl();
//                QString templateName = templateItem->getTemplateName();

//                // 创建网络请求
//                QNetworkRequest request(url);
//                request.setRawHeader("accept", "*/*");
//                QString token = UserSession::getInstance().getToken();
//                if (!token.isEmpty()) {
//                    request.setRawHeader("Authorization", token.toUtf8());
//                }

//                // 发送下载请求
//                QNetworkReply* reply = m_networkManager->get(request);

//                // 连接下载完成的信号
//                connect(reply, &QNetworkReply::finished, [this, reply, templateName]() {
//                    if (reply->error() == QNetworkReply::NoError) {
//                        // 获取下载的数据
//                        QByteArray data = reply->readAll();

//                        // 保存到本地方案文件
//                        QString userName = UserSession::getInstance().getUsername();
//                        QString localPath = QCoreApplication::applicationDirPath() + "/scheme/" + userName + "_solution.xml";

//                        QFile file(localPath);
//                        if (file.open(QIODevice::WriteOnly)) {
//                            file.write(data);
//                            file.close();

//                            // 上传到服务器
//                            uploadToServer(localPath, templateName);
//                        } else {
//                            QMessageBox::warning(this, tr("保存失败"),
//                                tr("无法保存模板到本地: %1").arg(file.errorString()));
//                        }
//                    } else {
//                        QMessageBox::warning(this, tr("下载失败"),
//                            tr("下载模板失败: %1").arg(reply->errorString()));
//                    }
//                    reply->deleteLater();
//                });
//                break;
//            }
//        }
//    }

}

void TemplateListWidget::onDeleteClicked(int id)
{
    // 查找对应的模板项
    for (int i = 0; i < m_scrollLayout->count(); i++) {
        QLayoutItem* item = m_scrollLayout->itemAt(i);
        if (item && item->widget()) {
            TemplateListItem* templateItem = qobject_cast<TemplateListItem*>(item->widget());
            if (templateItem && templateItem->getId() == id) {
                QString templateName = templateItem->getTemplateName();
                m_scrollLayout->removeWidget(templateItem);
                //delete templateItem;
                // 断开所有信号连接
                templateItem->disconnect();
                // 设置父对象为nullptr，这样deleteLater会正确删除
                templateItem->setParent(nullptr);
                // 使用deleteLater安全删除
                templateItem->deleteLater();
                // 发送信号通知方案被删除
                emit templateDeleted(templateName);
                break;
            }
        }
    }
}

void TemplateListWidget::uploadToServer(const QString& filePath, const QString& templateName)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        QMessageBox::warning(this, tr("上传失败"),
            tr("无法打开本地文件: %1").arg(file.errorString()));
        return;
    }

    QByteArray fileData = file.readAll();
    file.close();

    // 创建网络请求
    QNetworkRequest request;
    if(!m_isGraph)
    {
        request= QNetworkRequest(ApiUrlManager::getInstance().getUrl(ApiUrlManager::SCHEME_UPDATE));
    }else {
        request= QNetworkRequest(ApiUrlManager::getInstance().getUrl(ApiUrlManager::GRAPH_SCHEME_UPDATE));
    }
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    QString token = UserSession::getInstance().getToken();
    if (!token.isEmpty()) {
        request.setRawHeader("Authorization", token.toUtf8());
    }

    // 准备请求数据
    QJsonObject jsonObj;
    jsonObj["name"] = templateName;
    jsonObj["content"] = QString(fileData.toBase64());
    QJsonDocument doc(jsonObj);
    QByteArray data = doc.toJson();

    // 发送上传请求
    QNetworkReply* reply = m_networkManager->post(request, data);

    // 连接请求完成的信号
    connect(reply, &QNetworkReply::finished, [this, reply, templateName]() {
        if (reply->error() == QNetworkReply::NoError) {
            QByteArray response = reply->readAll();
            QJsonDocument doc = QJsonDocument::fromJson(response);
            QJsonObject obj = doc.object();

            if (obj["success"].toBool()) {
                QMessageBox::information(this, tr("上传成功"),
                    tr("模板 %1 已成功上传到服务器").arg(templateName));
            } else {
                QMessageBox::warning(this, tr("上传失败"),
                    tr("上传模板失败: %1").arg(obj["message"].toString()));
            }
        } else {
            QMessageBox::warning(this, tr("上传失败"),
                tr("上传模板失败: %1").arg(reply->errorString()));
        }
        reply->deleteLater();
    });
}

void TemplateListWidget::refreshTemplateList()
{
    // 清空现有内容
    QLayoutItem* item;
    while ((item = m_scrollLayout->takeAt(0)) != nullptr) {
        delete item->widget();
        delete item;
    }

    // 重新加载模板列表
    loadTemplates();
}
void TemplateListWidget::setMonType(bool isGraph)
{
    m_isGraph = isGraph;
}
