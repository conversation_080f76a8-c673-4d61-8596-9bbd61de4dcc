<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1280</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>微服务监控系统</string>
  </property>
  <property name="styleSheet">
   <string>QMainWindow {
    background-color: #f0f2f5;
}

#sideMenu {
    background-color: #1890ff;
    min-width: 80px;
    max-width: 80px;
}

#sideMenu QPushButton {
    border: none;
    color: white;
    padding: 15px 0;
    text-align: center;
    font-size: 14px;
    min-height: 80px;
    max-height: 80px;
}

#sideMenu QPushButton:hover {
    background-color: #40a9ff;
}

#sideMenu QPushButton:checked {
    background-color: #096dd9;
}

#topBar {
    background-color: #1890ff;
    min-height: 50px;
    max-height: 50px;
    border-bottom: 1px solid #e8e8e8;
}

#topBar QLabel {
    color: white;
    font-weight: bold;
}

#titleLabel {
    font-size: 16px;
    margin-left: 10px;
}

#contentArea {
    background-color: white;
    margin: 10px;
    border-radius: 4px;
}

QLabel#usernameLabel {
    color: white;
    font-size: 14px;
    padding-right: 10px;
    border-right: 1px solid rgba(255, 255, 255, 0.5);
    margin-right: 10px;
}
</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,1">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QWidget" name="sideMenu" native="true">
      <layout class="QVBoxLayout" name="verticalLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QPushButton" name="homeButton">
         <property name="text">
          <string>返回主页</string>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="overviewButton">
         <property name="text">
          <string>主机总览</string>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="documentButton">
         <property name="text">
          <string>健康管理</string>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="managementButton">
         <property name="text">
          <string>管理中心</string>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="topBar" native="true">
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QLabel" name="titleLabel">
           <property name="text">
            <string>微服务监控系统</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="usernameLabel">
           <property name="text">
            <string>用户名</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="contentArea" native="true">
        <layout class="QVBoxLayout" name="contentLayout">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
        </layout>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
