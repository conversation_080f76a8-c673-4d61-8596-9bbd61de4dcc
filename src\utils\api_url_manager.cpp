#include "api_url_manager.h"

#include <QDebug>
#include <QUrlQuery>
#include <QSettings>
#include <QStandardPaths>
#include <QDir>

/**
 * 获取单例实例
 */
ApiUrlManager& ApiUrlManager::getInstance() {
    static ApiUrlManager instance;
    return instance;
}

/**
 * 私有构造函数
 */
ApiUrlManager::ApiUrlManager() {
    // 初始化API路径映射表
    initApiPathMap();

    // 获取配置文件路径
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QString configFile = QDir(configPath).filePath("MicroserviceMonitor.conf");

    // 从配置文件中读取服务器地址
    QSettings settings(configFile, QSettings::IniFormat);
    QString serverAddress = settings.value("Server/address", "*************:9001").toString();

    // 设置基础URL
    if (!serverAddress.isEmpty()) {
        if (!serverAddress.startsWith("http://") && !serverAddress.startsWith("https://")) {
            m_baseUrl = "http://" + serverAddress;
        } else {
            m_baseUrl = serverAddress;
        }
    } else {
        // 如果配置文件中没有服务器地址，则使用默认值
        m_baseUrl = "http://*************:9001";
    }

    // 确保URL格式正确
    if (m_baseUrl.endsWith("/")) {
        m_baseUrl.chop(1);
    }

    qDebug() << "ApiUrlManager initialized with base URL from config:" << m_baseUrl;
}

/**
 * 初始化API路径映射表
 */
void ApiUrlManager::initApiPathMap() {
    // 用户相关API
    m_apiPaths[USER_LOGIN] = "/user/login";
    m_apiPaths[USER_LOGOUT] = "/user/exit";
    m_apiPaths[USER_ADD] = "/user/addUser";
    m_apiPaths[USER_ALL] = "/user/getAllUser";
    m_apiPaths[USER_DELETE] = "user/delete";
    m_apiPaths[RESET_USER] = "user/resetUser";

    // 文件相关API
    m_apiPaths[FILE_LIST] = "/icd/list";
    m_apiPaths[FILE_VIEW] = "/file/view";
    m_apiPaths[FILE_UPLOAD] = "/file/newUpload";

    // ICD相关API
    m_apiPaths[ICD_LIST] = "/icd/list?type=name";
    
    /***数值监控方案相关API***/
    //数值方案文件下载
    m_apiPaths[SCHEME_XML] = "/file/dataProgrammeDownload";
    //数值方案文件上传
    m_apiPaths[SCHEME_UPDATE] = "/file/dataProgrammeUpload";
    //数值方案分享
    m_apiPaths[SCHEME_SHARE] = "/file/dataProgrammeTemplateUpload";

    //取消分析/数值模板删除
    m_apiPaths[SCHEME_UNSHARE] = "/file/deleteDataProgrammeTemplate";
    //获取模板列表
    m_apiPaths[GET_TEMPLATE_LIST] = "/file/getDataProgrammeTemplate";

    /***图形监控方案相关API***/
    //图形监控方案相关API
    m_apiPaths[GRAPH_SCHEME_UPDATE] = "/file/graphicProgrammeUpload";
    m_apiPaths[GRAPH_SHCEME_DOWNLOAD] = "/file/graphicProgrammeDownload";
    m_apiPaths[GRAPH_TEMPLATE_DEL] = "/file/deleteGraphicProgrammeTemplate";
    //图形方案分享
    m_apiPaths[GRAPH_TEMPLATE_ADD] = "file/graphicProgrammeTemplateUpload";
    //图形模板方案列表查询
    m_apiPaths[GRAPH_TEMPLATE_GET] = "file/getGraphicProgrammeTemplate" ;


    qDebug() << "API path mapping initialized with" << m_apiPaths.size() << "endpoints";
}

/**
 * 设置API服务器基础URL
 */
void ApiUrlManager::setBaseUrl(const QString& baseUrl) {
    // 去除尾部的斜杠，确保一致性
    m_baseUrl = baseUrl;
    if (m_baseUrl.endsWith("/")) {
        m_baseUrl.chop(1);
    }
    qDebug() << "Base URL set to:" << m_baseUrl;
}

/**
 * 从配置文件重新加载服务器地址
 */
void ApiUrlManager::reloadFromConfig() {
    // 获取配置文件路径
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QString configFile = QDir(configPath).filePath("MicroserviceMonitor.conf");

    // 从配置文件中读取服务器地址
    QSettings settings(configFile, QSettings::IniFormat);
    QString serverAddress = settings.value("Server/address", "*************:9001").toString();

    // 设置基础URL
    if (!serverAddress.isEmpty()) {
        if (!serverAddress.startsWith("http://") && !serverAddress.startsWith("https://")) {
            setBaseUrl("http://" + serverAddress);
        } else {
            setBaseUrl(serverAddress);
        }
        qDebug() << "ApiUrlManager reloaded base URL from config:" << m_baseUrl;
    }
}

/**
 * 获取API服务器基础URL
 */
QString ApiUrlManager::getBaseUrl() const {
    return m_baseUrl;
}

/**
 * 构建完整的API URL
 */
QUrl ApiUrlManager::buildUrl(const QString& path) const {
    QString urlStr = m_baseUrl;

    // 确保路径以/开头
    if (!path.isEmpty() && !path.startsWith("/")) {
        urlStr += "/";
    }

    urlStr += path;
    return QUrl(urlStr);
}

/**
 * 构建完整的API URL（基于枚举类型）
 */
QUrl ApiUrlManager::getUrl(ApiEndpoint endpoint) const {
    // 检查映射表中是否包含该端点
    if (!m_apiPaths.contains(endpoint)) {
        qWarning() << "Unknown API endpoint:" << endpoint;
        return QUrl();
    }

    return buildUrl(m_apiPaths[endpoint]);
}

/**
 * 构建带参数的API URL
 */
QUrl ApiUrlManager::getUrlWithParams(ApiEndpoint endpoint, const QMap<QString, QString>& params) const {
    // 获取基本URL
    QUrl url = getUrl(endpoint);
    if (url.isEmpty()) {
        return url;
    }

    // 添加查询参数
    QUrlQuery query;
    for (auto it = params.constBegin(); it != params.constEnd(); ++it) {
        query.addQueryItem(it.key(), it.value());
    }

    // 设置查询参数
    url.setQuery(query);
    return url;
}

/**
 * 获取用户登录URL
 */
QUrl ApiUrlManager::getUserLoginUrl() const {
    return getUrl(USER_LOGIN);
}

/**
 * 获取用户登出URL
 */
QUrl ApiUrlManager::getUserLogoutUrl() const {
    return getUrl(USER_LOGOUT);
}

/**
 * 获取文件列表URL
 */
QUrl ApiUrlManager::getFileListUrl() const {
    QMap<QString, QString> params;
    params["type"] = "name";
    return getUrlWithParams(FILE_LIST, params);
}

/**
 * 获取文件查看URL
 */
QUrl ApiUrlManager::getFileViewUrl(int id, int paragraph, const QString& type) const {
    QMap<QString, QString> params;
    params["id"] = QString::number(id);
    params["paragraph"] = QString::number(paragraph);
    params["type"] = type;
    return getUrlWithParams(FILE_VIEW, params);
}

/**
 * 获取文件上传URL
 */
QUrl ApiUrlManager::getFileUploadUrl() const {
    return getUrl(FILE_UPLOAD);
}

/**
 * 获取ICD列表URL
 */
QUrl ApiUrlManager::getIcdListUrl() const {
    QMap<QString, QString> params;
    params["type"] = "name";
    return getUrlWithParams(ICD_LIST, params);
}

/**
 * 获取添加用户URL
 */
QUrl ApiUrlManager::getUserAddUrl() const {
    return getUrl(USER_ADD);
}

/**
 * 获取全部用户URL
 */
QUrl ApiUrlManager::getAllUserUrl() const {
    return getUrl(USER_ALL);
}

/**
 * 获取删除用户URL
 */
QUrl ApiUrlManager::getUserListUrl(const QString &userId) const {
    QMap<QString, QString> params;
    params["userId"] = userId;
    return getUrlWithParams(FILE_LIST, params);
}

/**
 * 获取方案XML文件的URL
 */
QUrl ApiUrlManager::getSchemeXmlUrl(const QString& userId) const {
    // 创建参数映射
    QMap<QString, QString> params;
    params["userId"] = userId;

    // 构建带参数的URL
    return getUrlWithParams(SCHEME_XML, params);
}

/**
 * 更新方案XML文件的URL
 */
QUrl ApiUrlManager::getUpdateSchemeXmlUrl() const
{
    return getUrl(SCHEME_UPDATE);
}

 QUrl ApiUrlManager::getGraphSchemeXmlUrl(const QString &userId)const
 {
     // 创建参数映射
     QMap<QString, QString> params;
     params["userId"] = userId;

     // 构建带参数的URL
     return getUrlWithParams(GRAPH_SHCEME_DOWNLOAD, params);
 }
