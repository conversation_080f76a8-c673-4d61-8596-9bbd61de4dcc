#ifndef TCP_DATA_PROCESSOR_H
#define TCP_DATA_PROCESSOR_H

#include <QObject>
#include <QByteArray>
#include <QMutex>
#include <QDebug>
#include <QJsonObject>
#include <QJsonDocument>
#include <QDateTime>
#include <QMap>
#include <QVariant>

#include "command_handlers/command_handler.h"
#include "third/tcpFrameStruct.h"

/**
 * @brief TCP 数据处理器类
 * 
 * 这个类负责处理从 TCP 客户端接收到的数据。
 * 设计为在独立线程中运行，避免阻塞主 UI 线程。
 */
class TcpDataProcessor : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 获取单例实例
     * @return TcpDataProcessor 的单例对象指针
     */
    static TcpDataProcessor* getInstance();

    /**
     * @brief 设置是否启用详细日志
     * @param enable 是否启用
     */
    void setVerboseLogging(bool enable);

    /**
     * @brief 注册命令处理器
     * @param cmdType 命令类型
     * @param handler 处理器对象
     */
    void registerCommandHandler(SUBSCRIBE_COMMAND cmdType, CommandHandler* handler);

    /**
     * @brief 取消注册命令处理器
     * @param cmdType 命令类型
     */
    void unregisterCommandHandler(SUBSCRIBE_COMMAND cmdType);

public slots:
    /**
     * @brief 处理接收到的原始数据
     * @param data 原始数据
     */
    void processData(const QByteArray &data);

    /**
     * @brief 处理二进制格式的数据
     * @param data 二进制数据
     */
    void processBinaryData(const QByteArray &data);

    /**
     * @brief 处理订阅命令数据
     * @param data 命令数据
     * @param cmdType 命令类型
     */
    void processCommandData(const QByteArray &data, SUBSCRIBE_COMMAND cmdType);

signals:
    /**
     * @brief 数据处理完成的信号
     * @param result 处理结果
     */
    void dataProcessed(const QString &result);

    /**
     * @brief 二进制数据处理完成的信号
     * @param processedData 处理后的二进制数据
     */
    void binaryDataProcessed(const QByteArray &processedData);

    /**
     * @brief 订阅命令处理完成的信号
     * @param cmdType 命令类型
     * @param success 处理是否成功
     * @param message 处理结果消息
     */
    void commandProcessed(SUBSCRIBE_COMMAND cmdType, bool success, const QString &message);

    /**
     * @brief 订阅命令处理错误信号
     * @param cmdType 命令类型
     * @param errorMessage 错误消息
     */
    void commandError(SUBSCRIBE_COMMAND cmdType, const QString &errorMessage);

    /**
     * @brief 收到订阅响应信号
     * @param response 响应数据
     */
    void subscribeResponse(const QByteArray &response);

private:
    /**
     * @brief 私有构造函数，防止外部创建实例
     */
    explicit TcpDataProcessor(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~TcpDataProcessor();

    /**
     * @brief 识别数据类型
     * @param data 要识别类型的数据
     * @return 数据类型的描述字符串
     */
    QString identifyDataType(const QByteArray &data);

    /**
     * @brief 提取命令类型
     * @param data 数据包
     * @return 命令类型，如果提取失败则返回0
     */
    SUBSCRIBE_COMMAND extractCommandType(const QByteArray &data);

    static TcpDataProcessor* instance;   // 单例实例
    static QMutex mutex;                 // 互斥锁，保证线程安全
    bool verboseLogging;                 // 是否启用详细日志

    // 命令处理器映射
    QMap<SUBSCRIBE_COMMAND, CommandHandler*> m_commandHandlers;
};

#endif // TCP_DATA_PROCESSOR_H
