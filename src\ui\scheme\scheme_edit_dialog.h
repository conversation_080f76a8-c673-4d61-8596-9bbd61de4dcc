﻿#ifndef SCHEME_EDIT_DIALOG_H
#define SCHEME_EDIT_DIALOG_H

#include <QDialog>
#include <QStandardItemModel>
#include <QTreeView>
#include <QTreeWidget>
#include <QLineEdit>
#include <QPushButton>
#include <QLabel>
#include <QSplitter>
#include <QListWidget>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QDomElement>

#include "solution.h"
#include "third/parseAndAnalysis.h"

namespace Ui {
class SchemeEditDialog;
}

class SchemeEditDialog : public QDialog
{
    Q_OBJECT

public:
    explicit SchemeEditDialog(const QString &schemeName = "", QWidget *parent = nullptr);
    ~SchemeEditDialog();
    void loadSchemeData(const QString &schemeName);

private slots:
    void onSearchTextChanged(const QString &text);
    void onSearchUpClicked();
    void onSearchDownClicked();
    void performSearch(const QString &text);
    void highlightCurrentSearchResult();
    void onUnitItemClicked(QTreeWidgetItem *item, int column);
    void onTreeItemClicked(const QModelIndex &index);
    void onConfirmClicked();
    void onCancelClicked();
    void onSaveAsClicked();
    void onDeleteSelectedItem();
    void onClearSelectionClicked();
    void onAddToSchemeClicked();
    void showContextMenu(const QPoint &pos);
    void onUploadFinished(QNetworkReply *reply);

private:
    void setupUI();
    void setupConnections();
    void initializeUnitTree();
    void initializeTreeView();
    void initializeSelectedList();
    void updateSelectedList();
    void filterTreeItems(QTreeWidgetItem *item, const QString &text);
    //更新中间信号
    void updateSchemeTreeView(QTreeWidgetItem *selectedItem);
    void addChildToSchemeTree(QTreeWidgetItem *sourceItem, QStandardItem *parentItem);
    void processChildNodes(const QModelIndex &parentIndex, QStringList &pathParts, TopicStruct &topic);
    //在返回结果前清空界面，保证下一次点开该界面是最新状态
    void clearView();
    void uploadToServer(const QString &content, const QString &fileName);
    QStandardItem* addSignalRecursive(QStandardItem* node, const QStringList& pathParts);

private:
    Ui::SchemeEditDialog *ui;
    QStandardItemModel *treeModel;
    QString currentSchemeName;

    // 主要组件
    QSplitter *mainSplitter;

    // 左侧面板组件
    QWidget *leftPanel;
    QLabel *schemeNameLabel;
    QLineEdit *searchEdit;
    QTreeWidget *unitTreeWidget;
    QPushButton *searchUpButton;
    QPushButton *searchDownButton;
    QVector<QTreeWidgetItem*> searchResults;  // 存储搜索结果
    int currentSearchIndex;  // 当前搜索结果的索引


    // 中间面板组件
    QWidget *middlePanel;
    QTreeView *schemeTreeView;
    QPushButton *clearSelectionButton;
    QPushButton *addToSchemeButton;

    // 右侧面板组件
    QWidget *rightPanel;
    QLabel *selectedLabel;
    QListWidget *selectedListWidget;
    QLineEdit *schemeNameEdit;
    QPushButton *confirmButton;
    QPushButton *cancelButton;
    QPushButton *saveAsButton;

    //接口map key:接口名称 value:接口缩略名
    QMap<QString,QString>m_interfaceMap;

    solution m_solution;
    void* m_decoderToken = nullptr;
    // 网络请求管理器
    QNetworkAccessManager *m_networkManager;

    QMap<QString, parseFromLib> m_message_infoFromLib;

signals:
    //增加方案完成信号
    // return 1:有新增 2:取消
    void editSchemefinish(int ret);
};

#endif // SCHEME_EDIT_DIALOG_H
