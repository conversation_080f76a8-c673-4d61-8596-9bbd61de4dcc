#ifndef FC_DATA_MANAGER_H
#define FC_DATA_MANAGER_H

#include <QObject>
#include <QMap>
#include <QVector>
#include <QStringList>
#include <QXmlStreamReader>
#include <QTimer>
#include <QMutex>

#include "fc_data_structures.h"
#include "tcpFrameStruct.h"

/**
 * @brief FC数据管理器
 * 参考graph_scheme_manager.h的设计模式，管理FC方案数据
 * 避免使用SchemeXmlData的复杂map结构，采用清晰的层级数据结构
 */
class FcDataManager : public QObject
{
    Q_OBJECT

public:
    explicit FcDataManager(QObject *parent = nullptr);
    ~FcDataManager();

    // 方案管理
    void addScheme(const FcSchemeData& scheme);
    FcSchemeData* getScheme(const QString& schemeName);
    QStringList getSchemeNames() const;
    void removeScheme(const QString& schemeName);
    void clearAllSchemes();

    // XML数据解析
    bool parseXmlData(const QString& xmlPath);
    bool loadSchemeFromFile(const QString& filePath);

    // 数据更新
    void updateMessageData(const QString& schemeName, const QString& messageId,
                          const QMap<QString, QString>& topicValues);
    void updateSignalData(const QString& schemeName, const QString& messageId,
                         const QString& topicId, const QString& signalId, const QString& value);

    // 监控控制
    void startMonitoring(const QString& schemeName);
    void stopMonitoring(const QString& schemeName);
    void pauseMonitoring(const QString& schemeName);
    void resumeMonitoring(const QString& schemeName);

    // 数据查询
    QVector<FcUnitMessageData> getActiveUnitMessages(const QString& schemeName) const;
    QVector<FcTopicData> getActiveTopics(const QString& schemeName) const;
    QVector<FcSignalData> getActiveSignals(const QString& schemeName) const;
    int getMessageCount(const QString& schemeName) const;
    int getActiveMessageCount(const QString& schemeName) const;

    //获取当前订阅数据
    SUBSCRIBE_PACKAGE_CMD_SUB getCurrentSubData(QString schemeName);

signals:
    // 数据变化信号
    void schemeDataChanged(const QString& schemeName);
    void schemeAdded(const QString& schemeName);  // 新增：方案添加信号
    void messageDataUpdated(const QString& schemeName, const QString& messageId);
    void topicDataUpdated(const QString& schemeName, const QString& messageId, const QString& topicId);

    // 监控状态信号
    void monitoringStarted(const QString& schemeName);
    void monitoringStopped(const QString& schemeName);
    void monitoringPaused(const QString& schemeName);
    void monitoringResumed(const QString& schemeName);

    // 错误信号
    void parseError(const QString& errorMessage);
    void dataError(const QString& schemeName, const QString& errorMessage);

private slots:
    void onDataUpdateTimer();

private:
    // 核心数据存储
    QMap<QString, FcSchemeData> m_schemes;  // 方案名 -> 方案数据

    // 监控状态管理
    QMap<QString, bool> m_monitoringStates;  // 方案名 -> 是否正在监控
    QMap<QString, bool> m_pausedStates;      // 方案名 -> 是否暂停

    // 定时器和线程安全
    QTimer* m_updateTimer;
    mutable QMutex m_dataMutex;

    // 数据验证
    bool validateSchemeData(const FcSchemeData& scheme) const;
    bool validateUnitMessageData(const FcUnitMessageData& unitMessage) const;
    bool validateTopicData(const FcTopicData& topic) const;
    bool validateSignalData(const FcSignalData& signal) const;

    // 内部工具方法
    void cleanupScheme(const QString& schemeName);
    void updateSchemeStatistics(const QString& schemeName);
    QString generateUniqueUnitMessageId(const FcUnitMessageData& unitMessage) const;
    QString generateUniqueTopicId(const FcTopicData& topic, const QString& messageId) const;
    QString generateUniqueSignalId(const FcSignalData& signal, const QString& topicId) const;
};

#endif // FC_DATA_MANAGER_H
