#include "continue_handler.h"
#include <QDebug>

/**
 * @brief 构造函数
 */
ContinueCommandHandler::ContinueCommandHandler(QObject *parent) 
    : CommandHandler(parent) 
{
    qDebug() << "创建继续命令处理器";
}

/**
 * @brief 析构函数
 */
ContinueCommandHandler::~ContinueCommandHandler() 
{
    qDebug() << "销毁继续命令处理器";
}

/**
 * @brief 处理订阅命令并返回详细结果
 */
CommandHandler::CommandResult ContinueCommandHandler::processCommand(const QByteArray &data)
{
    // 使用基类的新通用命令处理方法，返回详细结果
    return processCommonCommand(data, CMD_RESPONSE, "响应");
}
