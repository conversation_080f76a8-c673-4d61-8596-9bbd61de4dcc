#ifndef USER_DIALOG_H
#define USER_DIALOG_H

#include <QDialog>
#include <QLineEdit>
#include <QComboBox>
#include <QPushButton>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>

class UserDialog : public QDialog
{
    Q_OBJECT

public:
    explicit UserDialog(QWidget *parent = nullptr);
    ~UserDialog();

    // 获取用户输入的信息
    QString getUsername() const;
    QString getName() const;
    QString getPassword() const;
    QString getRole() const;

private slots:
    void onConfirmClicked();
    void onCancelClicked();

private:
    void setupUI();
    void setupConnections();

    QLineEdit *usernameEdit;  // 账号输入框
    QLineEdit *nameEdit;      // 姓名输入框
    QLineEdit *passwordEdit;  // 密码输入框
    QComboBox *roleComboBox;  // 角色选择框

    QPushButton *confirmBtn;  // 确认按钮
    QPushButton *cancelBtn;   // 取消按钮

    QVBoxLayout *mainLayout;
    QFormLayout *formLayout;
    QHBoxLayout *buttonLayout;
};

#endif // USER_DIALOG_H
