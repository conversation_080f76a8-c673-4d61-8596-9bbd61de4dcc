#ifndef PARSEANDANALYSIS_TEST_H
#define PARSEANDANALYSIS_TEST_H

#include <QObject>
#include <QTest>
#include <QString>
#include <QDebug>

// 根据编译选项决定是直接包含头文件或使用动态加载
#ifdef PARSE_ANALYSIS_DLL_LOAD
#include "parseandanalysis/dll_parseandanalysis.h"
#else
#include "parseAndAnalysis.h"
#include "icdStructDef.h"
#include "frameStruct.h"
#endif

class ParseAndAnalysisTest : public QObject
{
    Q_OBJECT

public:
    explicit ParseAndAnalysisTest(QObject *parent = nullptr);
    ~ParseAndAnalysisTest();

private slots:
    // 测试初始化和清理
    void initTestCase();
    void cleanupTestCase();

    // 测试用例
    void testAdd();
    void testInitDecoder();
    void testGetFuncUnitList();
    void testSearchInterfaces();

private:
    void* m_decoderToken = nullptr;
    
    // 辅助函数，用于检查DLL是否有效
    bool checkDllValidity(const QString& dllPath);
};

#endif // PARSEANDANALYSIS_TEST_H 
