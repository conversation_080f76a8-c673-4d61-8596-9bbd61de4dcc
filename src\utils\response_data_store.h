#ifndef RESPONSE_DATA_STORE_H
#define RESPONSE_DATA_STORE_H

#include <QObject>
#include <QMap>
#include <QVector>
#include <QString>
#include <QMutex>
#include <QSet>

/**
 * @brief 响应数据存储类
 * 
 * 该类负责存储和管理从服务器接收到的响应数据。
 * 它将数据处理和数据存储分离，减少组件之间的耦合。
 * 使用单例模式确保全局只有一个数据存储实例。
 */
class ResponseDataStore : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 获取单例实例
     * @return 单例实例引用
     */
    static ResponseDataStore& getInstance();

    /**
     * @brief 批量更新多个信号数据
     * @param dataMap 包含多个信号数据的映射
     */
    void updateAllData(const QMap<QString, QVector<QString>>& dataMap);

    /**
     * @brief 批量更新多个信号数据
     * @param topicVar 变长topic
     */
    void updateAllData(const QString& topicVar);

    /**
     * @brief 清除所有数据
     */
    void clearData();

    /**
     * @brief 获取所有数据
     */
    void getAndClearResponseData(QMap<QString, QVector<QString>> &outDataMap,
                      QSet<QString> &outUpdatedSignals,
                      bool &outDataChanged);

    bool getCmdReuslt();
    bool setCmdReuslt(bool result);

    /**
        * @brief 根据TreeView索引key获取信号数据
        * @param treeViewKey TreeView索引key（格式：sourceFuncId_pubSubTopicId_signalId）
        * @return 信号数据向量（[信号值, 时间戳]）
        */
    QVector<QString> getSignalData(const QString& treeViewKey) const;

    /**
        * @brief 获取所有TreeView相关的数据更新
        * @param outTreeViewDataMap 输出的TreeView数据映射
        * @param outUpdatedKeys 输出的更新key集合
        * @param outDataChanged 输出的数据变化标志
        */
    void getTreeViewUpdates(QMap<QString, QVector<QString>>& outTreeViewDataMap,
                            QSet<QString>& outUpdatedKeys,
                            bool& outDataChanged);

    // 临时写在这里，后期有空优化
    // 方案名 -> (可变主题唯一值 soureId_destId_interfaceId_topicValue)
    QMap<QString, QSet<QString>> schemeToTopicVar;

private:
    // 私有构造函数，防止外部创建实例
    ResponseDataStore();
    ~ResponseDataStore();

    // 禁止拷贝和赋值
    ResponseDataStore(const ResponseDataStore&) = delete;
    ResponseDataStore& operator=(const ResponseDataStore&) = delete;

    // 存储数据的映射：信号全名 -> 信号数据
    QMap<QString, QVector<QString>> m_responseDataMap;
    QSet<QString> updatedSignals;
    bool dataChanged = false;
    bool m_cmdReuslt = false; // tcp发送报文返回的结果

    // 互斥锁，用于线程安全访问
    mutable QMutex m_mutex;
};

#endif // RESPONSE_DATA_STORE_H 
