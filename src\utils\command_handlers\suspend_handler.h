#ifndef SUSPEND_HANDLER_H
#define SUSPEND_HANDLER_H

#include "command_handler.h"

/**
 * @brief 暂停命令处理器
 */
class SuspendCommandHandler : public CommandHandler
{
    Q_OBJECT

public:
    explicit SuspendCommandHandler(QObject *parent = nullptr);
    ~SuspendCommandHandler() override;
    CommandHandler::CommandResult processCommand(const QByteArray &data) override;

private:
    // 可以添加特定于暂停命令的私有成员和方法
};

#endif // SUSPEND_HANDLER_H 
