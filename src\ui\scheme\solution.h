﻿#ifndef SOLUTION_H
#define SOLUTION_H

#include <QString>
#include <QMap>
#include <QVector>

struct TopicStruct
{
    QString m_topicName;
    QString m_topicShortName;
    // 新增：主题值
    QString m_topicValue;
    //信号Id:信号名称
    QMap<QString,QString> m_signals;
};

struct MsgStruct
{
    QString m_msgName;     //功能单元消息名称
    QString m_srcUnitId;     //源服务 服务调用者
    QString m_subPubTopicId;  //接口缩略名
    QVector<TopicStruct> m_topics;
};


struct parseFromLib
{
    QString msgName;
    QString srcUnitId;
    QString subPubTopId;
    QString shortName;
    QString name;
};

class solution
{
public:
    solution();
    void insertMsg(MsgStruct);
    void deleteMsg(MsgStruct);
    void deleteMsg(QString);
    void clearMsg();
    QString m_name;
    QString m_IcdVersion;
    //key为 服务调用者-服务提供者-接口名 组成的字符串
    QMap<QString,MsgStruct> m_msgs;
};

#endif // SOLUTION_H
