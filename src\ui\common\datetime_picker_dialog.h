#ifndef DATETIME_PICKER_DIALOG_H
#define DATETIME_PICKER_DIALOG_H

#include <QDialog>
#include <QDateTime>

namespace Ui {
class DateTimePickerDialog;
}

class DateTimePickerDialog : public QDialog
{
    Q_OBJECT

public:
    explicit DateTimePickerDialog(QWidget *parent = nullptr);
    ~DateTimePickerDialog();

    // 获取选择的开始时间和结束时间
    QDateTime getStartDateTime() const;
    QDateTime getEndDateTime() const;

    // 设置开始时间和结束时间
    void setStartDateTime(const QDateTime &dateTime);
    void setEndDateTime(const QDateTime &dateTime);

    // 设置日期时间范围
    void setDateTimeRange(const QDateTime &minDateTime, const QDateTime &maxDateTime);

private slots:
    // 处理开始时间改变
    void onStartDateTimeChanged(const QDateTime &dateTime);
    
    // 处理结束时间改变
    void onEndDateTimeChanged(const QDateTime &dateTime);
    
    // 处理确定按钮点击
    void onAcceptButtonClicked();
    
    // 处理取消按钮点击
    void onRejectButtonClicked();

private:
    Ui::DateTimePickerDialog *ui;
    
    QDateTime startDateTime;
    QDateTime endDateTime;
    
    // 时间选择对话框
    QDialog *startTimeDialog;
    QDialog *endTimeDialog;
};

#endif // DATETIME_PICKER_DIALOG_H 