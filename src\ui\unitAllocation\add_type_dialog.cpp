#include "add_type_dialog.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QMessageBox>

AddTypeDialog::AddTypeDialog(QWidget *parent)
    : QDialog(parent)
{
    setWindowTitle("新增");
    setupUI();
    setupConnections();
}

void AddTypeDialog::setupUI()
{
    // 设置窗口大小和样式
    setFixedSize(300, 280);  // 减小窗口高度，因为现在布局更紧凑
    setStyleSheet(
        "QDialog { background-color: white; }"
        "QLabel { min-width: 40px; }"  // 设置标签最小宽度
        "QLineEdit { padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; }"
        "QLineEdit:focus { border-color: #1890ff; }"
        "QPushButton { padding: 8px 16px; border-radius: 4px; }"
        "QPushButton#confirmButton { background-color: #1890ff; color: white; border: none; }"
        "QPushButton#confirmButton:hover { background-color: #40a9ff; }"
        "QPushButton#confirmButton:pressed { background-color: #096dd9; }"
        "QPushButton#cancelButton { background-color: white; border: 1px solid #d9d9d9; }"
        "QPushButton#cancelButton:hover { border-color: #40a9ff; color: #40a9ff; }"
    );

    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(20);
    mainLayout->setContentsMargins(20, 20, 20, 20);

    // 类型输入行
    QHBoxLayout *typeLayout = new QHBoxLayout();
    QLabel *typeLabel = new QLabel("类型", this);
    typeEdit = new QLineEdit(this);
    typeEdit->setPlaceholderText("请输入类型");
    typeLayout->addWidget(typeLabel);
    typeLayout->addWidget(typeEdit);
    mainLayout->addLayout(typeLayout);

    // CODE输入行
    QHBoxLayout *codeLayout = new QHBoxLayout();
    QLabel *codeLabel = new QLabel("CODE", this);
    codeEdit = new QLineEdit(this);
    codeEdit->setPlaceholderText("请输入CODE");
    codeLayout->addWidget(codeLabel);
    codeLayout->addWidget(codeEdit);
    mainLayout->addLayout(codeLayout);

    // 名称输入行
    QHBoxLayout *nameLayout = new QHBoxLayout();
    QLabel *nameLabel = new QLabel("名称", this);
    nameEdit = new QLineEdit(this);
    nameEdit->setPlaceholderText("请输入名称");
    nameLayout->addWidget(nameLabel);
    nameLayout->addWidget(nameEdit);
    mainLayout->addLayout(nameLayout);

    // 系数输入行
    QHBoxLayout *coefficientLayout = new QHBoxLayout();
    QLabel *coefficientLabel = new QLabel("系数", this);
    coefficientEdit = new QLineEdit(this);
    coefficientEdit->setPlaceholderText("请输入系数");
    coefficientLayout->addWidget(coefficientLabel);
    coefficientLayout->addWidget(coefficientEdit);
    mainLayout->addLayout(coefficientLayout);

    // 按钮布局
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->setSpacing(10);

    cancelButton = new QPushButton("取消", this);
    cancelButton->setObjectName("cancelButton");
    confirmButton = new QPushButton("确认", this);
    confirmButton->setObjectName("confirmButton");

    buttonLayout->addWidget(cancelButton);
    buttonLayout->addWidget(confirmButton);

    mainLayout->addStretch();
    mainLayout->addLayout(buttonLayout);
}

void AddTypeDialog::setupConnections()
{
    connect(cancelButton, &QPushButton::clicked, this, &QDialog::reject);
    connect(confirmButton, &QPushButton::clicked, [this]() {
        // 验证输入
        if (typeEdit->text().isEmpty() || 
            codeEdit->text().isEmpty() || 
            nameEdit->text().isEmpty() || 
            coefficientEdit->text().isEmpty()) {
            QMessageBox msgBox(QMessageBox::Warning,
                               "错误提示       ",  // 添加空格来增加宽度
                               "请填写所有必填项",  // 添加空格来增加宽度
                               QMessageBox::Ok,
                               this);
            msgBox.setStyleSheet("QLabel{min-width: 240px; min-height: 40px; color: red; font-size: 20px;} "
                                 "QPushButton{min-width: 60px;}");
            msgBox.button(QMessageBox::Ok)->setText("确定");
            msgBox.exec();
            return;
        }
        accept();
    });
}

QString AddTypeDialog::getType() const
{
    return typeEdit->text();
}

QString AddTypeDialog::getCode() const
{
    return codeEdit->text();
}

QString AddTypeDialog::getName() const
{
    return nameEdit->text();
}

QString AddTypeDialog::getCoefficient() const
{
    return coefficientEdit->text();
} 