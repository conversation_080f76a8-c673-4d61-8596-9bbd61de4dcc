#include "command_handler.h"
#include <QDebug>

#include "third/tcpFrameStruct.h"
#include "utils/response_data_store.h"

/**
 * @brief 构造函数
 */
CommandHandler::CommandHandler(QObject *parent) : QObject(parent)
{

}

/**
 * @brief 析构函数
 */
CommandHandler::~CommandHandler()
{

}

CommandHandler::CommandResult CommandHandler::processCommand(const QByteArray &data)
{
    CommandResult result;
    return result;
}

/**
 * @brief 处理通用命令逻辑并返回详细结果
 * 
 * 这个方法是handleCommonCommand的增强版本，它返回一个包含详细信息的CommandResult对象，
 * 而不是通过信号发送结果。
 */
CommandHandler::CommandResult CommandHandler::processCommonCommand(const QByteArray &data, SUBSCRIBE_COMMAND expectedCmdType, const QString &cmdTypeStr)
{
    CommandResult result;
    
    if (data.isEmpty()) {
        result.success = false;
        result.errorMessage = cmdTypeStr + "命令数据为空";
        qWarning() << result.errorMessage;
        return result;
    }

    // 处理 普通返回
    SUBSCRIBE_PACKAGE_RESPONSE response;
    //response.code = (SUBSCRIBE_RET_CODE)(SUBSCRIBE_RET_CODE::SUBSCRIBE_ERR_REPLY_CODE_ERROR + 1);
    int ret = deserialize(data.constData(), data.size(), &response);
    qDebug() << "response.code = " << response.code << "response.msg = " << QString::fromStdString(response.msg);
    if (response.code == SUBSCRIBE_SUC) {
        ResponseDataStore::getInstance().setCmdReuslt(true);
        result.success = true;
        result.errorMessage = "";
    }
    else {
        ResponseDataStore::getInstance().setCmdReuslt(false);
        // 生成结果消息
        QPair<bool, QString> commonResult = generateResultMessage(response.code, cmdTypeStr);
        result.success = commonResult.first;
        if (result.success) {
            result.message = commonResult.second;
        } else {
            result.errorMessage = commonResult.second;
        }
    }

    return result;
}

/**
 * @brief 从数据中提取错误消息
 */
QString CommandHandler::extractErrorMessage(const char* dataPtr, size_t dataSize, size_t headerSize, size_t codeSize)
{
    // 错误消息位于返回码之后
    const char* msgPtr = dataPtr + headerSize + codeSize;
    
    // 计算剩余数据长度
    size_t remainingLength = dataSize - (headerSize + codeSize);
    
    // 安全地获取字符串长度，限制在剩余数据长度内
    size_t strLength = 0;
    bool foundEnd = false;
    
    // 查找字符串结束标记
    for (size_t i = 0; i < remainingLength && i < 1024; i++) { // 限制最大长度为1024字节
        if (msgPtr[i] == '\0') {
            strLength = i;
            foundEnd = true;
            break;
        }
    }
    
    // 如果找到有效字符串
    if (foundEnd && strLength > 0) {
        return QString::fromUtf8(msgPtr, strLength);
    } else if (remainingLength > 0) {
        // 如果没有找到结束标记但有数据，使用剩余全部数据
        // 但限制最大长度，防止解析过长字符串
        size_t maxLen = qMin(remainingLength, static_cast<size_t>(1024));
        return QString::fromUtf8(msgPtr, maxLen);
    } else {
        return "未提供错误详情";
    }
}

/**
 * @brief 生成结果消息
 */
QPair<bool, QString> CommandHandler::generateResultMessage(SUBSCRIBE_RET_CODE code, const QString &cmdTypeStr)
{
    bool success = (code == SUBSCRIBE_SUC);
    QString resultMessage;

    switch (code) {
        case SUBSCRIBE_SUC:
            resultMessage = cmdTypeStr + "命令处理成功";
            break;
        case SUBSCRIBE_ERR_SUB_RULE_EMPTY:
            resultMessage = cmdTypeStr + "失败: " + cmdTypeStr + "条件为空";
            break;
        case SUBSCRIBE_ERR_REQ_LEN_ERROR:
            resultMessage = cmdTypeStr + "失败: 请求长度错误";
            break;
        case SUBSCRIBE_ERR_UNKNOWN_REQ_TYPE:
            resultMessage = cmdTypeStr + "失败: 未知的请求类型";
            break;
        case SUBSCRIBE_ERR_UNKNOWN_IP_OR_PORT:
            resultMessage = cmdTypeStr + "失败: 未知的IP或端口";
            break;
        case SUBSCRIBE_SEND_SUB_ERROR:
            resultMessage = cmdTypeStr + "失败: 发送" + cmdTypeStr + "请求错误";
            break;
        case SUBSCRIBE_ERR_NOT_REPLY:
            resultMessage = cmdTypeStr + "失败: 无响应";
            break;
        case SUBSCRIBE_ERR_NOT_REPLY_MAIN:
            resultMessage = cmdTypeStr + "失败: 主服务器无响应";
            break;
        case SUBSCRIBE_ERR_NOT_REPLY_BAK:
            resultMessage = cmdTypeStr + "失败: 备用服务器无响应";
            break;
        case SUBSCRIBE_ERR_REPLY_DATA_ERROR:
            resultMessage = cmdTypeStr + "失败: 响应数据错误";
            break;
        case SUBSCRIBE_ERR_UNKNOWN_REPLY_IP:
            resultMessage = cmdTypeStr + "失败: 未知的响应IP";
            break;
        case SUBSCRIBE_ERR_REPLY_CODE_ERROR:
            resultMessage = cmdTypeStr + "失败: 响应代码错误";
            break;
        default:
            resultMessage = QString("%1失败: 未知错误码 %2").arg(cmdTypeStr).arg(code);
            break;
    }


    return qMakePair(success, resultMessage);
} 
