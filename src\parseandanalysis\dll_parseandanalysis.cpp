#include "dll_parseandanalysis.h"
#include <QByteArray>
#include <QFile>

// 单例模式获取实例
DllParseandanalysis& DllParseandanalysis::getInstance()
{
    static DllParseandanalysis instance;
    return instance;
}

DllParseandanalysis::DllParseandanalysis() : m_hModule(nullptr),
    m_AddFunc(nullptr),
    m_ParseIcdAndGenerateDataFileFunc(nullptr),
    m_LoadIcdJsonFunc(nullptr),
    m_InitDecoderFunc(nullptr), 
    m_DestroyDecoderFunc(nullptr), 
    m_GetCurrentIcdVersionFunc(nullptr),
    m_ParseFixedLengthDataFunc(nullptr),
    m_UnpackMuseDataFunc(nullptr),
    m_MsgpackPackVectorFunc(nullptr),
    m_GetFuncUnitListFunc(nullptr),
    m_GetInterfacesByServiceNameFunc(nullptr),
    m_GetInterfacesByServiceShortNameFunc(nullptr),
    m_GetTopicInfoByShortNameFunc(nullptr),
    m_SearchInterfacesByNameFuzzyMatchFunc(nullptr)
{
}

DllParseandanalysis::~DllParseandanalysis()
{
    release();
}

// 打印DLL导出函数列表
void DllParseandanalysis::printDllExports(HMODULE hModule)
{
    if (!hModule) return;
    
    try {
        // 获取DOS头
        PIMAGE_DOS_HEADER pDosHeader = (PIMAGE_DOS_HEADER)hModule;
        if (pDosHeader->e_magic != IMAGE_DOS_SIGNATURE) {
            qWarning() << "Invalid DOS signature";
            return;
        }
        
        // 获取NT头
        PIMAGE_NT_HEADERS pNtHeaders = (PIMAGE_NT_HEADERS)((BYTE*)hModule + pDosHeader->e_lfanew);
        if (pNtHeaders->Signature != IMAGE_NT_SIGNATURE) {
            qWarning() << "Invalid NT signature";
            return;
        }
        
        // 检查是否有导出目录
        DWORD exportDirRVA = pNtHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
        if (exportDirRVA == 0) {
            qWarning() << "No export directory found in the DLL";
            return;
        }
        
        // 获取导出目录
        PIMAGE_EXPORT_DIRECTORY pExportDir = (PIMAGE_EXPORT_DIRECTORY)((BYTE*)hModule + exportDirRVA);
        
        // 获取函数名称数组
        PDWORD pFunctionNameRVAs = (PDWORD)((BYTE*)hModule + pExportDir->AddressOfNames);
        
        // 输出导出函数列表
        qDebug() << "DLL exports" << pExportDir->NumberOfNames << "functions:";
        for (DWORD i = 0; i < pExportDir->NumberOfNames; i++) {
            PCHAR pFunctionName = (PCHAR)((BYTE*)hModule + pFunctionNameRVAs[i]);
            qDebug() << "  -" << pFunctionName;
        }
    } catch (...) {
        qWarning() << "Exception occurred while analyzing DLL exports";
    }
}

// 检查DLL是否为MinGW编译 - 通过读取PE头和检查特定特征
bool DllParseandanalysis::isMinGWDll(const QString& dllPath)
{
    QFile file(dllPath);
    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }
    
    // 读取文件头部，足够包含PE头信息
    QByteArray data = file.read(2048);
    file.close();
    
    // 检查是否包含"mingw"或"gcc"字符串
    return data.contains("mingw") || data.contains("gcc");
}

// 检查DLL架构（32位或64位）
bool DllParseandanalysis::is64BitDll(HMODULE hModule)
{
    if (!hModule) return false;
    
    try {
        // 获取DOS头
        PIMAGE_DOS_HEADER pDosHeader = (PIMAGE_DOS_HEADER)hModule;
        if (pDosHeader->e_magic != IMAGE_DOS_SIGNATURE) {
            return false;
        }
        
        // 获取NT头
        PIMAGE_NT_HEADERS pNtHeaders = (PIMAGE_NT_HEADERS)((BYTE*)hModule + pDosHeader->e_lfanew);
        if (pNtHeaders->Signature != IMAGE_NT_SIGNATURE) {
            return false;
        }
        
        // 检查Magic值，区分32位和64位
        WORD magic = pNtHeaders->OptionalHeader.Magic;
        return magic == IMAGE_NT_OPTIONAL_HDR64_MAGIC;
    } catch (...) {
        return false;
    }
}

// 检查dll函数是否有C++名称修饰
bool DllParseandanalysis::hasCppNameMangling(HMODULE hModule)
{
    if (!hModule) return false;
    
    try {
        // 获取DOS头
        PIMAGE_DOS_HEADER pDosHeader = (PIMAGE_DOS_HEADER)hModule;
        if (pDosHeader->e_magic != IMAGE_DOS_SIGNATURE) {
            return false;
        }
        
        // 获取NT头
        PIMAGE_NT_HEADERS pNtHeaders = (PIMAGE_NT_HEADERS)((BYTE*)hModule + pDosHeader->e_lfanew);
        if (pNtHeaders->Signature != IMAGE_NT_SIGNATURE) {
            return false;
        }
        
        // 检查是否有导出目录
        DWORD exportDirRVA = pNtHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
        if (exportDirRVA == 0) {
            return false;
        }
        
        // 获取导出目录
        PIMAGE_EXPORT_DIRECTORY pExportDir = (PIMAGE_EXPORT_DIRECTORY)((BYTE*)hModule + exportDirRVA);
        
        // 获取函数名称数组
        PDWORD pFunctionNameRVAs = (PDWORD)((BYTE*)hModule + pExportDir->AddressOfNames);
        
        // 检查是否有C++名称修饰（包含?或@字符）
        for (DWORD i = 0; i < pExportDir->NumberOfNames; i++) {
            PCHAR pFunctionName = (PCHAR)((BYTE*)hModule + pFunctionNameRVAs[i]);
            QString name = QString::fromLatin1(pFunctionName);
            
            // C++名称修饰通常包含?字符
            if (name.contains('?') || name.contains("@")) {
                return true;
            }
        }
        
        return false;
    } catch (...) {
        return false;
    }
}

bool DllParseandanalysis::init(const QString& dllPath)
{
    // 如果之前已加载，先释放
    if (m_hModule) {
        release();
    }

    qDebug() << "Attempting to load DLL from path:" << dllPath;
    
    // 检查DLL编译环境
    // bool isMinGW = isMinGWDll(dllPath);
    // if (isMinGW) {
    //     qDebug() << "DLL appears to be compiled with MinGW/GCC";
    // }

    // 加载DLL
    m_hModule = LoadLibraryW((LPCWSTR)dllPath.utf16());
    if (m_hModule == nullptr) {
        DWORD errorCode = GetLastError();
        qCritical() << "Failed to load DLL:" << dllPath << ", error code:" << errorCode;
        
        // 打印常见的错误码含义
        if (errorCode == ERROR_MOD_NOT_FOUND) {
            qCritical() << "Error details: The specified module could not be found.";
        } else if (errorCode == ERROR_BAD_EXE_FORMAT) {
            qCritical() << "Error details: The DLL is not a valid Windows DLL.";
        } else if (errorCode == ERROR_SXS_CANT_GEN_ACTCTX) {
            qCritical() << "Error details: The DLL or a dependent DLL could not be found.";
        }
        
        return false;
    }

    qDebug() << "DLL loaded successfully, analyzing...";
    
    // 检查DLL架构
    // bool is64Bit = is64BitDll(m_hModule);
    // qDebug() << "DLL architecture:" << (is64Bit ? "64-bit" : "32-bit");
    
    // 检查C++名称修饰
    // bool hasCppMangling = hasCppNameMangling(m_hModule);
    // qDebug() << "DLL has C++ name mangling:" << (hasCppMangling ? "Yes" : "No");
    
    // 打印DLL导出函数列表，帮助诊断
    printDllExports(m_hModule);

    // 构建可能的函数名列表（处理名称修饰、下划线前缀等）
    // QList<QPair<QString, QString>> functionAliases;
    // functionAliases << qMakePair(QString("add"), QString("add"))
    //                 << qMakePair(QString("add"), QString("_add"))
    //                 << qMakePair(QString("add"), QString("_add@8"))
    //                 << qMakePair(QString("parseIcdAndGenerateDataFile"), QString("parseIcdAndGenerateDataFile"))
    //                 << qMakePair(QString("loadIcdJson"), QString("loadIcdJson"))
    //                 << qMakePair(QString("initDecoder"), QString("initDecoder"))
    //                 << qMakePair(QString("destroyDecoder"), QString("destroyDecoder"))
    //                 << qMakePair(QString("getCurrentIcdVersion"), QString("getCurrentIcdVersion"))
    //                 << qMakePair(QString("parseFixedLengthData"), QString("parseFixedLengthData"))
    //                 << qMakePair(QString("unpackMuseData"), QString("unpackMuseData"))
    //                 << qMakePair(QString("msgpackPackVector"), QString("msgpackPackVector"))
    //                 << qMakePair(QString("getFuncUnitList"), QString("getFuncUnitList"))
    //                 << qMakePair(QString("getInterfacesByServiceName"), QString("getInterfacesByServiceName"))
    //                 << qMakePair(QString("getInterfacesByServiceShortName"), QString("getInterfacesByServiceShortName"))
    //                 << qMakePair(QString("getTopicInfoByShortName"), QString("getTopicInfoByShortName"))
    //                 << qMakePair(QString("searchInterfacesByNameFuzzyMatch"), QString("searchInterfacesByNameFuzzyMatch"));

    // 尝试加载add函数（多种可能的名称）
    // bool foundAdd = false;
    // for (const auto& alias : functionAliases) {
    //     if (alias.first == "add") {
    //         QByteArray funcName = alias.second.toLatin1();
    //         m_AddFunc = reinterpret_cast<AddFunc>(GetProcAddress(m_hModule, funcName.constData()));
    //         if (m_AddFunc) {
    //             qDebug() << "Successfully loaded 'add' function as:" << alias.second;
    //             foundAdd = true;
    //             break;
    //         }
    //     }
    // }
    
    // 如果没有找到add函数，尝试扫描所有导出函数
    // if (!foundAdd) {
    //     qWarning() << "Could not find 'add' function with expected names, attempting to scan all exports...";
        
    //     // 尝试枚举导出函数并测试
    //     try {
    //         PIMAGE_DOS_HEADER pDosHeader = (PIMAGE_DOS_HEADER)m_hModule;
    //         PIMAGE_NT_HEADERS pNtHeaders = (PIMAGE_NT_HEADERS)((BYTE*)m_hModule + pDosHeader->e_lfanew);
    //         DWORD exportDirRVA = pNtHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
            
    //         if (exportDirRVA != 0) {
    //             PIMAGE_EXPORT_DIRECTORY pExportDir = (PIMAGE_EXPORT_DIRECTORY)((BYTE*)m_hModule + exportDirRVA);
    //             PDWORD pFunctionNameRVAs = (PDWORD)((BYTE*)m_hModule + pExportDir->AddressOfNames);
    //             PWORD pOrdinals = (PWORD)((BYTE*)m_hModule + pExportDir->AddressOfNameOrdinals);
    //             PDWORD pFunctions = (PDWORD)((BYTE*)m_hModule + pExportDir->AddressOfFunctions);
                
    //             // 对每个导出函数尝试调用add(3,5)看是否返回8
    //             for (DWORD i = 0; i < pExportDir->NumberOfNames; i++) {
    //                 PCHAR pFunctionName = (PCHAR)((BYTE*)m_hModule + pFunctionNameRVAs[i]);
    //                 DWORD funcRVA = pFunctions[pOrdinals[i]];
    //                 void* funcAddr = (void*)((BYTE*)m_hModule + funcRVA);
                    
    //                 // 仅尝试名称短的函数，可能是简单函数如add
    //                 if (strlen(pFunctionName) < 10) {
    //                     AddFunc testFunc = reinterpret_cast<AddFunc>(funcAddr);
                        
    //                     try {
    //                         int result = testFunc(3, 5);
    //                         if (result == 8) {
    //                             qDebug() << "Found potential 'add' function with name:" << pFunctionName;
    //                             m_AddFunc = testFunc;
    //                             foundAdd = true;
    //                             break;
    //                         }
    //                     } catch (...) {
    //                         // 忽略可能的崩溃
    //                     }
    //                 }
    //             }
    //         }
    //     } catch (...) {
    //         qWarning() << "Exception while scanning exports for add function";
    //     }
    // }
    
    // 尝试加载其他函数
    tryLoadFunction(m_AddFunc, "add");
    tryLoadFunction(m_InitDecoderFunc, "initDecoder");
    tryLoadFunction(m_DestroyDecoderFunc, "destroyDecoder");
    tryLoadFunction(m_GetCurrentIcdVersionFunc, "getCurrentIcdVersion");
    tryLoadFunction(m_GetFuncUnitListFunc, "getFuncUnitList");
    tryLoadFunction(m_SearchInterfacesByNameFuzzyMatchFunc, "searchInterfacesByNameFuzzyMatch");
    
    // 尝试加载其余函数
    tryLoadFunction(m_ParseIcdAndGenerateDataFileFunc, "parseIcdAndGenerateDataFile");
    tryLoadFunction(m_LoadIcdJsonFunc, "loadIcdJson");
    tryLoadFunction(m_ParseFixedLengthDataFunc, "parseFixedLengthData");
    tryLoadFunction(m_UnpackMuseDataFunc, "unpackMuseData");
    tryLoadFunction(m_MsgpackPackVectorFunc, "msgpackPackVector");
    tryLoadFunction(m_GetInterfacesByServiceNameFunc, "getInterfacesByServiceName");
    tryLoadFunction(m_GetInterfacesByServiceShortNameFunc, "getInterfacesByServiceShortName");
    tryLoadFunction(m_GetTopicInfoByShortNameFunc, "getTopicInfoByShortName");
    
    // 检查是否成功加载至少一些必要的函数
    bool anyFunctionLoaded = m_AddFunc || m_InitDecoderFunc || m_DestroyDecoderFunc || m_GetCurrentIcdVersionFunc || m_GetFuncUnitListFunc;
    
    // qDebug() << "DLL loading summary:";
    // qDebug() << "  - add function:" << (m_AddFunc ? "Available" : "Not available");
    // qDebug() << "  - initDecoder function:" << (m_InitDecoderFunc ? "Available" : "Not available");
    // qDebug() << "  - destroyDecoder function:" << (m_DestroyDecoderFunc ? "Available" : "Not available");
    // qDebug() << "  - getCurrentIcdVersion function:" << (m_GetCurrentIcdVersionFunc ? "Available" : "Not available");
    // qDebug() << "  - getFuncUnitList function:" << (m_GetFuncUnitListFunc ? "Available" : "Not available");
    
    // 只要任何一个函数加载成功，我们就认为DLL部分加载成功
    return anyFunctionLoaded;
}

void DllParseandanalysis::release()
{
    if (m_hModule) {
        FreeLibrary(m_hModule);
        m_hModule = nullptr;
        qDebug() << "DLL released.";
    }

    // 清空所有函数指针
    m_AddFunc = nullptr;
    m_ParseIcdAndGenerateDataFileFunc = nullptr;
    m_LoadIcdJsonFunc = nullptr;
    m_InitDecoderFunc = nullptr;
    m_DestroyDecoderFunc = nullptr;
    m_GetCurrentIcdVersionFunc = nullptr;
    m_ParseFixedLengthDataFunc = nullptr;
    m_UnpackMuseDataFunc = nullptr;
    m_MsgpackPackVectorFunc = nullptr;
    m_GetFuncUnitListFunc = nullptr;
    m_GetInterfacesByServiceNameFunc = nullptr;
    m_GetInterfacesByServiceShortNameFunc = nullptr;
    m_GetTopicInfoByShortNameFunc = nullptr;
    m_SearchInterfacesByNameFuzzyMatchFunc = nullptr;
}

// 尝试加载函数，但如果失败不会终止加载过程
template<typename T>
bool DllParseandanalysis::tryLoadFunction(T& funcPtr, const char* funcName)
{
    funcPtr = reinterpret_cast<T>(GetProcAddress(m_hModule, funcName));
    if (funcPtr == nullptr) {
        DWORD error = GetLastError();
        qWarning() << "Failed to load function:" << funcName << ", error:" << error;
        
        // 尝试带下划线前缀的版本（MinGW常见）
        QString underscoredName = QString("_") + QString(funcName);
        funcPtr = reinterpret_cast<T>(GetProcAddress(m_hModule, underscoredName.toLatin1().constData()));
        if (funcPtr) {
            qDebug() << "Successfully loaded function" << funcName << "as" << underscoredName;
            return true;
        }
        
        return false;
    }
    qDebug() << "Successfully loaded function:" << funcName;
    return true;
} 
