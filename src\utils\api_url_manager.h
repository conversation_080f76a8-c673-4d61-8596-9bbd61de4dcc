#ifndef API_URL_MANAGER_H
#define API_URL_MANAGER_H

#include <QString>
#include <QUrl>
#include <QMap>

/**
 * @brief API URL管理器单例类
 *
 * 这个类用于集中管理应用程序中使用的所有API URL，
 * 使用单例模式设计，确保系统中只有一个URL管理实例。
 */
class ApiUrlManager {
public:
    /**
     * API端点类型枚举，用于标识不同的API端点
     */
    enum ApiEndpoint {
        USER_LOGIN,           // 用户登录
        USER_LOGOUT,          // 用户登出
        USER_ADD,             // 添加用户
        USER_ALL,             // 全部用户
        USER_DELETE,          // 删除用户
        RESET_USER,           // 用户重置
        FILE_LIST,            // 文件列表
        FILE_VIEW,            // 文件查看
        FILE_UPLOAD,          // 文件上传
        ICD_LIST,             // ICD列表
        SCHEME_XML,            // 下载数值监控方案XML文件
        SCHEME_UPDATE,          //更新数值监控方案XML文件
        SCHEME_SHARE,       //数值监控方案分享
        SCHEME_UNSHARE,     //数值监控取消方案分享
        GET_TEMPLATE_LIST,   //数值监控获取模板方案列表
        GRAPH_SCHEME_UPDATE,   //图形监控方案上传更新
        GRAPH_SHCEME_DOWNLOAD,  //图形监控方案下载
        GRAPH_TEMPLATE_DEL,     //图形监控模板删除/取消分享
        GRAPH_TEMPLATE_GET,     //图形监控模板方案列表查询
        GRAPH_TEMPLATE_ADD   //图形监控方案分享

    };

    /**
     * @brief 获取单例实例
     * @return ApiUrlManager的单例对象引用
     */
    static ApiUrlManager& getInstance();

    // 禁止拷贝和移动
    ApiUrlManager(const ApiUrlManager&) = delete;
    ApiUrlManager& operator=(const ApiUrlManager&) = delete;
    ApiUrlManager(ApiUrlManager&&) = delete;
    ApiUrlManager& operator=(ApiUrlManager&&) = delete;

    /**
     * @brief 设置API服务器基础URL
     * @param baseUrl 服务器基础URL（例如：http://172.16.10.240:9001）
     */
    void setBaseUrl(const QString& baseUrl);

    /**
     * @brief 获取API服务器基础URL
     * @return 服务器基础URL
     */
    QString getBaseUrl() const;

    /**
     * @brief 从配置文件重新加载服务器地址
     * 当登录界面更新服务器地址后，调用此方法同步更新ApiUrlManager中的地址
     */
    void reloadFromConfig();

    /**
     * @brief 构建完整的API URL
     * @param path API路径（例如：/user/login）
     * @return 完整的QUrl对象
     */
    QUrl buildUrl(const QString& path) const;

    /**
     * @brief 构建完整的API URL（基于枚举类型）
     * @param endpoint API端点枚举值
     * @return 完整的QUrl对象
     */
    QUrl getUrl(ApiEndpoint endpoint) const;

    /**
     * @brief 构建带参数的API URL
     * @param endpoint API端点枚举值
     * @param params 查询参数映射
     * @return 完整的带参数的QUrl对象
     */
    QUrl getUrlWithParams(ApiEndpoint endpoint, const QMap<QString, QString>& params) const;

    // 用户相关API
    QUrl getUserLoginUrl() const;
    QUrl getUserLogoutUrl() const;
    QUrl getUserAddUrl() const;
    QUrl getAllUserUrl() const;
    QUrl getUserListUrl(const QString &userId) const;

    // 文件相关API
    QUrl getFileListUrl() const;
    QUrl getFileViewUrl(int id, int paragraph, const QString& type) const;
    QUrl getFileUploadUrl() const;

    // ICD相关API
    QUrl getIcdListUrl() const;

    // 方案相关API
    /**
     * @brief 获取方案XML文件的URL
     * @param fileName 方案文件名
     * @return 完整的URL
     */
    QUrl getSchemeXmlUrl(const QString& userID) const;

    //获取图形监控方案xml的url
    QUrl getGraphSchemeXmlUrl(const QString &userID)const;

    /**
     * @brief 更新方案XML文件的URL
     * @return 完整的URL
     */
    QUrl getUpdateSchemeXmlUrl() const;

private:
    /**
     * @brief 私有构造函数，防止外部创建实例
     */
    ApiUrlManager();

    /**
     * @brief 初始化API路径映射表
     */
    void initApiPathMap();

    QString m_baseUrl;                      // API服务器基础URL
    QMap<ApiEndpoint, QString> m_apiPaths;  // API路径映射表
};

#endif // API_URL_MANAGER_H
