﻿#include "scheme_edit_dialog.h"
#include "ui_scheme_edit_dialog.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QHeaderView>
#include <QFile>
#include <QDomDocument>
#include <QDebug>
#include <QCoreApplication>
#include <QMenu>
#include <QJsonObject>
#include <QJsonDocument>

#include "utils/user_session.h"
#include "utils/api_url_manager.h"
#include "third/parseAndAnalysis.h"
#include "utils/file_list_service.h"


SchemeEditDialog::SchemeEditDialog(const QString &schemeName, QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::SchemeEditDialog)
    , treeModel(nullptr)
    , currentSchemeName(schemeName)
{
    ui->setupUi(this);
    treeModel = new QStandardItemModel(this);
    // 设置全屏显示
    showMinimized();
    setupUI();
    this->hide();
    setupConnections();

    // 重置token避免重复初始化
    m_decoderToken = FileListService::getInstance().getParseandanalysisP();

    // 初始化网络管理器
    m_networkManager = new QNetworkAccessManager(this);
    connect(m_networkManager, &QNetworkAccessManager::finished, this, &SchemeEditDialog::onUploadFinished);
    loadSchemeData(schemeName);
}

SchemeEditDialog::~SchemeEditDialog()
{
    delete ui;
}

void SchemeEditDialog::setupUI()
{
    // 创建主分割器
    mainSplitter = new QSplitter(Qt::Horizontal, this);
    mainSplitter->setChildrenCollapsible(false);
    mainSplitter->setHandleWidth(2);
    ui->verticalLayout->addWidget(mainSplitter);

    // 左侧面板
    leftPanel = new QWidget();
    leftPanel->setObjectName("leftPanel");
    leftPanel->setMinimumWidth(300);  // 增加宽度以适应层级显示
    leftPanel->setMaximumWidth(400);
    QVBoxLayout *leftLayout = new QVBoxLayout(leftPanel);
    leftLayout->setContentsMargins(10, 10, 10, 10);
    leftLayout->setSpacing(10);

    schemeNameLabel = new QLabel(tr("方案名称"));

    // 创建搜索布局
    QHBoxLayout *searchLayout = new QHBoxLayout();
    searchLayout->setSpacing(5);

    searchEdit = new QLineEdit();
    searchEdit->setPlaceholderText(tr("查询单元"));

    // 创建上下搜索按钮
    searchUpButton = new QPushButton();
    searchDownButton = new QPushButton();

    // 设置按钮图标
    searchUpButton->setIcon(QIcon(":/images/arrow-up.png"));
    searchDownButton->setIcon(QIcon(":/images/arrow-down2.png"));

    // 设置按钮样式
    QString searchButtonStyle =
        "QPushButton {"
        "    border: 1px solid #d9d9d9;"
        "    border-radius: 4px;"
        "    background-color: white;"
        "    min-width: 28px;"
        "    max-width: 28px;"
        "    min-height: 28px;"
        "    max-height: 28px;"
        "    padding: 0px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #e6f7ff;"
        "    border-color: #40a9ff;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #bae7ff;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #f5f5f5;"
        "    border-color: #d9d9d9;"
        "}";

    searchUpButton->setStyleSheet(searchButtonStyle);
    searchDownButton->setStyleSheet(searchButtonStyle);

    // 初始时禁用导航按钮
    searchUpButton->setEnabled(false);
    searchDownButton->setEnabled(false);

    // 添加到搜索布局
    searchLayout->addWidget(searchEdit);
    searchLayout->addWidget(searchUpButton);
    searchLayout->addWidget(searchDownButton);

    // 创建树形控件
    unitTreeWidget = new QTreeWidget();
    unitTreeWidget->setHeaderLabels(QStringList() << tr("名称") << tr("值"));
    unitTreeWidget->setColumnCount(2);
    unitTreeWidget->setAlternatingRowColors(true);
    unitTreeWidget->setAnimated(true);
    unitTreeWidget->setExpandsOnDoubleClick(true);
    unitTreeWidget->setSelectionMode(QAbstractItemView::SingleSelection);

    // 设置树形控件的样式
//    unitTreeWidget->setStyleSheet(
//        "QTreeWidget {"
//        "   border: 1px solid #d9d9d9;"
//        "   border-radius: 4px;"
//        "   background-color: white;"
//        "}"
//        "QTreeWidget::item {"
//        "   height: 24px;"
//        "   padding: 2px;"
//        "}"
//        "QTreeWidget::item:hover {"
//        "   background-color: #e6f7ff;"
//        "}"
//        "QTreeWidget::item:selected {"
//        "   background-color: #bae7ff;"
//        "   color: black;"
//        "}"
//        "QTreeWidget::branch:has-children:!has-siblings:closed,"
//        "QTreeWidget::branch:closed:has-children:has-siblings {"
//        "   image: url(:/images/arrow-right.png);"
//        "}"
//        "QTreeWidget::branch:open:has-children:!has-siblings,"
//        "QTreeWidget::branch:open:has-children:has-siblings {"
//        "   image: url(:/images/arrow-down.png);"
//        "}"
//        );

    leftLayout->addWidget(schemeNameLabel);
    leftLayout->addLayout(searchLayout);
    leftLayout->addWidget(unitTreeWidget);

    // 中间面板
    middlePanel = new QWidget();
    middlePanel->setObjectName("middlePanel");
    middlePanel->setMinimumWidth(400);
    QVBoxLayout *middleLayout = new QVBoxLayout(middlePanel);
    middleLayout->setContentsMargins(10, 10, 10, 10);
    middleLayout->setSpacing(10);

    schemeTreeView = new QTreeView();
    schemeTreeView->setModel(treeModel);
    schemeTreeView->setHeaderHidden(false);
    schemeTreeView->setExpandsOnDoubleClick(true);
    schemeTreeView->setSelectionMode(QAbstractItemView::SingleSelection);
    schemeTreeView->setEditTriggers(QAbstractItemView::NoEditTriggers);

    // 创建按钮布局
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->setAlignment(Qt::AlignRight);  // 按钮靠右对齐

    // 创建取消全选按钮
    clearSelectionButton = new QPushButton(tr("取消全选"));
    clearSelectionButton->setStyleSheet(
        "QPushButton {"
        "   background-color: white;"
        "   color: #1890ff;"
        "   border: 1px solid #1890ff;"
        "   border-radius: 4px;"
        "   padding: 4px 12px;"
        "   min-width: 80px;"
        "   min-height: 28px;"
        "}"
        "QPushButton:hover {"
        "   background-color: #e6f7ff;"
        "}"
        "QPushButton:pressed {"
        "   background-color: #bae7ff;"
        "}"
        );

    // 创建添加到方案按钮
    addToSchemeButton = new QPushButton(tr("添加到方案"));
    addToSchemeButton->setStyleSheet(
        "QPushButton {"
        "   background-color: #1890ff;"
        "   color: white;"
        "   border: none;"
        "   border-radius: 4px;"
        "   padding: 4px 12px;"
        "   min-width: 80px;"
        "   min-height: 28px;"
        "}"
        "QPushButton:hover {"
        "   background-color: #40a9ff;"
        "}"
        "QPushButton:pressed {"
        "   background-color: #096dd9;"
        "}"
        );

    // 添加按钮到布局
    buttonLayout->addWidget(clearSelectionButton);
    buttonLayout->addWidget(addToSchemeButton);
    buttonLayout->setSpacing(8);  // 设置按钮之间的间距

    // 将树形视图和按钮布局添加到中间面板布局
    middleLayout->addWidget(schemeTreeView);
    middleLayout->addLayout(buttonLayout);

    // 右侧面板
    rightPanel = new QWidget();
    rightPanel->setObjectName("rightPanel");
    rightPanel->setMinimumWidth(250);
    rightPanel->setMaximumWidth(300);
    QVBoxLayout *rightLayout = new QVBoxLayout(rightPanel);
    rightLayout->setContentsMargins(10, 10, 10, 10);
    rightLayout->setSpacing(10);

    selectedLabel = new QLabel(tr("已选清单"));
    selectedListWidget = new QListWidget();
    schemeNameEdit = new QLineEdit();
    schemeNameEdit->setText(currentSchemeName);
    schemeNameEdit->setPlaceholderText(tr("请输入方案名称"));

    QHBoxLayout *rightButtonLayout = new QHBoxLayout();  // 改名为rightButtonLayout
    confirmButton = new QPushButton(tr("确认"));
    cancelButton = new QPushButton(tr("取消"));
    saveAsButton = new QPushButton(tr("另存为"));
    cancelButton->setObjectName("cancelButton");

    rightButtonLayout->addWidget(confirmButton);  // 使用新的变量名
    rightButtonLayout->addWidget(cancelButton);
    rightButtonLayout->addWidget(saveAsButton);

    rightLayout->addWidget(selectedLabel);
    rightLayout->addWidget(selectedListWidget);
    rightLayout->addWidget(schemeNameEdit);
    rightLayout->addLayout(rightButtonLayout);  // 使用新的变量名

    // 添加到分割器
    mainSplitter->addWidget(leftPanel);
    mainSplitter->addWidget(middlePanel);
    mainSplitter->addWidget(rightPanel);

    // 初始化数据
    initializeUnitTree();
    initializeTreeView();
    initializeSelectedList();
}

void SchemeEditDialog::setupConnections()
{
    connect(searchEdit, &QLineEdit::textChanged, this, &SchemeEditDialog::onSearchTextChanged);
    connect(searchUpButton, &QPushButton::clicked, this, &SchemeEditDialog::onSearchUpClicked);
    connect(searchDownButton, &QPushButton::clicked, this, &SchemeEditDialog::onSearchDownClicked);

    connect(unitTreeWidget, &QTreeWidget::itemClicked, this, &SchemeEditDialog::onUnitItemClicked);
    connect(schemeTreeView, &QTreeView::clicked, this, &SchemeEditDialog::onTreeItemClicked);
    connect(confirmButton, &QPushButton::clicked, this, &SchemeEditDialog::onConfirmClicked);
    connect(cancelButton, &QPushButton::clicked, this, &SchemeEditDialog::onCancelClicked);
    connect(saveAsButton, &QPushButton::clicked, this, &SchemeEditDialog::onSaveAsClicked);

    // 添加新按钮的连接
    connect(clearSelectionButton, &QPushButton::clicked, this, &SchemeEditDialog::onClearSelectionClicked);
    connect(addToSchemeButton, &QPushButton::clicked, this, &SchemeEditDialog::onAddToSchemeClicked);

    // 添加右键菜单
    selectedListWidget->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(selectedListWidget, &QListWidget::customContextMenuRequested,
            this, &SchemeEditDialog::showContextMenu);
}

void SchemeEditDialog::initializeUnitTree()
{
    // 清空现有树
    unitTreeWidget->clear();
    unitTreeWidget->setColumnCount(1);
    unitTreeWidget->setHeaderLabel("功能域");

    // 获取功能域列表
    std::vector<FuncDomainInfo> funcDomainVec;
    // 功能域-功能子域-功能单元-功能单元消息
    // 功能单元消息- 消息主题结构
    int result;


    try {
        m_decoderToken = FileListService::getInstance().getParseandanalysisP();
        result =  getFuncDomainList(m_decoderToken, funcDomainVec);
        // 打印获取的功能域信息
        qDebug() << "Found" << funcDomainVec.size() << "functional domains:";
        for (const auto& funcDomain : funcDomainVec) {
            qDebug() << "  - Name:" << QString::fromStdString(funcDomain.name)
            << ", Short Name:" << QString::fromStdString(funcDomain.shortName);
        }
    } catch(const std::exception& e) {
        qDebug()<<"getFuncDomainVec failed:"<<e.what();
    }

    for (const auto& funcDomain : funcDomainVec) {
        // 父节点显示 name，如果为空则用 shortName
        QString funcDomainText = QString::fromStdString(
            !funcDomain.name.empty() ? funcDomain.name : funcDomain.shortName
            );
        QTreeWidgetItem* funcDomainItem = new QTreeWidgetItem(QStringList(funcDomainText));

        // 添加 funcSubDomainVec 中的每一项作为子节点
        for (const auto& subDomain : funcDomain.funcSubDomainVec) {
            std::string name = subDomain.name;
            QString serviceText = QString::fromStdString(subDomain.name);
            QTreeWidgetItem* subDomainTreeItem = new QTreeWidgetItem(QStringList(serviceText));
            funcDomainItem->addChild(subDomainTreeItem);

            //功能单元
            std::vector<FuncUnitInfo> FuncUnitInfo = subDomain.funcUnitVec;
            // 遍历 FuncUnitInfo 并为每个接口添加子节点
            for (const auto& funcUint : FuncUnitInfo) {
                QString funcUnitText = QString::fromStdString(funcUint.name);
                //功能单元
                QTreeWidgetItem* funcUnitItem = new QTreeWidgetItem(QStringList(funcUnitText));

                for (const auto& funcUintInfo:funcUint.messageVec){
                    QString funcUnitInfoText = QString::fromStdString(funcUintInfo);
                    QTreeWidgetItem* funcUintInfoItem = new QTreeWidgetItem(QStringList(funcUnitInfoText));
                    funcUnitItem->addChild(funcUintInfoItem);
                }
                subDomainTreeItem->addChild(funcUnitItem);
            }
        }
        unitTreeWidget->addTopLevelItem(funcDomainItem);
    }

    //unitTreeWidget->expandAll(); // 展开所有节点
    //只展开到服务层级
    unitTreeWidget->expandToDepth(0);
}

void SchemeEditDialog::onSearchTextChanged(const QString &text)
{
   performSearch(text);
}

void SchemeEditDialog::performSearch(const QString &text)
{
    // 清空之前的搜索结果onSearchTextChanged
    searchResults.clear();
    currentSearchIndex = -1;

    // 如果搜索文本为空，清除所有高亮并禁用导航按钮
    if (text.isEmpty()) {
        searchUpButton->setEnabled(false);
        searchDownButton->setEnabled(false);
        // 清除所有项的背景色
        QTreeWidgetItemIterator it(unitTreeWidget);
        while (*it) {
          (*it)->setBackground(0, Qt::transparent);
            ++it;
        }
        return;
    }

    // 遍历所有项查找匹配的项
    QTreeWidgetItemIterator it(unitTreeWidget);
    while (*it) {
        if ((*it)->text(0).contains(text, Qt::CaseInsensitive)) {
            searchResults.append(*it);
        }
        // 清除之前的高亮
        (*it)->setBackground(0, Qt::transparent);
        ++it;
    }

    // 更新导航按钮状态
   bool hasResults = !searchResults.isEmpty();
    searchUpButton->setEnabled(hasResults);
    searchDownButton->setEnabled(hasResults);

    // 如果有搜索结果，高亮第一个
    if (hasResults) {
        currentSearchIndex = 0;
        highlightCurrentSearchResult();
    }
}

void SchemeEditDialog::highlightCurrentSearchResult()
{
    if (currentSearchIndex >= 0 && currentSearchIndex < searchResults.size()) {
        // 清除所有项的高亮
        QTreeWidgetItemIterator it(unitTreeWidget);
        while (*it) {
            (*it)->setBackground(0, Qt::transparent);
            ++it;
        }

        // 高亮当前项
        QTreeWidgetItem *currentItem = searchResults[currentSearchIndex];
        QString cText = currentItem->text(0);
        currentItem->setBackground(0, QBrush(QColor(0, 0, 255))); //蓝色

        // 展开到当前项并滚动到可见
        QTreeWidgetItem *parent = currentItem->parent();
        while (parent) {
            parent->setExpanded(true);
            parent = parent->parent();
        }
        unitTreeWidget->scrollToItem(currentItem);
    }
}

void SchemeEditDialog::onSearchUpClicked()
{
    if (searchResults.isEmpty()) return;

    // 向上移动索引
    currentSearchIndex--;
    if (currentSearchIndex < 0) {
        currentSearchIndex = searchResults.size() - 1;
    }

    highlightCurrentSearchResult();
}

void SchemeEditDialog::onSearchDownClicked()
{
    if (searchResults.isEmpty()) return;

    // 向下移动索引
    currentSearchIndex++;
    if (currentSearchIndex >= searchResults.size()) {
        currentSearchIndex = 0;
    }

    highlightCurrentSearchResult();
}

void SchemeEditDialog::filterTreeItems(QTreeWidgetItem *item, const QString &text)
{
    if (!item) return;

    // 获取当前项的文本
    QString itemText = item->text(0) + " " + item->text(1);
    bool match = text.isEmpty() || itemText.contains(text, Qt::CaseInsensitive);

    // 递归处理所有子项
    int childCount = item->childCount();
    bool childVisible = false;

    for (int i = 0; i < childCount; ++i) {
        QTreeWidgetItem *childItem = item->child(i);
        filterTreeItems(childItem, text);
        childVisible |= !childItem->isHidden();
    }

    // 如果当前项匹配或者有可见的子项，则显示当前项
    item->setHidden(!match && !childVisible);

    // 如果有匹配项，展开父节点以显示匹配项
    if (match || childVisible) {
        QTreeWidgetItem *parent = item->parent();
        while (parent) {
            parent->setExpanded(true);
            parent = parent->parent();
        }
    }
}

void SchemeEditDialog::onUnitItemClicked(QTreeWidgetItem *item, int column)
{
    if (!item) return;

    // 获取当前节点的层级
    int level = 0;
    QTreeWidgetItem *parent = item->parent();
    while (parent) {
        level++;
        parent = parent->parent();
    }

    // 如果是第4层节点，显示其子节点数据到schemeTreeView
    if (level == 3) {  // 因为层级从0开始计数
        updateSchemeTreeView(item);
    }
}

void SchemeEditDialog::initializeTreeView()
{
    // 设置列标题
    QStringList headers;
    headers << tr("名称") << tr("单位") << tr("运算符") << tr("预警值")
            << tr("信号类型") << tr("起始位") << tr("起始字") << tr("长度")
            << tr("最高有效位") << tr("最低有效位") << tr("最大值") << tr("最小值");
    treeModel->setHorizontalHeaderLabels(headers);

    // 设置树形视图的样式
    schemeTreeView->setStyleSheet(
        "QTreeView {"
        "   border: 2px solid #d9d9d9;"  // 加粗边框
        "   border-radius: 4px;"
        "   background-color: white;"
        "   outline: none;"
        "}"
        "QTreeView::item {"
        "   height: 32px;"
        "   padding: 4px;"
        "   border-bottom: 1px solid #f0f0f0;"
        "}"
        "QTreeView::item:hover {"
        "   background-color: #e6f7ff;"
        "}"
        "QTreeView::item:selected {"
        "   background-color: #bae7ff;"
        "   color: black;"
        "}"
        "QTreeView::branch:has-children:!has-siblings:closed,"
        "QTreeView::branch:closed:has-children:has-siblings {"
        "   image: url(:/images/arrow-right.png);"
        "}"
        "QTreeView::branch:open:has-children:!has-siblings,"
        "QTreeView::branch:open:has-children:has-siblings {"
        "   image: url(:/images/arrow-down.png);"
        "}"
        );

    // 设置表头样式
    schemeTreeView->header()->setStyleSheet(
        "QHeaderView::section {"
        "   background-color: #fafafa;"
        "   padding: 8px 4px;"
        "   border: none;"
        "   border-right: 1px solid #f0f0f0;"
        "   border-bottom: 1px solid #f0f0f0;"
        "   font-weight: bold;"
        "}"
        );

    // 启用排序
    schemeTreeView->setSortingEnabled(true);

    // 允许多选
    schemeTreeView->setSelectionMode(QAbstractItemView::ExtendedSelection);

    // 设置列宽
    for (int i = 0; i < headers.size(); ++i) {
        schemeTreeView->setColumnWidth(i, 100);
    }

    // 最后一列自动拉伸
    schemeTreeView->header()->setStretchLastSection(true);
}

void SchemeEditDialog::initializeSelectedList()
{
    // 初始化已选清单
    updateSelectedList();
}

void SchemeEditDialog::updateSelectedList()
{
    selectedListWidget->clear();
    // 添加示例已选项
    QStringList selectedItems = {
        "已选项1", "已选项2", "已选项3"
    };

    for (const QString &item : selectedItems) {
        selectedListWidget->addItem(item);
    }
}

void SchemeEditDialog::loadSchemeData(const QString &schemeName)
{
    if(schemeName == "") return ;
    // 获取程序路径下的solution.xml文件路径
    QString userName = UserSession::getInstance().getUsername();
    QString filePath = QCoreApplication::applicationDirPath() + "/scheme/" + userName + "_solution.xml";
    QFile file(filePath);

    if (!file.exists()) {
        QMessageBox::warning(this, tr("错误"), tr("方案文件不存在"));
        return;
    }

    if (!file.open(QIODevice::ReadOnly)) {
        QMessageBox::warning(this, tr("错误"), tr("无法打开方案文件"));
        return;
    }

    QDomDocument doc;
    QString errorMsg;
    int errorLine, errorColumn;
    if (!doc.setContent(&file, &errorMsg, &errorLine, &errorColumn)) {
        file.close();
        QMessageBox::warning(this, tr("错误"),
            tr("XML解析错误：%1\n行：%2，列：%3").arg(errorMsg).arg(errorLine).arg(errorColumn));
        return;
    }
    file.close();

    // 查找指定名称的方案
    QDomElement root = doc.documentElement();
    QDomNodeList solutions = root.elementsByTagName("solution");
    bool found = false;

    for (int i = 0; i < solutions.count(); i++) {
        QDomElement solution = solutions.at(i).toElement();
        if (solution.attribute("name") == schemeName) {
            found = true;
            // 清空现有数据
            selectedListWidget->clear();

            // 遍历所有Msg节点
            QDomNodeList msgs = solution.elementsByTagName("Msg");
            for (int j = 0; j < msgs.count(); j++) {
                QDomElement msg = msgs.at(j).toElement();
                MsgStruct msgStruct;
                msgStruct.m_msgName = msg.attribute("msgName");
                msgStruct.m_srcUnitId = msg.attribute("srcUnitId");
                msgStruct.m_subPubTopicId = msg.attribute("subPubTopicId");

                // 遍历所有topic节点
                QDomNodeList topics = msg.elementsByTagName("topic");
                for (int k = 0; k < topics.count(); k++) {
                    QDomElement topic = topics.at(k).toElement();
                    TopicStruct topicStruct;
                    topicStruct.m_topicName = topic.attribute("name");
                    topicStruct.m_topicShortName = topic.attribute("shortName");

                    // 遍历所有Signal节点
                    QDomNodeList signals_ = topic.elementsByTagName("Signal");
                    for (int l = 0; l < signals_.count(); l++) {
                        QDomElement signal = signals_.at(l).toElement();
                        QString signalId = signal.attribute("id");
                        QString signalName = signal.attribute("name");
                        //topicStruct.m_signals[signalId] = signalName;
                        topicStruct.m_signals.insert(signalId,signalName);
                    }

                    msgStruct.m_topics.append(topicStruct);
                }

                m_solution.insertMsg(msgStruct);

                // 添加到右侧列表
                QListWidgetItem *interfaceItem = new QListWidgetItem(
                    QString("%1").arg(
                        msgStruct.m_msgName
                    )
                );
                selectedListWidget->addItem(interfaceItem);
            }
            break;
        }
    }

//    if (!found) {
//        QMessageBox::warning(this, tr("错误"), tr("未找到指定名称的方案"));
//    }
}

void SchemeEditDialog::onTreeItemClicked(const QModelIndex &index)
{
    if (index.isValid()) {
        QStandardItem *item = treeModel->itemFromIndex(index);
        if (item) {
            // 处理树形项的点击事件
            QString itemText = item->text();
            qDebug() << "Clicked item:" << itemText;
        }
    }
}

//确认按钮
void SchemeEditDialog::onConfirmClicked()
{
    QString newName = schemeNameEdit->text().trimmed();
    if (newName.isEmpty()) {
        QMessageBox::warning(this, tr("提示"), tr("方案名称不能为空"));
        return;
    }

    // 获取程序路径下的solution.xml文件路径
    QString userName = UserSession::getInstance().getUsername();
    QString filePath = QCoreApplication::applicationDirPath() + "/scheme/" + userName + "_solution.xml";
    QFile file(filePath);

    //获取ICD VERSION
    QString icdVersion = QString::fromStdString(getCurrentIcdVersion(m_decoderToken));

    // 读取现有XML文件
    QDomDocument doc;
    if (file.exists()) {
        if (!file.open(QIODevice::ReadOnly)) {
            QMessageBox::warning(this, tr("错误"), tr("无法打开方案文件"));
            return;
        }
        QString errorMsg;
        int errorLine, errorColumn;
        if (!doc.setContent(&file, &errorMsg, &errorLine, &errorColumn)) {
            file.close();
            QMessageBox::warning(this, tr("错误"),
                tr("XML解析错误：%1\n行：%2，列：%3").arg(errorMsg).arg(errorLine).arg(errorColumn));
            return;
        }
        file.close();
    } else {
        // 如果文件不存在，创建新的XML文档
        QDomElement root = doc.createElement("root");
        doc.appendChild(root);
    }

    // 查找并更新指定名称的方案
    QDomElement root = doc.documentElement();
    QDomNodeList solutions = root.elementsByTagName("solution");
    bool found = false;

    for (int i = 0; i < solutions.count(); i++) {
        QDomElement solution = solutions.at(i).toElement();
        if (solution.attribute("name") == currentSchemeName && solution.attribute("ICD_name") == icdVersion) {
            found = true;
            // 更新方案名称
            solution.setAttribute("name", newName);
            solution.setAttribute("ICD_name", icdVersion);
            //初始状态为未分享
            solution.setAttribute("isShared",false);

            // 删除旧的Msg节点
            QDomNodeList oldMsgs = solution.elementsByTagName("Msg");
            for (int j = oldMsgs.count() - 1; j >= 0; j--) {
                solution.removeChild(oldMsgs.at(j));
            }

            // 添加新的Msg节点
            for (auto it = m_solution.m_msgs.begin(); it != m_solution.m_msgs.end(); ++it) {
                const MsgStruct &msg = it.value();
                QDomElement msgElement = doc.createElement("Msg");
                msgElement.setAttribute("msgName",msg.m_msgName);
                msgElement.setAttribute("srcUnitId", msg.m_srcUnitId);
                msgElement.setAttribute("subPubTopicId", msg.m_subPubTopicId);
                // 遍历每个topic
                for (const TopicStruct &topic : msg.m_topics) {
                    QDomElement topicElement = doc.createElement("topic");
                    topicElement.setAttribute("shortName",topic.m_topicShortName);
                    topicElement.setAttribute("name", topic.m_topicName);
                    // 遍历每个signal
                    for (auto it = topic.m_signals.begin(); it != topic.m_signals.end(); ++it) {
                        QDomElement signalElement = doc.createElement("Signal");
                        signalElement.setAttribute("name", it.value());
                        signalElement.setAttribute("id", it.key());
                        topicElement.appendChild(signalElement);
                    }

                    msgElement.appendChild(topicElement);
                }

                solution.appendChild(msgElement);
            }

            // 将新的solution添加到root
            root.appendChild(solution);
        }
    }

    if (!found) {
        QMessageBox::warning(this, tr("错误"), tr("未找到要修改的方案"));
        return;
    }

    // 保存XML文件
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::warning(this, tr("错误"), tr("无法保存方案文件"));
        return;
    }

    QTextStream out(&file);
    out.setCodec("UTF-8");
    doc.save(out, 4); // 4是缩进空格数
    QString content = doc.toString(4);
    file.close();

    //QMessageBox::information(this, tr("提示"), tr("方案已成功修改"));
    //accept();
    QString fileName = QFileInfo(filePath).fileName();
    uploadToServer(content,fileName);
}

void SchemeEditDialog::onCancelClicked()
{
    //reject();
    clearView();
    emit editSchemefinish(0);
}

//另存为按钮
void SchemeEditDialog::onSaveAsClicked()
{
    QString newName = schemeNameEdit->text().trimmed();
    if (newName.isEmpty()) {
        QMessageBox::warning(this, tr("提示"), tr("方案名称不能为空"));
        return;
    }

    if(selectedListWidget->count() == 0)
    {
        QMessageBox::warning(this, tr("提示"), tr("添加信号不能为空"));
        return;
    }
    //获取ICD VERSION
    QString icdVersion = QString::fromStdString(getCurrentIcdVersion(m_decoderToken));

    // 获取程序路径下的solution.xml文件路径
    QString userName = UserSession::getInstance().getUsername();
    QString filePath = QCoreApplication::applicationDirPath() + "/scheme/" +userName + "_solution.xml";
    QFile file(filePath);

    // 读取现有XML文件
    QDomDocument doc;
    if (file.exists()) {
        if (!file.open(QIODevice::ReadOnly)) {
            QMessageBox::warning(this, tr("错误"), tr("无法打开方案文件"));
            return;
        }
        QString errorMsg;
        int errorLine, errorColumn;
        if (!doc.setContent(&file, &errorMsg, &errorLine, &errorColumn)) {
            file.close();
            QMessageBox::warning(this, tr("错误"),
                                 tr("XML解析错误：%1\n行：%2，列：%3").arg(errorMsg).arg(errorLine).arg(errorColumn));
            return;
        }
        file.close();
        // 检查是否存在同名solution
        QDomElement root = doc.documentElement();
        QDomNodeList solutions = root.elementsByTagName("solution");
        for (int i = 0; i < solutions.count(); i++) {
            QDomElement solution = solutions.at(i).toElement();
            if (solution.attribute("name") == newName && solution.attribute("ICD_name") == icdVersion) {
                QMessageBox::warning(this, tr("错误"), tr("相同版本icd已存在同名方案，请重新输入方案名称"));
                return;
            }
        }
    } else {
        // 如果文件不存在，创建新的XML文档
        QDomElement root = doc.createElement("root");
        doc.appendChild(root);
    }

    // 创建新的solution元素
    QDomElement root = doc.documentElement();
    QDomElement solution = doc.createElement("solution");
    solution.setAttribute("name", newName);
    solution.setAttribute("ICD_name", icdVersion);
    //初始状态为未分享
    solution.setAttribute("isShared",false);

    // 遍历m_solution中的每个MsgStruct
    for (auto it = m_solution.m_msgs.begin(); it != m_solution.m_msgs.end(); ++it) {
        const MsgStruct &msg = it.value();
        QDomElement msgElement = doc.createElement("Msg");
        msgElement.setAttribute("msgName",msg.m_msgName);
        msgElement.setAttribute("srcUnitId", msg.m_srcUnitId);
        msgElement.setAttribute("subPubTopicId", msg.m_subPubTopicId);
        // 遍历每个topic
        for (const TopicStruct &topic : msg.m_topics) {
            QDomElement topicElement = doc.createElement("topic");
            topicElement.setAttribute("shortName",topic.m_topicShortName);
            topicElement.setAttribute("name", topic.m_topicName);
            // 遍历每个signal
            for (auto it = topic.m_signals.begin(); it != topic.m_signals.end(); ++it) {
                QDomElement signalElement = doc.createElement("Signal");
                QStringList nameTmp = it.value().split("/");
                nameTmp.removeFirst();
                QString removeFirstSignalFullName = nameTmp.join("/");
                signalElement.setAttribute("name", removeFirstSignalFullName);
                signalElement.setAttribute("id", it.key());
                topicElement.appendChild(signalElement);
            }

            msgElement.appendChild(topicElement);
        }

        solution.appendChild(msgElement);
    }

    // 将新的solution添加到root
    root.appendChild(solution);

    // 保存XML文件
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::warning(this, tr("错误"), tr("无法保存方案文件"));
        return;
    }

    QTextStream out(&file);
    out.setCodec("UTF-8");
    doc.save(out, 4); // 4是缩进空格数
    QString content = doc.toString(4);
    file.close();

    //QMessageBox::information(this, tr("提示"), tr("方案已另存为：%1").arg(newName));
    // 获取文件内容和文件名

    QString fileName = QFileInfo(filePath).fileName();
    uploadToServer(content,fileName);
    //done(1);
}

void SchemeEditDialog::onDeleteSelectedItem()
{
    QListWidgetItem *currentItem = selectedListWidget->currentItem();
    if (currentItem) {
        delete selectedListWidget->takeItem(selectedListWidget->row(currentItem));
    }
}

void SchemeEditDialog::updateSchemeTreeView(QTreeWidgetItem *selectedItem)
{
    if (!selectedItem) return;

    // 清除现有数据
    treeModel->clear();

    // 重新设置表头
    QStringList headers;
    headers << tr("名称") << tr("序列化编码");
    treeModel->setColumnCount(2);
    treeModel->setHorizontalHeaderLabels(headers);
    schemeTreeView->setColumnWidth(0, 400);

    QString funcUnitInfoName = selectedItem->text(0);
    Message message;
    // 获取接口详细信息
    int result;
    try {
        m_decoderToken = FileListService::getInstance().getParseandanalysisP();
        result = getMessageInfoByMessageName(m_decoderToken,funcUnitInfoName.toStdString(), message);
    } catch(const std::exception& e) {
        qDebug()<<"getTopicInfoByShortName failed:"<<e.what();
    }
    parseFromLib tmp;
    tmp.msgName = QString::fromStdString(message.messageInfo.msgName);
    tmp.srcUnitId = QString::number(message.messageInfo.sourceFuncId);
    tmp.subPubTopId = QString::number(message.messageInfo.pubSubTopicId);
    m_message_infoFromLib.insert(tmp.msgName,tmp);

    //功能单元消息
    QString funcUnitMsgText = QString::fromStdString(message.messageInfo.msgName);
    QStandardItem* funcUnitMsgItem = new QStandardItem(funcUnitMsgText);
    treeModel->appendRow(funcUnitMsgItem);
    QString funcUnitMsgThemeText = QString::fromStdString(message.messageTopic.name);
    QStandardItem* funcUnitMsgThemeItem = new QStandardItem(funcUnitMsgThemeText);
    funcUnitMsgItem->appendRow(funcUnitMsgThemeItem);
    for(const auto& signal:message.messageTopic.signalVec)
    {
        QStringList parts = QString::fromStdString(signal->name).split("/");
        QStandardItem *signalItem = addSignalRecursive(funcUnitMsgThemeItem,parts);

        QString signalId = QString::fromStdString(signal->id);
        QStandardItem* signalIdItem =  new QStandardItem(signalId);
        signalItem->parent()->setChild(signalItem->parent()->rowCount()-1,1,signalIdItem);

        signalItem->setCheckable(true);
        signalItem->setCheckState(Qt::Checked);
    }

    // 展开所有节点
    schemeTreeView->expandAll();
}

void SchemeEditDialog::addChildToSchemeTree(QTreeWidgetItem *sourceItem, QStandardItem *parentItem)
{
    if (!sourceItem || !parentItem) return;

    // 创建新的行项目
    QList<QStandardItem*> rowItems;

    // 添加名称列
    QStandardItem *nameItem = new QStandardItem(sourceItem->text(0));
    nameItem->setCheckable(true);
    nameItem->setCheckState(sourceItem->checkState(0));
    rowItems << nameItem;

    // 添加其他列的默认值
    for (int i = 1; i < 12; ++i) {
        QStandardItem *item = new QStandardItem();
        if (i == 1) item->setText("--"); // 单位
        else if (i == 2) item->setText("="); // 运算符
        else if (i == 3) item->setText("0"); // 预警值
        else if (i == 4) item->setText("INT"); // 信号类型
        else item->setText("0"); // 其他数值列
        rowItems << item;
    }

    // 将行添加到父项
    parentItem->appendRow(rowItems);

    // 递归处理子节点
    for (int i = 0; i < sourceItem->childCount(); ++i) {
        QTreeWidgetItem *childItem = sourceItem->child(i);
        if (childItem->checkState(0) == Qt::Checked) {
            addChildToSchemeTree(childItem, nameItem);
        }
    }
}

void SchemeEditDialog::onClearSelectionClicked()
{
    schemeTreeView->clearSelection();
    // 如果需要，还可以取消所有项的选中状态
    QStandardItem *rootItem = treeModel->invisibleRootItem();
    for (int i = 0; i < rootItem->rowCount(); ++i) {
        QStandardItem *item = rootItem->child(i);
        if (item && item->isCheckable()) {
            item->setCheckState(Qt::Unchecked);
        }
    }
}

void SchemeEditDialog::onAddToSchemeClicked()
{
    QVector<MsgStruct> msgStructs;
    QModelIndexList selectedIndexes = schemeTreeView->selectionModel()->selectedRows();
    if (selectedIndexes.isEmpty()) {
        QMessageBox::warning(this, tr("提示"), tr("请先选择要添加的项"));
        return;
    }

    // 遍历所有根节点
    int rootCount = treeModel->rowCount();
    for (int rootIndex = 0; rootIndex < rootCount; rootIndex++) {
        QModelIndex rootNode = treeModel->index(rootIndex, 0);
        if (!rootNode.isValid()) continue;

        // 获取根节点信息
        QString rootText = treeModel->data(rootNode).toString();
        // 从根节点文本中提取接口信息

        MsgStruct  msgStruct;
        if (m_message_infoFromLib.find(rootText) != m_message_infoFromLib.end()){
            parseFromLib tmp = m_message_infoFromLib[rootText];
            msgStruct.m_msgName = tmp.msgName;
            msgStruct.m_srcUnitId = tmp.srcUnitId;
            msgStruct.m_subPubTopicId = tmp.subPubTopId;
        }

        // 遍历该根节点下的所有第二层节点
        int topicCount = treeModel->rowCount(rootNode);
        for (int topicIndex = 0; topicIndex < topicCount; topicIndex++) {
            QModelIndex topicNode = treeModel->index(topicIndex, 0, rootNode);
            if (!topicNode.isValid()) continue;

            TopicStruct topic;
            topic.m_topicName = treeModel->data(topicNode).toString();
            QStringList parts = rootText.split("/");
            if (parts.size() != 2) continue;
            topic.m_topicShortName = parts[1].trimmed();
            // 构建初始路径（从topic开始）
            QStringList pathParts;
            pathParts.append(topic.m_topicName);

            // 递归处理所有子节点
            processChildNodes(topicNode, pathParts, topic);
            // 只有当topic下有选中的信号时才添加到msgStruct
            if (!topic.m_signals.isEmpty()) {
                msgStruct.m_topics.append(topic);
            }

        }
        // 只有当msgStruct下有选中的topic时才添加到msgStructs
        if (!msgStruct.m_topics.isEmpty()) {
            msgStructs.append(msgStruct);
            m_solution.insertMsg(msgStruct);
        }
    }

    // 将处理后的数据添加到右侧列表
    for (const MsgStruct &msgStruct : msgStructs) {
        // 添加接口信息
        QListWidgetItem *interfaceItem = new QListWidgetItem(
                    QString("%1").arg(
                        msgStruct.m_msgName
                        )
                    );
        selectedListWidget->addItem(interfaceItem);
    }
}

void SchemeEditDialog::processChildNodes(const QModelIndex &parentIndex, QStringList &pathParts, TopicStruct &topic)
{
    int childCount = treeModel->rowCount(parentIndex);
    for (int i = 0; i < childCount; i++) {
        QModelIndex childIndex = treeModel->index(i, 0, parentIndex);

        // 如果当前节点是叶子节点
        if (treeModel->rowCount(childIndex) == 0) {
            // 如果当前节点被选中
            if (schemeTreeView->selectionModel()->isSelected(childIndex))
            {
                QString signalName = pathParts.join("/") + "/" + treeModel->data(childIndex).toString();
                QString signalId = treeModel->data(childIndex.sibling(childIndex.row(), 1)).toString();
                if(signalName.startsWith("/"))
                {
                    signalName.remove(0,1);
                }
                if (!signalName.isEmpty() && !signalId.isEmpty()) {
                    //topic.m_signals[signalId] = signalName;
                    topic.m_signals.insert(signalId,signalName);
                }
            }
        } else {
            // 如果不是叶子节点，继续递归遍历
            pathParts.append(treeModel->data(childIndex).toString());
            processChildNodes(childIndex, pathParts, topic);
            pathParts.removeLast(); // 回溯时移除当前节点
        }
    }
}

void SchemeEditDialog::showContextMenu(const QPoint &pos)
{
    QListWidgetItem *item = selectedListWidget->itemAt(pos);
    if (!item) return;

    QMenu contextMenu(this);
    QAction *deleteAction = contextMenu.addAction(tr("删除"));
    // 设置删除动作的样式
    // 设置菜单样式，包括删除动作的悬停效果
    contextMenu.setStyleSheet(
                "QMenu { background-color: white; border: 1px solid #CCCCCC; }"
                "QMenu::item { padding: 5px 20px; }"
                "QMenu::item:selected { background-color: #F0F0F0; }"
                "QMenu::item[text='删除']:hover { color: red; }"
                );

    QAction *selectedAction = contextMenu.exec(selectedListWidget->mapToGlobal(pos));
    if (selectedAction == deleteAction) {
        // 获取当前项的行号
        int row = selectedListWidget->row(item);

        //删除存储在内存的数据
        QString itemText = selectedListWidget->currentItem()->text();
        m_solution.deleteMsg(itemText);

        // 删除该项
        delete selectedListWidget->takeItem(row);
    }
}
void SchemeEditDialog::onUploadFinished(QNetworkReply *reply)
{
    // 处理响应
    if (reply->error() == QNetworkReply::NoError) {
        // 读取响应数据
        QByteArray responseData = reply->readAll();

        // 尝试解析 JSON 响应
        QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData);
        if (!jsonDoc.isNull() && jsonDoc.isObject()) {
            QJsonObject responseObj = jsonDoc.object();

            // 获取顶层字段
            QString code = responseObj["code"].toString();
            QString message = responseObj["message"].toString();
            bool success = responseObj["success"].toBool();

            if (success) {
                // 获取 data 对象
                QJsonObject dataObj = responseObj["data"].toObject();
                if (!dataObj.isEmpty()) {
                    // 提取 data 中的字段
                    QString name = dataObj["name"].toString();
                    QString type = dataObj["type"].toString();
                    QString url = dataObj["url"].toString();
                    QString createTime = dataObj["createTime"].toString();
                    QString updateTime = dataObj["updateTime"].toString();
                    int id = dataObj["id"].toInt();

                    // 构建成功消息
                    QString successMsg = tr("文件已成功上传到服务器\n\n");
                    successMsg += tr("名称: %1\n").arg(name);
                    successMsg += tr("类型: %1\n").arg(type);

                    successMsg += tr("ID: %1\n").arg(id);
                    successMsg += tr("创建时间: %1\n").arg(createTime);
                    successMsg += tr("更新时间: %1\n").arg(updateTime);

                    if (!url.isEmpty())
                        successMsg += tr("URL: %1").arg(url);

                    //QMessageBox::information(this, tr("上传成功"), successMsg);
                } else {
                    QMessageBox::information(this, tr("上传成功"),
                                            tr("文件已成功上传，但服务器返回的数据为空"));
                }
            } else {
                // 显示错误信息
                QString errorMsg = tr("上传失败: %1").arg(message);
                QMessageBox::warning(this, tr("上传失败"), errorMsg);
            }
        } else {
            QMessageBox::information(this, tr("上传成功"),
                                    tr("文件已上传，但服务器响应不是有效的JSON格式"));
        }
    } else {
        // 显示网络错误信息
        QMessageBox::warning(this, tr("上传失败"),
                            tr("无法上传文件: %1").arg(reply->errorString()));
    }

    // 释放响应对象
    reply->deleteLater();
    //done(1);
    clearView();
    emit editSchemefinish(1);
}

void SchemeEditDialog::clearView()
{
    //清除内存数据
    m_solution.clearMsg();

    // 清除现有数据
    treeModel->clear();
    selectedListWidget->clear();
}

void SchemeEditDialog::uploadToServer(const QString &content, const QString &fileName)
{

    // 获取用户会话数据
    QString token = UserSession::getInstance().getToken();

    // 准备请求
    QNetworkRequest request(ApiUrlManager::getInstance().getUrl(ApiUrlManager::SCHEME_UPDATE));
    request.setRawHeader("accept", "*/*");
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    if (!token.isEmpty()) {
        request.setRawHeader("Authorization", token.toUtf8());
    }

    // 创建 JSON 数据
    QJsonObject jsonObj;
    jsonObj["content"] = content;
    jsonObj["id"] = 0;  // 按接口要求设置为0
    jsonObj["name"] = fileName;
    jsonObj["type"] = "solutions";
    jsonObj["userId"] =  UserSession::getInstance().getUserId();

    // 添加用户ID（如果可用）
    if (UserSession::getInstance().isLoggedIn() && UserSession::getInstance().getUserId() > 0) {
        jsonObj["userId"] = UserSession::getInstance().getUserId();
    }

    QJsonDocument jsonDoc(jsonObj);
    QByteArray jsonData = jsonDoc.toJson();

    // 发送 POST 请求
    m_networkManager->post(request, jsonData);
}

QStandardItem* SchemeEditDialog::addSignalRecursive(QStandardItem* node, const QStringList& pathParts)
{
    if (pathParts.isEmpty()) {
        return node;
    }

    QString name = pathParts.first();
    QStandardItem* nextItem = nullptr;
    bool flag = false;
    // 遍历当前节点的所有子节点
    int num = node->rowCount();
    QString nodeName = node->text();
    for (int row = 0; row < node->rowCount(); ++row) {
        QStandardItem* child = node->child(row);

        // 如果子项的文本与目标名称匹配，返回该子项
        if (child->text() == name) {
            nextItem = child;
            flag = true;
            break;
        }
    }

    // 如果没有找到对应名称的子项，新建一个子项并添加到当前节点
    if(!flag)
    {
        nextItem = new QStandardItem(name);
        nextItem->setText(name);
        node->appendRow(nextItem);  // 将新子项添加到当前节点下
    }

    // 递归处理剩余部分
    return addSignalRecursive(nextItem, pathParts.mid(1));
}
