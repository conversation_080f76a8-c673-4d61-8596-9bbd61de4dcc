#ifndef FILE_UTILS_H
#define FILE_UTILS_H

#include <QString>
#include <QDir>

class FileUtils {
public:
    static bool ensureDirectoryExists(const QString &dirPath);
    static bool ensureFileExists(const QString &filePath, const QString &defaultContent = QString());
    static QString getConfigPath(const QString &appName);
    
private:
    FileUtils() {} // 私有构造函数，防止实例化
};

#endif // FILE_UTILS_H 