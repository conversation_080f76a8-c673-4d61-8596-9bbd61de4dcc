#include "document_window.h"
#include "ui_documentwindow.h"
#include <QFileDialog>
#include <QMessageBox>
#include <QDateTime>

DocumentWindow::DocumentWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::DocumentWindow)
    , documentModel(new QStandardItemModel(this))
{
    ui->setupUi(this);
    setupUI();
    setupConnections();
    initializeDocumentList();
}

DocumentWindow::~DocumentWindow()
{
    delete ui;
}

void DocumentWindow::setupUI()
{
    // 设置树形视图的列头
    QStringList headers;
    headers << "标题" << "上传时间" << "FC设置状态" << "功能显示状态";
    documentModel->setHorizontalHeaderLabels(headers);
    ui->documentTree->setModel(documentModel);

    // 调整列宽
    ui->documentTree->setColumnWidth(0, 200);
    ui->documentTree->setColumnWidth(1, 150);
    ui->documentTree->setColumnWidth(2, 100);
    ui->documentTree->setColumnWidth(3, 100);
}

void DocumentWindow::setupConnections()
{
    connect(ui->uploadFCButton, &QPushButton::clicked, this, &DocumentWindow::onUploadFCClicked);
    connect(ui->uploadICDButton, &QPushButton::clicked, this, &DocumentWindow::onUploadICDClicked);
}

void DocumentWindow::initializeDocumentList()
{
    // 添加示例数据
    QList<QStandardItem*> row;
    
    QStandardItem *titleItem = new QStandardItem("FC控制器");
    QStandardItem *timeItem = new QStandardItem(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    QStandardItem *fcStatusItem = new QStandardItem("1394协议配分");
    QStandardItem *funcStatusItem = new QStandardItem("功能显示完成");
    
    row << titleItem << timeItem << fcStatusItem << funcStatusItem;
    documentModel->appendRow(row);
}

void DocumentWindow::onUploadFCClicked()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        tr("选择FC设置表单"), "",
        tr("Excel Files (*.xlsx *.xls);;All Files (*)"));
        
    if (!fileName.isEmpty()) {
        // TODO: 处理文件上传
        QMessageBox::information(this, "提示", "FC设置表单上传成功");
    }
}

void DocumentWindow::onUploadICDClicked()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        tr("选择ICD文件"), "",
        tr("ICD Files (*.icd);;All Files (*)"));
        
    if (!fileName.isEmpty()) {
        // TODO: 处理文件上传
        QMessageBox::information(this, "提示", "ICD文件上传成功");
    }
}

void DocumentWindow::onPreviewClicked()
{
    // TODO: 实现预览功能
} 