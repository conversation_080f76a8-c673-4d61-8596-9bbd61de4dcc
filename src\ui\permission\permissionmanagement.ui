<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PermissionManagement</class>
 <widget class="QWidget" name="PermissionManagement">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>700</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>权限管理</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget {
    background-color: #f0f2f5;
    font-family: Microsoft YaHei, Arial, sans-serif;
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>15</number>
   </property>
   <property name="leftMargin">
    <number>20</number>
   </property>
   <property name="topMargin">
    <number>20</number>
   </property>
   <property name="rightMargin">
    <number>20</number>
   </property>
   <property name="bottomMargin">
    <number>20</number>
   </property>
   <item>
    <widget class="QWidget" name="contentWidget" native="true">
     <property name="styleSheet">
      <string notr="true">QWidget#contentWidget {
    background-color: #ffffff;
    border-radius: 4px;
}</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>15</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="leftPanel" native="true">
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>250</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">QWidget#leftPanel {
    background-color: #1890ff;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_3">
         <property name="spacing">
          <number>10</number>
         </property>
         <property name="leftMargin">
          <number>10</number>
         </property>
         <property name="topMargin">
          <number>15</number>
         </property>
         <property name="rightMargin">
          <number>10</number>
         </property>
         <property name="bottomMargin">
          <number>15</number>
         </property>
         <item>
          <widget class="QPushButton" name="addRoleButton">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>36</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton {
    background-color: #ffffff;
    color: #1890ff;
    border: none;
    border-radius: 4px;
    padding: 5px 15px;
}
QPushButton:hover {
    background-color: #e6f7ff;
}
QPushButton:pressed {
    background-color: #d9d9d9;
}</string>
           </property>
           <property name="text">
            <string>新增角色</string>
           </property>
           <property name="icon">
            <iconset>
             <normaloff>:/images/add.png</normaloff>:/images/add.png</iconset>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QListWidget" name="roleListWidget">
           <property name="styleSheet">
            <string notr="true">QListWidget {
    background-color: transparent;
    border: none;
    color: white;
    outline: none;
}
QListWidget::item {
    padding: 10px;
    border-radius: 4px;
}
QListWidget::item:hover {
    background-color: #40a9ff;
}
QListWidget::item:selected {
    background-color: #096dd9;
    color: white;
}</string>
           </property>
           <item>
            <property name="text">
             <string>管理员</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>普通用户</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>访客</string>
            </property>
           </item>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="rightPanel" native="true">
        <property name="styleSheet">
         <string notr="true">QWidget#rightPanel {
    background-color: #ffffff;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <property name="leftMargin">
          <number>15</number>
         </property>
         <property name="topMargin">
          <number>15</number>
         </property>
         <property name="rightMargin">
          <number>15</number>
         </property>
         <property name="bottomMargin">
          <number>15</number>
         </property>
         <item>
          <layout class="QHBoxLayout" name="topBarLayout">
           <item>
            <widget class="QLabel" name="roleLabel">
             <property name="styleSheet">
              <string notr="true">font-size: 14px; 
font-weight: bold; 
color: #1890ff; 
background-color: #e6f7ff; 
padding: 5px 10px; 
border-radius: 4px;</string>
             </property>
             <property name="text">
              <string>管理员</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="saveButton">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>32</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>80</width>
               <height>32</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
   background-color: #1890ff;
   color: white;
   border: none;
   border-radius: 4px;
   padding: 5px 15px;
   font-weight: bold;
}
QPushButton:hover {
   background-color: #40a9ff;
}
QPushButton:pressed {
   background-color: #096dd9;
}</string>
             </property>
             <property name="text">
              <string>保存</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QTreeView" name="permissionTreeView">
           <property name="styleSheet">
            <string notr="true">QTreeView {
   border: none;
   background-color: white;
   selection-background-color: #e6f7ff;
   selection-color: #1890ff;
}
QTreeView::item {
   padding: 8px 5px;
   border-bottom: 1px solid #f0f0f0;
}
QTreeView::branch:has-children:!has-siblings:closed,
QTreeView::branch:closed:has-children:has-siblings {
   image: url(:/images/arrow-right.png);
}
QTreeView::branch:open:has-children:!has-siblings,
QTreeView::branch:open:has-children:has-siblings {
   image: url(:/images/arrow-down.png);
}

QScrollBar:vertical {
   border: none;
   background: #f0f0f0;
   width: 6px;
   margin: 0px 0px 0px 0px;
}
QScrollBar::handle:vertical {
   background: #d9d9d9;
   min-height: 30px;
   border-radius: 3px;
}
QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
   height: 0px;
}</string>
           </property>
           <property name="selectionMode">
            <enum>QAbstractItemView::NoSelection</enum>
           </property>
           <property name="headerHidden">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui> 