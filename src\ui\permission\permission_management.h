#ifndef PERMISSION_MANAGEMENT_H
#define PERMISSION_MANAGEMENT_H

#include <QWidget>
#include <QStandardItemModel>
#include <QStandardItem>
#include <QInputDialog>
#include <QListWidgetItem>
#include <QComboBox>

// 前向声明
class QCheckBox;

namespace Ui {
class PermissionManagement;
}

class PermissionManagement : public QWidget
{
    Q_OBJECT

public:
    explicit PermissionManagement(QWidget *parent = nullptr);
    ~PermissionManagement() override;

private slots:
    void onRoleListItemClicked(QListWidgetItem *item);
    void onFunctionItemChanged(QStandardItem *item);
    void onSaveButtonClicked();
    void onDataRangeChanged(int index);
    void onAddRoleButtonClicked();

private:
    void setupConnections();
    void initializePermissionTree();
    void initializeRoleList();
    void loadRolePermissionData(const QString &role);
    void addFunctionGroup(const QString &groupName, const QStringList &functions, const QStringList &operations);
    void addDataRangeComboBoxes();
    QString getCurrentRole() const;
    QModelIndex findItemIndex(const QString &text);
    QComboBox* getDataRangeComboBox(const QString &functionName = QString());

    Ui::PermissionManagement *ui;
    QStandardItemModel *permissionModel;
    QStringList roleList;
};

#endif // PERMISSION_MANAGEMENT_H