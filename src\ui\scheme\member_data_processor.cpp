#include "member_data_processor.h"
#include <QDebug>
#include <stdexcept>

MemberDataProcessor::MemberDataProcessor(QObject* parent)
    : QObject(parent), totalProcessedCount(0), totalChangedCount(0), totalProcessingTime(0)
{
    // 设置线程池参数 - 保留一个线程给UI，其余用于数据处理
    int idealThreadCount = QThread::idealThreadCount();
    localThreadPool.setMaxThreadCount(idealThreadCount > 1 ? idealThreadCount - 1 : 1);
    
    // 设置线程池超时
    localThreadPool.setExpiryTimeout(10000); // 10秒
}

MemberDataProcessor::~MemberDataProcessor()
{
    // 等待所有任务完成
    localThreadPool.waitForDone();
}

void MemberDataProcessor::processMemberUpdate(const QString& memberId, const MemberData& data)
{
    QMap<QString, MemberData> singleUpdate;
    singleUpdate.insert(memberId, data);
    processBatchUpdate(singleUpdate);
}

void MemberDataProcessor::processBatchUpdate(const QMap<QString, MemberData>& updates)
{
    if (updates.isEmpty()) return;
    
    perfTimer.start();
    QMutexLocker locker(&dataMutex);
    
    try {
        // 快速过滤出有实际变化的数据
        QMap<QString, MemberData> significantChanges = filterSignificantChanges(updates);
        
        // 更新缓存
        for (auto it = significantChanges.constBegin(); it != significantChanges.constEnd(); ++it) {
            memberDataCache[it.key()] = it.value();
        }
        
        // 仅当有实际变化时才发出信号
        if (!significantChanges.isEmpty()) {
            emit batchDataChanged(significantChanges);
        }
        
        // 更新性能统计
        qint64 elapsed = perfTimer.elapsed();
        updateStatistics(updates.size(), significantChanges.size(), elapsed);
        
        // 每100次更新发送一次统计数据
        if ((totalProcessedCount % 100) == 0) {
            emit processingStats(totalProcessedCount, totalChangedCount, 
                               totalProcessingTime / (double)totalProcessedCount);
        }
    }
    catch (const std::exception& e) {
        emit processingError(QString("处理成员数据时发生错误: %1").arg(e.what()));
    }
}

void MemberDataProcessor::processRawDataPacket(const QByteArray& packet)
{
    // 使用线程池并行处理原始数据包
    QtConcurrent::run(&localThreadPool, [this, packet]() {
        try {
            // 提取成员数据（可能是耗时操作）
            QMap<QString, MemberData> extractedData = extractMemberDataFromPacket(packet);
            
            // 将处理结果发送到主处理方法
            QMetaObject::invokeMethod(this, "processBatchUpdate",
                                     Qt::QueuedConnection,
                                     Q_ARG(QMap<QString, MemberData>, extractedData));
        }
        catch (const std::exception& e) {
            emit processingError(QString("解析数据包时发生错误: %1").arg(e.what()));
        }
    });
}

QMap<QString, MemberData> MemberDataProcessor::filterSignificantChanges(
    const QMap<QString, MemberData>& updates)
{
    QMap<QString, MemberData> result;
    
    for (auto it = updates.constBegin(); it != updates.constEnd(); ++it) {
        const QString& memberId = it.key();
        const MemberData& newData = it.value();
        
        // 检查是否有缓存的数据
        if (memberDataCache.contains(memberId)) {
            const MemberData& cachedData = memberDataCache[memberId];
            
            // 使用MemberData的判断方法确定是否有重要变化
            if (newData.hasSignificantChanges(cachedData)) {
                result.insert(memberId, newData);
            }
        }
        else {
            // 没有缓存，视为新数据
            result.insert(memberId, newData);
        }
    }
    
    return result;
}

QMap<QString, MemberData> MemberDataProcessor::extractMemberDataFromPacket(const QByteArray& packet)
{
    QMap<QString, MemberData> result;
    
    // 这里是解析数据包的逻辑，根据您的协议格式实现
    // 例如，从二进制、JSON或自定义格式提取成员数据
    
    // 示例实现（假设数据包是序列化的JSON）:
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(packet, &error);
    
    if (error.error != QJsonParseError::NoError) {
        throw std::runtime_error("无效的数据包格式");
    }
    
    QJsonObject root = doc.object();
    
    // 假设格式是 {"memberUpdates": {"member_001": {...}, "member_002": {...}, ...}}
    if (root.contains("memberUpdates")) {
        QJsonObject updates = root["memberUpdates"].toObject();
        
        for (auto it = updates.begin(); it != updates.end(); ++it) {
            QString memberId = it.key();
            QJsonObject dataObj = it.value().toObject();
            
            // 从JSON构造MemberData
            MemberData data;
            data.name = dataObj["name"].toString();
            data.signalValue = dataObj["signalValue"].toDouble();
            data.unit = dataObj["unit"].toString();
            data.signalType = dataObj["signalType"].toString();
            data.startWord = dataObj["startWord"].toInt();
            data.startBit = dataObj["startBit"].toInt();
            data.status = dataObj["status"].toString();
            data.length = dataObj["length"].toInt();
            data.rawValue = dataObj["rawValue"].toString();
            data.maxValue = dataObj["maxValue"].toDouble();
            data.minValue = dataObj["minValue"].toDouble();
            
            // 添加到结果
            result.insert(memberId, data);
        }
    }
    
    return result;
}

void MemberDataProcessor::updateStatistics(int totalCount, int changedCount, qint64 processingTime)
{
    totalProcessedCount += totalCount;
    totalChangedCount += changedCount;
    totalProcessingTime += processingTime;
} 