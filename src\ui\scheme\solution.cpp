﻿#include "solution.h"

solution::solution()
{

}

void solution::insertMsg(MsgStruct msg)
{
    QString key = msg.m_srcUnitId+":"+msg.m_subPubTopicId;
    m_msgs.insert(key,msg);
}

void solution::deleteMsg(MsgStruct msg)
{
    QString key = msg.m_srcUnitId+"-"+ msg.m_subPubTopicId;
    m_msgs.remove(key);
}

void solution::deleteMsg(QString msgString)
{
    m_msgs.remove(msgString);
}

void solution::clearMsg()
{
    m_msgs.clear();
}

