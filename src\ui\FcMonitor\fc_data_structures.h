#ifndef FC_DATA_STRUCTURES_H
#define FC_DATA_STRUCTURES_H

#include <QString>
#include <QVector>
#include <QMap>
#include <QSet>
#include <QStandardItem>
#include <QDateTime>

/**
 * @brief FC信号数据结构
 * 对应XML中的<信号>标签，支持嵌套结构
 */
class FcSignalData {
public:
    QString signalId;                   // 序列化编码
    QString signalName;                 // 信号名称
    QString signalIdentifier;           // 信号标识符
    QString signalType;                 // 信号类型
    QString startWord;                  // 起始字
    QString startBit;                   // 起始位
    QString lsb;                        // LSB
    QString msb;                        // MSB
    QString unitCode;                   // 单位代码
    double minValue;
    double maxValue;
    int signalSize;

    // 嵌套信号支持
    QVector<FcSignalData> nestedSignals; // 包含信号列表
    QMap<QString, FcSignalData*> signalIndex; // 信号标识符 -> 信号数据指针

    // 运行时数据
    QString currentValue;               // 当前值
    QDateTime lastUpdateTime;           // 最后更新时间
    bool isActive;                      // 是否活跃

    FcSignalData() : isActive(false) {}

    // 构建嵌套信号索引
    void buildSignalIndex() {
        signalIndex.clear();
        for (auto& signal : nestedSignals) {
            signalIndex[signal.signalIdentifier] = &signal;
            signal.buildSignalIndex(); // 递归构建
        }
    }
};

/**
 * @brief FC主题数据结构
 * 对应XML中的<主题>标签
 */
class FcTopicData {
public:
    QString topicId;                    // 主题ID
    QString topicName;                  // 主题名称
    QString topicIdentifier;            // 主题标识符
    QString topicType;                  // 主题类型（如APPLICATION_TOPIC）
    QString formatType;                 // 格式类型（如DATA_BLOCK）
    QString dataLength;                 // 数据长度
    QString lengthType;                 // 长度类型（如WORD）

    QVector<FcSignalData> signalList;   // 包含信号列表（避免与Qt的signals宏冲突）
    QMap<QString, FcSignalData*> signalIndex; // 信号标识符 -> 信号数据指针

    // 运行时数据
    QDateTime lastUpdateTime;           // 最后更新时间
    bool isActive;                      // 是否活跃

    FcTopicData() : isActive(false) {}

    // 构建信号索引
    void buildSignalIndex() {
        signalIndex.clear();
        for (auto& signal : signalList) {
            signalIndex[signal.signalIdentifier] = &signal;
            signal.buildSignalIndex(); // 递归构建嵌套信号索引
        }
    }
};

/**
 * @brief FC消息数据结构
 * 对应XML中的Msg标签
 */
class FcMessageData {
public:
    QString msgName;                    // Msg标签的msgName属性
    QString dstSrv;                     // 目标服务
    QString srcSrv;                     // 源服务
    QString m_interface;                // 接口
    QString srcUnit;                    // 源单元
    QString dstUnit;                    // 目标单元

    QVector<FcTopicData> topics;        // 该消息包含的主题列表

    // 用于快速查找的索引
    QMap<QString, FcTopicData*> topicIndex;  // 主题shortName -> 主题数据指针

    // 运行时数据
    int messageCount;                   // 消息计数
    QDateTime lastReceiveTime;          // 最后接收时间
    bool isMonitoring;                  // 是否正在监控

    FcMessageData() : messageCount(0), isMonitoring(false) {}

    // 构建主题索引
    void buildTopicIndex() {
        topicIndex.clear();
        for (auto& topic : topics) {
            topicIndex[topic.topicIdentifier] = &topic;
            topic.buildSignalIndex();
        }
    }

    // 获取消息的完整标识
    QString getFullIdentifier() const {
        return QString("%1_%2_%3").arg(srcSrv, dstSrv, m_interface);
    }
};

/**
 * @brief FC功能单元消息数据结构
 * 对应XML中的<功能单元消息>标签
 */
class FcUnitMessageData {
public:
    QString messageName;                // 消息名
    QString messageId;                  // 消息ID
    int sourceFuncId;               // 源功能ID
    QVector<QString> destFuncIds;       // 目的功能ID列表
    QString messageTopicName;           // 消息主题名称
    QString messageTopicIdentifier;     // 消息主题标识符
    int pubSubTopicId;              // 发布订阅主题ID
    FcTopicData topicData;

    // 运行时数据
    int messageCount;                   // 消息计数
    QDateTime lastReceiveTime;          // 最后接收时间
    bool isMonitoring;                  // 是否正在监控

    FcUnitMessageData() : messageCount(0), isMonitoring(false) {}
};

/**
 * @brief FC方案数据结构
 * 管理整个FC监控方案的数据，对应XML中的solution标签
 */
class FcSchemeData {
public:
    QString schemeName;                 // 方案名称（solution name属性）
    QString icdName;                    // ICD名称（solution ICD_name属性）
    bool isShared;                      // 是否共享（solution isShared属性）

    // 新的XML格式数据
    QVector<FcUnitMessageData> unitMessages;    // 功能单元消息列表
    QVector<FcTopicData> topics;                // 主题列表

    // 用于快速查找的索引
    QMap<QString, FcUnitMessageData*> unitMessageIndex;  // 消息ID -> 功能单元消息指针
    QMap<QString, FcTopicData*> topicIndex;              // 主题标识符 -> 主题数据指针
    QMap<QString, FcSignalData*> signalIndex;            // 信号标识符 -> 信号数据指针

    // TreeView项索引：用于TCP数据更新时快速定位TreeView中的信号项
    // key格式：sourceFuncId_pubSubTopicId_signalId
    // value：该信号对应的TreeView项列表（一个信号可能在多个地方显示）
    QMap<QString, QVector<QStandardItem*>> signalTreeItemIndex;

    // 统计信息
    int totalMessages;                  // 总消息数
    int activeMessages;                 // 活跃消息数
    QDateTime schemeStartTime;          // 方案启动时间

    FcSchemeData() : isShared(false), totalMessages(0), activeMessages(0) {}

    // 构建所有索引
    void buildIndices() {
        unitMessageIndex.clear();
        topicIndex.clear();
        signalIndex.clear();
        signalTreeItemIndex.clear();

        // 构建功能单元消息索引和主题索引
        for (auto& unitMessage : unitMessages) {
            unitMessageIndex[unitMessage.messageId] = &unitMessage;

            // 构建主题索引（从功能单元消息的topicData）
            if (!unitMessage.topicData.topicIdentifier.isEmpty()) {
                topicIndex[unitMessage.topicData.topicIdentifier] = &unitMessage.topicData;
                unitMessage.topicData.buildSignalIndex();

                // 构建全局信号索引
                buildSignalIndexRecursive(unitMessage.topicData.signalList);
            }
        }

        // 构建独立主题索引
        for (auto& topic : topics) {
            topicIndex[topic.topicIdentifier] = &topic;
            topic.buildSignalIndex();

            // 构建全局信号索引
            buildSignalIndexRecursive(topic.signalList);
        }

        totalMessages = unitMessages.size();
        updateActiveMessageCount();
    }

private:
    // 递归构建信号索引
    void buildSignalIndexRecursive(const QVector<FcSignalData>& signalList) {
        for (const auto& signal : signalList) {
            if (!signal.signalIdentifier.isEmpty()) {
                signalIndex[signal.signalIdentifier] = const_cast<FcSignalData*>(&signal);
            }
            // 递归处理嵌套信号
            buildSignalIndexRecursive(signal.nestedSignals);
        }
    }

public:

    // 更新活跃消息计数
    void updateActiveMessageCount() {
        activeMessages = 0;

        // 统计功能单元消息
        for (const auto& unitMessage : unitMessages) {
            if (unitMessage.isMonitoring) {
                activeMessages++;
            }
        }
    }

    // 根据消息标识符查找功能单元消息
    FcUnitMessageData* findUnitMessage(const QString& messageIdentifier) {
        return unitMessageIndex.value(messageIdentifier, nullptr);
    }

    // 根据主题标识符查找主题
    FcTopicData* findTopic(const QString& topicIdentifier) {
        return topicIndex.value(topicIdentifier, nullptr);
    }

    // 根据信号标识符查找信号
    FcSignalData* findSignal(const QString& signalIdentifier) {
        return signalIndex.value(signalIdentifier, nullptr);
    }

    // TreeView项索引管理方法

    /**
     * @brief 生成信号TreeView索引的key
     * @param sourceFuncId 源功能ID
     * @param pubSubTopicId 发布订阅主题ID
     * @param signalId 信号ID
     * @return 索引key字符串
     */
    QString generateSignalTreeKey(int sourceFuncId, int pubSubTopicId, const QString& signalId) {
        return QString("%1_%2_%3").arg(sourceFuncId).arg(pubSubTopicId).arg(signalId);
    }

    /**
     * @brief 添加信号TreeView项到索引
     * @param sourceFuncId 源功能ID
     * @param pubSubTopicId 发布订阅主题ID
     * @param signalId 信号ID
     * @param item TreeView项指针
     */
    void addSignalTreeItem(int sourceFuncId, int pubSubTopicId, const QString& signalId, QStandardItem* item) {
        QString key = generateSignalTreeKey(sourceFuncId, pubSubTopicId, signalId);
        signalTreeItemIndex[key].append(item);
    }

    /**
     * @brief 根据信号信息查找对应的TreeView项列表
     * @param sourceFuncId 源功能ID
     * @param pubSubTopicId 发布订阅主题ID
     * @param signalId 信号ID
     * @return TreeView项列表
     */
    QVector<QStandardItem*> findSignalTreeItems(int sourceFuncId, int pubSubTopicId, const QString& signalId) {
        QString key = generateSignalTreeKey(sourceFuncId, pubSubTopicId, signalId);
        return signalTreeItemIndex.value(key, QVector<QStandardItem*>());
    }

    /**
     * @brief 移除信号TreeView项从索引
     * @param sourceFuncId 源功能ID
     * @param pubSubTopicId 发布订阅主题ID
     * @param signalId 信号ID
     * @param item TreeView项指针（如果为nullptr，则移除该key下的所有项）
     */
    void removeSignalTreeItem(int sourceFuncId, int pubSubTopicId, const QString& signalId, QStandardItem* item = nullptr) {
        QString key = generateSignalTreeKey(sourceFuncId, pubSubTopicId, signalId);
        if (item == nullptr) {
            signalTreeItemIndex.remove(key);
        } else {
            signalTreeItemIndex[key].removeAll(item);
            if (signalTreeItemIndex[key].isEmpty()) {
                signalTreeItemIndex.remove(key);
            }
        }
    }

    /**
     * @brief 清空所有TreeView项索引
     */
    void clearSignalTreeItemIndex() {
        signalTreeItemIndex.clear();
    }
};

#endif // FC_DATA_STRUCTURES_H
