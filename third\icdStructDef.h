//
// Created by 合计飞 on 2025/3/20.
//

#ifndef PARSEANDANALYSIS_ICDSTRUCTDEF_H
#define PARSEANDANALYSIS_ICDSTRUCTDEF_H

#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <map>

#include "frameStruct.h"

enum LENGTH_TYPE {
    TYPE_WORD = 1,
    TYPE_BIT
};

enum SIGNAL_TYPE {
    TYPE_INT = 1,
    TYPE_DOUBLE,
    TYPE_CHAR,
    TYPE_GROUP
};

enum TR_TYPE {
    TYPE_EVENT = 1,
    TYPE_PERIOD
};

enum SERVICE_TYPE_TO_INTERFACE {
    AFFILIATED_SERVICE = 1,
    INVOKED_SERVICE
};

typedef struct {
    int id;
    std::string name;
    std::string shortName;
} BasicInfo;

//嵌套信号结构
typedef struct {
    std::string name;
    std::string shortName;
    int groupFlag;
    int startByte;
    int startBit;
} SubSignal;

//信号结构
typedef struct {
    int id;
    std::string name;
    std::string shortName;
    std::string sigType;
    int signalSize;
    bool isSigned;
    int signalArrayLength;
    std::string signalUnit;
    double minValue;
    double maxValue;
    std::string lsb;
    std::string msb;
    std::unordered_map<std::string , std::string> enumMap;
    std::vector<std::shared_ptr<SubSignal>> subSignalVec;
} Signal;

typedef struct IcdTopicSignal {
    std::string name;
    std::string shortName;
    bool signalGroupFlag;
    std::string signalUnit;
    std::string lsb;
    std::string msb;
    int startWord;
    int startBit;
    int serializeId;
    std::vector<std::shared_ptr<IcdTopicSignal>> subSignalVec;
} IcdTopicSignal;

typedef struct {
    int id;
    std::string name;
    std::string shortName;
    int parentId;           // 对应<归属上级通讯对象ID>
    std::string parentIdentifier; // 对应<归属上级通讯对象标识符>
} FuncUnit;

typedef struct {
    std::string msgName;
    int msgId;
    int sourceFuncId;
    std::vector<int> destFuncVec;
    std::string msgTopicName;
    std::string msgTopicShortName;
    int pubSubTopicId;
} FuncUnitMessage;


typedef struct {
    int id;
    std::string name;
    std::string shortName;
    int parentId;           // 对应<归属上级通讯对象ID>
    std::string parentIdentifier; // 对应<归属上级通讯对象标识符>

    std::vector<std::shared_ptr<FuncUnit>> funcUnitVec;
} FuncSubDomain;

// 新增域和子域结构体
typedef struct {
    int id;
    std::string name;
    std::string shortName;  // 对应XML中的<缩略名>

    std::vector<std::shared_ptr<FuncSubDomain>> funcSubDomainVec;
} FuncDomain;

typedef struct {
    int id;
    std::string name;
    std::string shortName;

    //功能单元消息
    std::vector<std::string> messageVec;
} FuncUnitInfo;

typedef struct {
    int id;
    std::string name;
    std::string shortName;

    std::vector<FuncUnitInfo> funcUnitVec;
} FuncSubDomainInfo;

// 新增域和子域结构体
typedef struct {
    int id;
    std::string name;
    std::string shortName;  // 对应XML中的<缩略名>

    std::vector<FuncSubDomainInfo > funcSubDomainVec;
} FuncDomainInfo;

typedef struct {
    int id;
    std::string name;
    std::string shortName;
    std::string topicType;
    std::string formatType;
    int dataLength;
    std::string lengthType;
    std::vector<std::shared_ptr<IcdTopicSignal>> signalVec;
} IcdTopic;

enum MuseMsgType {
    MSG_FILLER = 0,       //过滤消息
    MSG_METHOD_FUNC = 1,  //传递方法名
    MSG_EVENT = 2,        //传递类型：@binding,@syncevent,@query 不解析 不发送到kafka
    MSG_RESULT = 3,
    MSG_NOTIFY = 4,
    MSG_ERROR = 5,
};

enum SignalType {
    SIGNAL_TYPE_INT = 0,
    SIGNAL_TYPE_TINYINT,
    SIGNAL_TYPE_SMALLINT,
    SIGNAL_TYPE_BIGINT,
    SIGNAL_TYPE_FLOAT,
    SIGNAL_TYPE_DOUBLE,
    SIGNAL_TYPE_CHAR,
    SIGNAL_TYPE_STRING,
    SIGNAL_TYPE_BOOL,
//    SIGNAL_TYPE_ENUM,

    // ICD解析专用类型
    SIGNAL_TYPE_CUSTOM,
};

typedef struct {
    std::string id;
    std::string name;
    std::string shortName;
    std::string sigType;
    bool isSigned;
    int signalSize;
    std::string signalUnit;
    double minValue;
    double maxValue;
    std::string lsb;
    std::string msb;
    int startByte;
    int startBit;
} TopicSignal;

typedef struct {
    int id;
    std::string name;
    std::string shortName;
    std::string topicType;
    std::string formatType;
    int dataLength;
    std::string lengthType;
    std::vector<std::shared_ptr<TopicSignal>> signalVec;
} Topic;

typedef struct {
    unsigned char *buffer;
    int size;
    bool dataReverseFlag; // true-进行过数据翻转
} DataBuffer;

// 消息详情，包括主题结构
typedef struct {
    // 消息信息
    FuncUnitMessage messageInfo;
    // 主题结构
    Topic messageTopic;
} Message;

typedef struct {
    uint32_t source;  //源id
    uint32_t dest;  //目的id

    uint16_t callerId;  // caller_id:请求方id  dstId
    uint16_t serviceId;  //service_id 服务id  srcId

    int ID;
} CallerCalStruct;

//解析后的变长数据
typedef struct {
    SignalType m_signalType;  // 信号类型
    bool m_isSigned;     // 是否有符号
    bool enumFlag;
    union {
        char m_int8;        // 有符号字节整数
        uint8_t m_uint8;    // 无符号字节整数
        int16_t m_int16;    // 有符号短整数
        uint16_t m_uint16;    // 无符号短整数
        int32_t m_int32;    // 有符号整数
        uint32_t m_uint32;    // 无符号整数
        float m_float32;    // 单精度浮点数
        double m_float64;    // 双精度浮点数或日期时间
        int64_t m_int64;    // 有符号长整数，时间戳
        uint64_t m_uint64;    // 无符号长整数
        uint8_t *m_blob;    // 长度+值
        char *m_string;        // 长度+值
        uint8_t m_data[8];    // 二进制数据
    };
    uint32_t stringLen;            // 字符串数组长度
    std::string m_signalId;
    std::string m_signalShortName;

    void *m_orgDataPtr;        // 原始数据指针
    uint32_t m_orgDataLen;    // 原始数据长度,单位字节
} SignalData;

typedef struct {
    ZMQWrapHeader zmqWrapHeader;
    union {
        struct {
            FcFrameHeader frameHeader;
            AoxeHeader aoxeHeader;
        } headerFc;
        struct{
            I394AsmHeader asmHeader;
            I394AoxeHeader aoxeHeader;
        } header1394;
    };
} DataHeader;

typedef struct {
    DataHeader header;
    std::map<std::string, std::string> signalValueMap;
} ParsedData;

#endif //PARSEANDANALYSIS_ICDSTRUCTDEF_H
