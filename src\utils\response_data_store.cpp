#include "response_data_store.h"
#include <QMutexLocker>
#include <QDebug>

/**
 * @brief 获取单例实例
 */
ResponseDataStore& ResponseDataStore::getInstance()
{
    static ResponseDataStore instance;
    return instance;
}

/**
 * @brief 构造函数
 */
ResponseDataStore::ResponseDataStore() : QObject()
{
    qDebug() << "ResponseDataStore初始化";
}

/**
 * @brief 析构函数
 */
ResponseDataStore::~ResponseDataStore()
{
    qDebug() << "ResponseDataStore销毁";
    clearData();
}

/**
 * @brief 批量更新多个信号数据
 */
void ResponseDataStore::updateAllData(const QMap<QString, QVector<QString>>& dataMap)
{
    {
        QMutexLocker locker(&m_mutex);     
        // 检查是否有实际更新
        for (auto it = dataMap.constBegin(); it != dataMap.constEnd(); ++it) {
            if (!m_responseDataMap.contains(it.key()) || m_responseDataMap[it.key()][0] != it.value()[0]
                    || m_responseDataMap[it.key()][1] != it.value()[1]) {
                m_responseDataMap[it.key()] = it.value();
                updatedSignals.insert(it.key());
                dataChanged = true;
            }
        }
    }
}

void ResponseDataStore::updateAllData(const QString& topicVar)
{
    {
        QMutexLocker locker(&m_mutex);
        // 检查是否有实际更新
        for (auto it = m_responseDataMap.begin(); it != m_responseDataMap.end(); ++it) {
            if (it.key().startsWith(topicVar)) {
                for (QString& value : it.value()) {
                    value = "";
                }
                updatedSignals.insert(it.key());
                dataChanged = true;
            }
        }
    }
}

/**
 * @brief 清除所有数据
 */
void ResponseDataStore::clearData()
{
    QMutexLocker locker(&m_mutex);
    dataChanged = !m_responseDataMap.isEmpty();
    m_responseDataMap.clear();
    updatedSignals.clear();
    dataChanged = false;
}

// 一次获取所有数据
void ResponseDataStore::getAndClearResponseData(QMap<QString, QVector<QString>> &outDataMap,
                                     QSet<QString> &outUpdatedSignals,
                                     bool &outDataChanged)
{
    QMutexLocker locker(&m_mutex);

    std::swap(outDataMap, m_responseDataMap);
    std::swap(outUpdatedSignals, updatedSignals);
    outDataChanged = dataChanged;

    dataChanged = false;
}

bool ResponseDataStore::getCmdReuslt()
{
    return m_cmdReuslt;
}

bool ResponseDataStore::setCmdReuslt(bool result)
{
    m_cmdReuslt = result;
}

// 在ResponseDataStore.cpp中实现
QVector<QString> ResponseDataStore::getSignalData(const QString& treeViewKey) const
{
    QMutexLocker locker(&m_mutex);
    return m_responseDataMap.value(treeViewKey);
}

void ResponseDataStore::getTreeViewUpdates(QMap<QString, QVector<QString>>& outTreeViewDataMap,
                                          QSet<QString>& outUpdatedKeys,
                                          bool& outDataChanged)
{
    QMutexLocker locker(&m_mutex);

    // 只返回TreeView相关的数据（key格式：sourceFuncId_pubSubTopicId_signalId）
    for (auto it = m_responseDataMap.constBegin(); it != m_responseDataMap.constEnd(); ++it) {
        QString key = it.key();
        QStringList keyParts = key.split('_');

        // 检查key格式是否为TreeView索引格式（3个部分）
        if (keyParts.size() == 3) {
            bool ok1, ok2;
            keyParts[0].toInt(&ok1);  // sourceFuncId
            keyParts[1].toInt(&ok2);  // pubSubTopicId
            // keyParts[2] 是 signalId（字符串）

            if (ok1 && ok2) {
                outTreeViewDataMap[key] = it.value();
                if (updatedSignals.contains(key)) {
                    outUpdatedKeys.insert(key);
                }
            }
        }
    }

    outDataChanged = dataChanged;

    // 清理已处理的更新标志
    for (const QString& key : outUpdatedKeys) {
        updatedSignals.remove(key);
    }
    dataChanged = false;
}

