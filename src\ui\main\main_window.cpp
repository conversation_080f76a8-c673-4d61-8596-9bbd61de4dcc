﻿#include "main_window.h"
#include "ui_mainwindow.h"
#include "ui/document/document_window.h"
#include "ui/monitor/monitor_window.h"
#include "ui/user/user_management.h"
#include "ui/permission/permission_management.h"
#include "ui/unitAllocation/unitallocation.h"
#include "utils/user_session.h"
#include "utils/api_url_manager.h"
#include "utils/tcp_client.h"
#include "utils/tcp_data_processor.h"

// 包含命令处理器头文件
#include "utils/command_handlers/command_handler.h"
#include "utils/command_handlers/subscribe_handler.h"
#include "utils/command_handlers/unsubscribe_handler.h"
#include "utils/command_handlers/suspend_handler.h"
#include "utils/command_handlers/continue_handler.h"
#include "utils/command_handlers/response_handler.h"

#include <QButtonGroup>
#include <QMenu>
#include <QAction>
#include <QApplication>
#include <QStyle>
#include <QEvent>
#include <QMouseEvent>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QIcon>
#include <QSpacerItem>
#include <QSizePolicy>
#include <QLabel>
#include <QPixmap>
#include <QGraphicsDropShadowEffect>
#include <QColor>
#include <QMessageBox>
#include <QNetworkRequest>
#include <QJsonDocument>
#include <QJsonObject>
#include <QNetworkReply>
#include <QTimer>
#include <QDebug>
#include <QThread>
#include <QSettings>
#include <QStandardPaths>
#include <QDir>

#include "ui/FcMonitor/fc_monitor_widget.h"
#include "real_time_buttons.h"

MainWindow::MainWindow(const QString &username, QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , monitorWindow(new MonitorWindow(this))
    , contentStack(new QStackedWidget(this))
    , documentWindow(new DocumentWindow(this))
    , userManagementWindow(new UserManagement(this))
    , unitAllocationWindow(new unitAllocation(this))
    , permissionManagementWindow(new PermissionManagement(this))
    , schemeManagementWindow(new FcMonitorWidget(this))
    , m_username(username)
    , currentPageIndex(0)
    , networkManager(new QNetworkAccessManager(this))
    , m_dataProcessorThread(nullptr)
    //, m_fcMonitorWindows(new FcMonitorWidget(this))
    , valueMonitorButton(nullptr)
    , graphMonitorButton(nullptr)
    , timeSeriesButton(nullptr)
    , dataLoadButton(nullptr)
    , unitConfigButton(nullptr)
    , monitorButtonGroup(nullptr)
    , mainButtonGroup(nullptr)
    , replayValueMonitorButton(nullptr)
    , replayGraphMonitorButton(nullptr)
    , replayTimeSeriesButton(nullptr)
    , replayDataLoadButton(nullptr)
    , replayUnitConfigButton(nullptr)
    , replayButtonGroup(nullptr)
{
    ui->setupUi(this);

    // 设置无边框窗口
    setWindowFlags(Qt::FramelessWindowHint);

    setupUI();
    setupCustomTitleBar();
    setupConnections();
    setupSideMenu();
    createSubMenus();
    initMonitorButtons();  // 调用初始化监控按钮的函数

    // 默认显示监控页面
    loadPage(0);

    // 初始化并启动TCP客户端和数据处理线程
    initTcpClientAndProcessor();

    // 建立实时链接
    connect(monitorWindow, &MonitorWindow::realtimeButtonClicked, this, &MainWindow::onSchemeManagementTriggered);
    
    // 建立回放链接
    connect(monitorWindow, &MonitorWindow::replayButtonClicked, this, &MainWindow::onReplayManagementTriggered);
}

MainWindow::~MainWindow()
{
    // 清理资源
    if (m_dataProcessorThread) {
        // 停止TCP连接
        TcpClient::getInstance().disconnectFromServer();
        // 退出并等待数据处理线程结束
        m_dataProcessorThread->quit();
        m_dataProcessorThread->wait();
    }

    delete ui;
}

bool MainWindow::eventFilter(QObject *watched, QEvent *event)
{
    static QPoint lastPos;

    if (watched == ui->topBar && event->type() == QEvent::MouseButtonPress) {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent*>(event);
        if (mouseEvent->button() == Qt::LeftButton) {
            lastPos = mouseEvent->globalPos();
            return true;
        }
    }
    else if (watched == ui->topBar && event->type() == QEvent::MouseMove) {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent*>(event);
        if (mouseEvent->buttons() & Qt::LeftButton) {
            move(pos() + mouseEvent->globalPos() - lastPos);
            lastPos = mouseEvent->globalPos();
            return true;
        }
    }

    return QMainWindow::eventFilter(watched, event);
}

void MainWindow::setupUI()
{
    // 设置窗口标题
    setWindowTitle("微服务监控系统");

    // 设置窗口最大化
    showMaximized();

    // 获取UI中的控件
    overviewButton = ui->overviewButton;
    documentButton = ui->documentButton;
    managementButton = ui->managementButton;
    homeButton = ui->homeButton;

    usernameLabel = ui->usernameLabel;

    // 隐藏顶部区域中的用户名标签，因为将在标题栏中显示
    usernameLabel->setVisible(false);

    // 设置内容区域
    ui->contentLayout->addWidget(contentStack);

    // 添加页面到堆栈
    contentStack->addWidget(monitorWindow);
    contentStack->addWidget(documentWindow);
    contentStack->addWidget(userManagementWindow);
    contentStack->addWidget(unitAllocationWindow);
    contentStack->addWidget(permissionManagementWindow);
    contentStack->addWidget(schemeManagementWindow);
    //contentStack->addWidget(m_fcMonitorWindows);

    // 创建按钮组，确保只有一个按钮被选中
    mainButtonGroup = new QButtonGroup(this);
    mainButtonGroup->addButton(overviewButton);
    mainButtonGroup->addButton(documentButton);
    mainButtonGroup->addButton(managementButton);
    mainButtonGroup->addButton(homeButton);

    // 为顶部栏添加事件过滤器，用于实现窗口拖动
    ui->topBar->installEventFilter(this);

    // 调整topBar的高度，使其变小
    ui->topBar->setFixedHeight(40); // 将高度从默认的50px减小到40px

    // 增强topBar的立体感
    ui->topBar->setStyleSheet(
        "QWidget#topBar {"
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                              stop:0 #2196F3, stop:1 #1976D2);"
        "   border-bottom: 1px solid #1565C0;"
        "   box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);"
        "}"
        "QLabel {"
        "   color: white;"
        "   font-weight: bold;"
        "}"
        );

    // 增强contentArea的立体感
    ui->contentArea->setStyleSheet(
        "QWidget#contentArea {"
        "   background-color: white;"
        "   border-radius: 6px;"
        "   margin: 10px;"
        "   border: 1px solid #E0E0E0;"
        "}"
        );

    // 将侧边栏按钮整体下移
    QVBoxLayout* sideMenuLayout = qobject_cast<QVBoxLayout*>(ui->sideMenu->layout());
    if (sideMenuLayout) {
        // 在顶部添加一个空白区域，使按钮下移
        QSpacerItem* topSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Fixed);
        sideMenuLayout->insertItem(0, topSpacer);

        // 调整按钮之间的间距
        sideMenuLayout->setSpacing(10);
    }

    // 为侧边栏和内容区域添加阴影效果
    QGraphicsDropShadowEffect *sideMenuShadow = new QGraphicsDropShadowEffect(this);
    sideMenuShadow->setBlurRadius(15);
    sideMenuShadow->setColor(QColor(0, 0, 0, 80));
    sideMenuShadow->setOffset(2, 0);
    ui->sideMenu->setGraphicsEffect(sideMenuShadow);

    QGraphicsDropShadowEffect *contentShadow = new QGraphicsDropShadowEffect(this);
    contentShadow->setBlurRadius(10);
    contentShadow->setColor(QColor(0, 0, 0, 40));
    contentShadow->setOffset(0, 2);
    ui->contentArea->setGraphicsEffect(contentShadow);
}

void MainWindow::setupCustomTitleBar()
{
    // 创建窗口控制按钮
    QPushButton *minimizeButton = new QPushButton(this);
    QPushButton *maximizeButton = new QPushButton(this);
    QPushButton *closeButton = new QPushButton(this);

    // 设置按钮样式 - 减小按钮尺寸以适应更小的topBar
    minimizeButton->setFixedSize(25, 25);
    maximizeButton->setFixedSize(25, 25);
    closeButton->setFixedSize(25, 25);

    // 增强按钮的立体感
    minimizeButton->setStyleSheet(
        "QPushButton { "
        "   border: none; "
        "   background-color: transparent; "
        "   color: white; "
        "   border-radius: 3px; "
        "} "
        "QPushButton:hover { "
        "   background-color: rgba(255, 255, 255, 0.2); "
        "   border: 1px solid rgba(255, 255, 255, 0.3); "
        "}");

    maximizeButton->setStyleSheet(
        "QPushButton { "
        "   border: none; "
        "   background-color: transparent; "
        "   color: white; "
        "   border-radius: 3px; "
        "} "
        "QPushButton:hover { "
        "   background-color: rgba(255, 255, 255, 0.2); "
        "   border: 1px solid rgba(255, 255, 255, 0.3); "
        "}");

    closeButton->setStyleSheet(
        "QPushButton { "
        "   border: none; "
        "   background-color: transparent; "
        "   color: white; "
        "   border-radius: 3px; "
        "} "
        "QPushButton:hover { "
        "   background-color: #e81123; "
        "   border: 1px solid rgba(255, 255, 255, 0.3); "
        "}");

    // 设置按钮图标
    minimizeButton->setText("—");
    maximizeButton->setText("□");
    closeButton->setText("✕");

    // 创建用户名按钮，使用传入的用户名
    titleBarUsernameLabel = new QPushButton(m_username, this);
    titleBarUsernameLabel->setStyleSheet(
        "QPushButton { "
        "   color: white; "
        "   font-size: 13px; "
        "   padding: 0 10px; "
        "   background-color: rgba(255, 255, 255, 0.1); "
        "   border-radius: 3px; "
        "   border: none; "
        "   text-align: center; "
        "} "
        "QPushButton:hover { "
        "   background-color: rgba(255, 255, 255, 0.2); "
        "}");

    // 根据用户名长度动态调整宽度
    QFontMetrics fm(titleBarUsernameLabel->font());
    int textWidth = fm.horizontalAdvance(m_username);
    titleBarUsernameLabel->setFixedWidth(textWidth + 30); // 添加一些额外的空间

    // 设置上下文菜单策略，允许右键菜单
    titleBarUsernameLabel->setContextMenuPolicy(Qt::CustomContextMenu);

    // 创建用户菜单
    userMenu = new QMenu(this);
    QAction *logoutAction = userMenu->addAction("登出");

    // 连接右键菜单信号
    connect(titleBarUsernameLabel, &QPushButton::customContextMenuRequested, [this](const QPoint &pos) {
        userMenu->exec(titleBarUsernameLabel->mapToGlobal(pos));
    });

    // 连接登出动作
    connect(logoutAction, &QAction::triggered, this, &MainWindow::onLogoutTriggered);

    // 创建垂直分隔线
    QFrame *verticalLine = new QFrame(this);
    verticalLine->setFrameShape(QFrame::VLine);
    verticalLine->setFrameShadow(QFrame::Sunken);
    verticalLine->setFixedHeight(25); // 减小分隔线高度
    verticalLine->setStyleSheet(
        "background-color: rgba(255, 255, 255, 0.5); "
        "margin: 0 10px; "
        "border: none;"); // 增强立体感

    // 添加按钮到顶部栏
    QHBoxLayout *topBarLayout = qobject_cast<QHBoxLayout*>(ui->topBar->layout());
    if (topBarLayout) {
        // 添加用户名按钮
        topBarLayout->addWidget(titleBarUsernameLabel);

        // 添加垂直分隔线
        topBarLayout->addWidget(verticalLine);

        // 添加窗口控制按钮
        topBarLayout->addWidget(minimizeButton);
        topBarLayout->addWidget(maximizeButton);
        topBarLayout->addWidget(closeButton);

        // 设置布局间距
        topBarLayout->setSpacing(5);
        topBarLayout->setContentsMargins(10, 0, 10, 0);
    }

    // 连接按钮信号
    connect(minimizeButton, &QPushButton::clicked, this, &MainWindow::showMinimized);
    connect(maximizeButton, &QPushButton::clicked, [this]() {
        if (isMaximized()) {
            showNormal();
        } else {
            showMaximized();
        }
    });
    connect(closeButton, &QPushButton::clicked, this, &MainWindow::close);
}

void MainWindow::setupConnections()
{
    connect(overviewButton, &QPushButton::clicked, this, &MainWindow::onOverviewButtonClicked);
    connect(documentButton, &QPushButton::clicked, this, &MainWindow::onDocumentButtonClicked);
    connect(managementButton, &QPushButton::clicked, this, &MainWindow::onManagementButtonClicked);
    connect(homeButton, &QPushButton::clicked, this, &MainWindow::onHomeButtonClicked);
}

void MainWindow::setupSideMenu()
{
    // 使用图标
    homeButton->setIcon(QIcon(":/images/backHome.png"));

    // 设置图标大小
    homeButton->setIconSize(QSize(32, 32));

    // 设置文字
    homeButton->setText("");
    overviewButton->setText("主机总览");
    documentButton->setText("健康管理");
    managementButton->setText("管理中心");

    // 设置按钮样式 - 使用自定义样式实现图标在上、文字在下
    QString commonStyle =
        "border: none;"
        "color: white;"
        "font-size: 11px;"
        "min-height: 60px;"
        "max-height: 60px;"
        "padding-top: 55px;"  // 进一步增加顶部内边距，避免图片与文字重叠
        "text-align: center;"
        "border-radius: 4px;"  // 添加圆角
        "margin: 2px 5px;";    // 添加边距，增强立体感

    QString overviewStyle = commonStyle +
                            QString("background-image: url(:/images/hostOverview.png);"
                                    "background-position: center 5px;"  // 调整图片位置更靠上
                                    "background-repeat: no-repeat;"
                                    "background-size: 32px 32px;");  // 明确设置背景图片大小

    QString documentStyle = commonStyle +
                            QString("background-image: url(:/images/healthManage.png);"
                                    "background-position: center 5px;"
                                    "background-repeat: no-repeat;"
                                    "background-size: 32px 32px;");

    QString managementStyle = commonStyle +
                              QString("background-image: url(:/images/manageCenter.png);"
                                      "background-position: center 5px;"
                                      "background-repeat: no-repeat;"
                                      "background-size: 32px 32px;");

    // 设置homeButton样式
    QString homeStyle =
        "border: none;"
        "min-height: 60px;"
        "max-height: 60px;"
        "border-radius: 4px;"  // 添加圆角
        "margin: 2px 5px;";    // 添加边距，增强立体感

    // 应用样式
    overviewButton->setIcon(QIcon()); // 清除图标，使用背景图片代替
    documentButton->setIcon(QIcon());
    managementButton->setIcon(QIcon());

    overviewButton->setStyleSheet(overviewStyle);
    documentButton->setStyleSheet(documentStyle);
    managementButton->setStyleSheet(managementStyle);
    homeButton->setStyleSheet(homeStyle);

    // 设置侧边栏样式 - 增强立体感
    QString sideMenuStyle =
        "#sideMenu {"
        "   background: qlineargradient(x1:0, y1:0, x2:1, y2:0, "
        "                              stop:0 #1E88E5, stop:1 #1976D2);"
        "   min-width: 80px;"
        "   max-width: 80px;"
        "   border-right: 1px solid #1565C0;"
        "}"
        "#sideMenu QPushButton:hover {"
        "   background-color: rgba(255, 255, 255, 0.15);"
        "   border: 1px solid rgba(255, 255, 255, 0.3);"
        "}"
        "#sideMenu QPushButton:checked {"
        "   background-color: rgba(0, 0, 0, 0.2);"
        "   border: 1px solid rgba(255, 255, 255, 0.5);"
        "   box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.2);"
        "}";

    ui->sideMenu->setStyleSheet(sideMenuStyle);

    // 确保按钮整体下移
    QVBoxLayout* sideMenuLayout = qobject_cast<QVBoxLayout*>(ui->sideMenu->layout());
    if (sideMenuLayout) {
        // 清除现有的顶部间距项
        for (int i = 0; i < sideMenuLayout->count(); i++) {
            QLayoutItem* item = sideMenuLayout->itemAt(i);
            if (item->spacerItem() && i == 0) {
                sideMenuLayout->removeItem(item);
                delete item;
                break;
            }
        }

        // 添加新的顶部间距
        QSpacerItem* topSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Fixed);
        sideMenuLayout->insertItem(0, topSpacer);

        // 调整按钮之间的间距
        sideMenuLayout->setSpacing(10);
    }

    // 默认选中主机总览按钮
    overviewButton->setChecked(true);
}

void MainWindow::createSubMenus()
{
    // 创建主机总览子菜单
    overviewMenu = new QMenu(this);
    QAction *valueMonitorAction = overviewMenu->addAction("数值监控");
    QAction *graphMonitorAction = overviewMenu->addAction("图形监控");
    QAction *unitConfigAction = overviewMenu->addAction("单位配置");

    // 连接子菜单动作
    connect(valueMonitorAction, &QAction::triggered, this, &MainWindow::onSchemeManagementTriggered);
    connect(graphMonitorAction, &QAction::triggered, this, &MainWindow::onGraphMonitorTriggered);
    connect(unitConfigAction, &QAction::triggered, this, &MainWindow::onUnitConfigTriggered);

    // 创建管理中心子菜单
    managementMenu = new QMenu(this);
    QAction *userManageAction = managementMenu->addAction("用户管理");
    QAction *permissionManageAction = managementMenu->addAction("权限管理");

    // 连接管理中心子菜单
    connect(userManageAction, &QAction::triggered, this, &MainWindow::onUserManagementTriggered);
    connect(permissionManageAction, &QAction::triggered, this, &MainWindow::onPermissionManagementTriggered);

    // 设置按钮的上下文菜单
    overviewButton->setContextMenuPolicy(Qt::CustomContextMenu);
    managementButton->setContextMenuPolicy(Qt::CustomContextMenu);

    // 连接自定义上下文菜单信号
    connect(overviewButton, &QPushButton::customContextMenuRequested, [this](const QPoint &pos) {
        overviewMenu->exec(overviewButton->mapToGlobal(pos));
    });

    connect(managementButton, &QPushButton::customContextMenuRequested, [this](const QPoint &pos) {
        managementMenu->exec(managementButton->mapToGlobal(pos));
    });
}

void MainWindow::loadPage(int index)
{
    currentPageIndex = index;
    contentStack->setCurrentIndex(index);

    // 更新按钮选中状态
    switch (index) {
    case 0: // 监控页面
        overviewButton->setChecked(true);
        homeButton->setChecked(false);
        // 尝试从服务器获取文件列表并更新监控窗口
        break;
    case 1: // 文档页面
        documentButton->setChecked(true);
        break;
    case 2: // 用户管理页面
        userManagementWindow->setVisible(true);
        overviewButton->setChecked(false);
        documentButton->setChecked(false);
        managementButton->setChecked(true);
        break;
    case 3: // 单位配置页面
        unitAllocationWindow->setVisible(true);
        overviewButton->setChecked(false);
        documentButton->setChecked(false);
        managementButton->setChecked(true);
        break;
    case 4: // 权限管理页面
        permissionManagementWindow->setVisible(true);
        overviewButton->setChecked(false);
        documentButton->setChecked(false);
        managementButton->setChecked(true);
        break;
    case 5: // 方案管理页面
        schemeManagementWindow->setVisible(true);
        overviewButton->setChecked(false);
        documentButton->setChecked(false);
        managementButton->setChecked(true);
        break;
        // 可以添加更多页面
    }
}

void MainWindow::onOverviewButtonClicked()
{
    loadPage(0);
}

void MainWindow::onDocumentButtonClicked()
{
    loadPage(1);
}

void MainWindow::onManagementButtonClicked()
{
    // 直接显示用户管理页面
    onUserManagementTriggered();
}

void MainWindow::onHomeButtonClicked()
{
    // 返回主页，显示监控页面
    updateSidePanel(true);
    loadPage(0);
    monitorWindow->setMonitorType(MonitorWindow::VALUE_MONITOR);
    homeButton->setChecked(false);
    overviewButton->setChecked(true);
    documentButton->setChecked(false);
    managementButton->setChecked(false);
    QTimer::singleShot(0, [this]() {
        homeButton->setAutoExclusive(false);
        homeButton->setChecked(false);
        homeButton->setAutoExclusive(true);
    });
}

void MainWindow::onValueMonitorTriggered(bool isReviewMode)
{
    if (!isReviewMode) {
        // 设置标题
        ui->titleLabel->setText("方案管理");
        // 显示方案管理页面
        contentStack->setCurrentWidget(schemeManagementWindow);

    }
}

void MainWindow::onGraphMonitorTriggered()
{
    // 设置标题
    ui->titleLabel->setText("图形监控");

    // 更新按钮选中状态
    overviewButton->setChecked(true);
    documentButton->setChecked(false);
    managementButton->setChecked(false);
}

void MainWindow::onUnitConfigTriggered()
{
    // 设置标题
    ui->titleLabel->setText("单位配置");

    // 显示用户管理页面
    contentStack->setCurrentWidget(unitAllocationWindow);

    // 更新按钮选中状态
    overviewButton->setChecked(true);
    documentButton->setChecked(false);
    managementButton->setChecked(false);
}

void MainWindow::onUserManagementTriggered()
{
    // 设置标题
    ui->titleLabel->setText("用户管理");

    // 显示用户管理页面
    contentStack->setCurrentWidget(userManagementWindow);

    // 更新按钮选中状态
    overviewButton->setChecked(false);
    documentButton->setChecked(false);
    managementButton->setChecked(true);
}

// 添加权限管理功能的处理函数
void MainWindow::onPermissionManagementTriggered()
{
    // 设置标题
    ui->titleLabel->setText("权限管理");

    // 显示权限管理页面
    contentStack->setCurrentWidget(permissionManagementWindow);

    // 更新按钮选中状态
    overviewButton->setChecked(false);
    documentButton->setChecked(false);
    managementButton->setChecked(true);
}

void MainWindow::updateSidePanel(bool isMain)
{
    static bool mainOrOther = true;

    if (mainOrOther == isMain) {
        return;
    }
    // 获取侧边栏布局
    QVBoxLayout* sideMenuLayout = qobject_cast<QVBoxLayout*>(ui->sideMenu->layout());


    if (!sideMenuLayout) {
        return;
    }
    if (!isMain) {
        // 清除现有按钮（除了基础按钮）
        QLayoutItem* item;
        while ((item = sideMenuLayout->takeAt(0)) != nullptr) {
            QWidget* widget = item->widget();
            if (widget && widget != homeButton && widget != overviewButton &&
                widget != documentButton && widget != managementButton) {
                delete widget;
            }
            delete item;
        }

        // 删除旧的按钮组
        if (mainButtonGroup) {
            delete mainButtonGroup;
            mainButtonGroup = nullptr;
        }
        if (monitorButtonGroup) {
            delete monitorButtonGroup;
            monitorButtonGroup = nullptr;
        }
        if (replayButtonGroup) {
            delete replayButtonGroup;
            replayButtonGroup = nullptr;
        }

        // 显示主页按钮
        homeButton->setVisible(true);
        sideMenuLayout->addWidget(homeButton);

        // 根据当前模式显示对应的按钮组
        if (m_isReviewMode) {
            // 回放模式：创建并显示回放按钮组
            if (replayButtonGroup) {
                delete replayButtonGroup;
                replayButtonGroup = nullptr;
            }
            replayButtonGroup = new QButtonGroup(this);
            replayButtonGroup->addButton(replayValueMonitorButton);
            replayButtonGroup->addButton(replayGraphMonitorButton);
            replayButtonGroup->addButton(replayTimeSeriesButton);
            replayButtonGroup->addButton(replayDataLoadButton);
            replayButtonGroup->addButton(replayUnitConfigButton);

            replayValueMonitorButton->setVisible(true);
            sideMenuLayout->addWidget(replayValueMonitorButton);

            replayGraphMonitorButton->setVisible(true);
            sideMenuLayout->addWidget(replayGraphMonitorButton);

            replayTimeSeriesButton->setVisible(true);
            sideMenuLayout->addWidget(replayTimeSeriesButton);

            replayDataLoadButton->setVisible(true);
            sideMenuLayout->addWidget(replayDataLoadButton);

            replayUnitConfigButton->setVisible(true);
            sideMenuLayout->addWidget(replayUnitConfigButton);
            
            // 隐藏实时按钮
            valueMonitorButton->setVisible(false);
            graphMonitorButton->setVisible(false);
            timeSeriesButton->setVisible(false);
            dataLoadButton->setVisible(false);
            unitConfigButton->setVisible(false);
        } else {
            // 实时模式：创建并显示实时按钮组
            if (monitorButtonGroup) {
                delete monitorButtonGroup;
            }
            monitorButtonGroup = new QButtonGroup(this);
            monitorButtonGroup->addButton(valueMonitorButton);
            monitorButtonGroup->addButton(graphMonitorButton);
            monitorButtonGroup->addButton(timeSeriesButton);
            monitorButtonGroup->addButton(dataLoadButton);
            monitorButtonGroup->addButton(unitConfigButton);

            valueMonitorButton->setVisible(true);
            sideMenuLayout->addWidget(valueMonitorButton);

            graphMonitorButton->setVisible(true);
            sideMenuLayout->addWidget(graphMonitorButton);

            timeSeriesButton->setVisible(true);
            sideMenuLayout->addWidget(timeSeriesButton);

            dataLoadButton->setVisible(true);
            sideMenuLayout->addWidget(dataLoadButton);

            unitConfigButton->setVisible(true);
            sideMenuLayout->addWidget(unitConfigButton);
            
            // 隐藏回放按钮
            replayValueMonitorButton->setVisible(false);
            replayGraphMonitorButton->setVisible(false);
            replayTimeSeriesButton->setVisible(false);
            replayDataLoadButton->setVisible(false);
            replayUnitConfigButton->setVisible(false);
        }

        // 添加弹性空间
        sideMenuLayout->addStretch();

        m_isReviewMode = false;

        overviewButton->setVisible(false);
        documentButton->setVisible(false);
        managementButton->setVisible(false);
        mainOrOther = false;
    }
    else {
        // 清除现有按钮（除了基础按钮）
        QLayoutItem* item;
        while ((item = sideMenuLayout->takeAt(0)) != nullptr) {
            QWidget* widget = item->widget();
            if (widget && widget != homeButton && widget != valueMonitorButton && widget != graphMonitorButton &&
                widget != timeSeriesButton &&  widget != dataLoadButton &&  widget != unitConfigButton &&
                widget != replayValueMonitorButton && widget != replayGraphMonitorButton &&
                widget != replayTimeSeriesButton &&  widget != replayDataLoadButton &&  widget != replayUnitConfigButton) {
                delete widget;
            }
            delete item;
        }

        // 删除旧的按钮组
        if (monitorButtonGroup) {
            delete monitorButtonGroup;
            monitorButtonGroup = nullptr;
        }
        if (mainButtonGroup) {
            delete mainButtonGroup;
            mainButtonGroup = nullptr;
        }

        mainButtonGroup = new QButtonGroup(this);
        mainButtonGroup->addButton(homeButton);
        mainButtonGroup->addButton(overviewButton);
        mainButtonGroup->addButton(documentButton);
        mainButtonGroup->addButton(managementButton);
        // 显示监控按钮
        homeButton->setVisible(true);
        sideMenuLayout->addWidget(homeButton);

        overviewButton->setVisible(true);
        sideMenuLayout->addWidget(overviewButton);

        documentButton->setVisible(true);
        sideMenuLayout->addWidget(documentButton);

        managementButton->setVisible(true);
        sideMenuLayout->addWidget(managementButton);
        // 添加弹性空间
        sideMenuLayout->addStretch();
        m_isReviewMode = true;

        // 隐藏所有监控按钮
        valueMonitorButton->setVisible(false);
        graphMonitorButton->setVisible(false);
        timeSeriesButton->setVisible(false);
        dataLoadButton->setVisible(false);
        unitConfigButton->setVisible(false);
        
        replayValueMonitorButton->setVisible(false);
        replayGraphMonitorButton->setVisible(false);
        replayTimeSeriesButton->setVisible(false);
        replayDataLoadButton->setVisible(false);
        replayUnitConfigButton->setVisible(false);
        mainOrOther = true;
    }


    // 清除现有的顶部间距项
    for (int i = 0; i < sideMenuLayout->count(); i++) {
        QLayoutItem* item = sideMenuLayout->itemAt(i);
        if (item->spacerItem() && i == 0) {
            sideMenuLayout->removeItem(item);
            delete item;
            break;
        }
    }

    // 添加新的顶部间距
    QSpacerItem* topSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Fixed);
    sideMenuLayout->insertItem(0, topSpacer);

    // 调整按钮之间的间距
    sideMenuLayout->setSpacing(10);
}

void MainWindow::onSchemeManagementTriggered()
{
    // 设置实时模式标志
    m_isReviewMode = false;

    // 设置标题
    ui->titleLabel->setText("方案管理");

    // 显示方案管理页面
    contentStack->setCurrentWidget(schemeManagementWindow);


    updateSidePanel(false);

    // 默认选中实时数值监控按钮
    if (valueMonitorButton) {
        valueMonitorButton->setChecked(true);
    }
}

void MainWindow::onReplayManagementTriggered(const QDateTime& startTime, const QDateTime& endTime)
{
    // 设置回放模式标志
    m_isReviewMode = true;
    
    // 设置标题为回放模式
    ui->titleLabel->setText(QString("回放监控 - %1 至 %2")
                           .arg(startTime.toString("yyyy-MM-dd HH:mm"))
                           .arg(endTime.toString("yyyy-MM-dd HH:mm")));

    // 显示方案管理页面（回放模式下也使用方案管理界面）
    contentStack->setCurrentWidget(schemeManagementWindow);


    // 更新侧边栏，进入监控模式
    updateSidePanel(false);

    // 默认选中回放数值监控按钮
    if (replayValueMonitorButton) {
        replayValueMonitorButton->setChecked(true);
    }

    // 这里可以添加回放模式特有的逻辑
    // 例如：设置回放时间范围、加载历史数据等
    qDebug() << "进入回放模式，时间范围:" << startTime << "至" << endTime;
}

void MainWindow::onLogoutTriggered()
{
    // 显示正在登出的提示
    QMessageBox msgBox;
    msgBox.setWindowTitle(tr("正在登出"));
    msgBox.setText(tr("正在退出登录，请稍候..."));
    msgBox.setStandardButtons(QMessageBox::NoButton);
    msgBox.setIcon(QMessageBox::Information);

    // 设置消息框为模态，但只显示一小段时间
    QTimer::singleShot(500, &msgBox, &QMessageBox::close);
    msgBox.show();

    // 获取用户会话数据
    UserSession& session = UserSession::getInstance();
    QString token = session.getToken();

    // 准备网络请求
    QNetworkRequest request(ApiUrlManager::getInstance().getUrl(ApiUrlManager::USER_LOGOUT));
    request.setRawHeader("accept", "*/*");
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    // 如果有token，添加到请求头中
    if (!token.isEmpty()) {
        request.setRawHeader("Authorization", token.toUtf8());
    }

    qDebug() << "Sending logout request for user:" << session.getUsername();

    // 连接响应信号
    connect(networkManager, &QNetworkAccessManager::finished, this, &MainWindow::onLogoutReplyFinished);

    // 发送POST请求
    networkManager->post(request, QByteArray());
}

void MainWindow::onLogoutReplyFinished(QNetworkReply *reply)
{
    // 断开信号连接，避免重复响应
    disconnect(networkManager, &QNetworkAccessManager::finished, this, &MainWindow::onLogoutReplyFinished);

    // 处理响应
    if (reply->error() == QNetworkReply::NoError) {
        // 读取响应数据
        QByteArray responseData = reply->readAll();

        // 尝试解析 JSON 响应
        QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData);

        if (!jsonDoc.isNull() && jsonDoc.isObject()) {
            QJsonObject responseObj = jsonDoc.object();

            // 获取响应字段
            QString code = responseObj["code"].toString();
            bool success = responseObj["success"].toBool();
            QString message = responseObj["message"].toString();

            if (success) {
                // 停止TCP客户端服务

                // 登出成功
                QMessageBox::information(this, tr("登出成功"), tr("您已成功退出登录"));

                // 释放响应对象
                reply->deleteLater();

                // 发送登出成功信号，通知应用程序显示登录界面
                qDebug() << "发送登出成功信号";
                emit logoutSuccess();

                // 关闭主窗口 - 这会导致应用程序返回到main函数的事件循环
                close();

                return;
            } else {
                // 登出失败，显示后台返回的错误信息
                QMessageBox::warning(this, tr("登出失败"), tr("登出失败: %1").arg(message));
            }
        } else {
            // JSON解析失败
            QMessageBox::warning(this, tr("登出失败"), tr("服务器响应格式错误"));
        }
    } else {
        // 网络请求失败
        QMessageBox::warning(this, tr("登出失败"),
                             tr("无法连接到服务器: %1").arg(reply->errorString()));
    }

    // 释放响应对象
    reply->deleteLater();
}

void MainWindow::initTcpClientAndProcessor()
{
    qDebug() << "初始化TCP客户端和数据处理线程...";

    // 获取TCP客户端单例
    TcpClient& tcpClient = TcpClient::getInstance();

    // 从配置文件读取TCP服务器设置
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QString configFile = QDir(configPath).filePath("MicroserviceMonitor.conf");
    QSettings settings(configFile, QSettings::IniFormat);

    // 读取TCP服务器配置
    QString serverIp = settings.value("TCP/serverIp", "127.0.0.1").toString();
    quint16 serverPort = settings.value("TCP/serverPort", 8080).toUInt();

    // 设置服务器IP和端口
    tcpClient.setServerIp(serverIp);
    tcpClient.setServerPort(serverPort);

    // 创建并启动数据处理线程
    m_dataProcessorThread = new QThread(this);
    m_dataProcessorThread->setObjectName("DataProcessorThread");
    m_dataProcessorThread->start();

    // 获取数据处理器单例实例并将其移动到新线程
    TcpDataProcessor* processor = TcpDataProcessor::getInstance();
    processor->setParent(nullptr); // 必须移除父对象才能移动到线程
    processor->moveToThread(m_dataProcessorThread);

    // 创建并注册各种命令处理器
    // 1. 订阅命令处理器
    // SubscribeCommandHandler* subscribeHandler = new SubscribeCommandHandler();
    // processor->registerCommandHandler(CMD_SUBSCRIBE, subscribeHandler);
    // // 2. 取消订阅命令处理器
    // UnsubscribeCommandHandler* unsubscribeHandler = new UnsubscribeCommandHandler();
    // processor->registerCommandHandler(CMD_UNSUBSCRIBE, unsubscribeHandler);
    // // 3. 暂停命令处理器
    // SuspendCommandHandler* suspendHandler = new SuspendCommandHandler();
    // processor->registerCommandHandler(CMD_SUSPEND, suspendHandler);
    // 4. 继续命令处理器
    ContinueCommandHandler* continueHandler = new ContinueCommandHandler();
    processor->registerCommandHandler(CMD_RESPONSE, continueHandler);
    // 5. 响应命令处理器
    ResponseCommandHandler* responseHandler = new ResponseCommandHandler();
    processor->registerCommandHandler(CMD_DATA, responseHandler);

    // ======= 信号链 =======
    // 1. 建立数据入口流：TcpClient -> TcpDataProcessor
    connect(&tcpClient, &TcpClient::dataReceived,  processor, &TcpDataProcessor::processData, Qt::QueuedConnection);

    // 2. 数据处理结果通过TcpClient中转 - 命令处理结果
    connect(processor, &TcpDataProcessor::commandProcessed,
            &tcpClient, &TcpClient::processingResult, Qt::QueuedConnection);

    // 3. 数据处理结果通过TcpClient中转 - 命令错误
    connect(processor, &TcpDataProcessor::commandError,
            &tcpClient, &TcpClient::processingError, Qt::QueuedConnection);

    // 4. 数据处理结果通过TcpClient中转 - 响应处理器到TcpClient
    connect(responseHandler, &ResponseCommandHandler::responseReceived,
            processor, &TcpDataProcessor::subscribeResponse, Qt::QueuedConnection);

    // 6. 从TcpClient到UI的连接-以便在MainWindow中处理和记录
    // connect(&tcpClient, &TcpClient::processingResult,
    //         this, &MainWindow::onTcpCommandProcessed, Qt::QueuedConnection);

    // connect(&tcpClient, &TcpClient::processingError,
    //         this, &MainWindow::onTcpCommandError, Qt::QueuedConnection);

    // 7. 从TcpClient直接到SchemeManagement的连接
    /*
     * TODO begin*************
    connect(&tcpClient, &TcpClient::processingResult,
            schemeManagementWindow, &SchemeManagement::updateCommandStatus, Qt::QueuedConnection);

    connect(&tcpClient, &TcpClient::processingError,
            schemeManagementWindow, &SchemeManagement::handleCommandError, Qt::QueuedConnection);
     *TODO end*************
    */

    // 8. 响应信号连接
    // connect(processor, &TcpDataProcessor::subscribeResponse,
    //         this, &MainWindow::onTcpResponseReceived, Qt::QueuedConnection);

    /*
     * TODO begin*************
    connect(processor, &TcpDataProcessor::subscribeResponse,
            schemeManagementWindow, &SchemeManagement::processResponseData, Qt::QueuedConnection);
     *TODO end*************
    */

    // 9. TCP客户端连接状态变化通知
    connect(&tcpClient, &TcpClient::connectionStatusChanged,
            this, [this, serverIp, serverPort](bool connected) {
                if (connected) {
                    qDebug() << "成功连接到TCP服务器:" << serverIp << ":" << serverPort;
                } else {
                    qDebug() << "与TCP服务器断开连接:" << serverIp << ":" << serverPort;
                }
            });

    connect(&tcpClient, &TcpClient::connectionError,
            this, [this](const QString& errorMsg) {
                qWarning() << "TCP连接错误:" << errorMsg;
            });

    // 尝试连接到TCP服务器
    bool connected = tcpClient.connectToServer();
    if (connected) {
        // 测试SUBSCRIBE_PACKAGE_CMD_SUB
        // SUBSCRIBE_PACKAGE_CMD_SUB data;
        // data.header.command = CMD_SUBSCRIBE;
        // // data.subRuleVec.emplace_back({1, 1, 1, 1, "test1"});
        // // data.subRuleVec.emplace_back(1, 1, 1, 1, "test1");
        // // data.subRuleVec.emplace_back(2, 2, 2, 2, std::string("test2"));
        // // data.subRuleVec.emplace_back(3, 3, 3, 3, "test3");
        // SUBSCRIBE_PACKAGE_SUB_RULE temp = {1, 1, 1, 1, "test1"};
        // data.subRuleVec.emplace_back(temp);
        // char buffer[BUFFER_SIZE] = {0};
        // int len = serialize(&data, sizeof(data), buffer);
        // memcpy(buffer, &len, sizeof(len));
        // // 将订阅包转换为二进制数据
        // QByteArray senddata(buffer, len);

        // // 使用 TcpClient 发送数据
        // TcpClient& client = TcpClient::getInstance();

        // // 检查是否已连接
        // if (!client.isConnected()) {
        //     qDebug() << "TCP客户端未连接，尝试连接...";
        //     if (!client.connectToServer()) {
        //         qDebug() << "TCP客户端连接失败，无法发送数据";
        //         return;
        //     }
        // }

        // // 发送数据
        // bool success = client.sendData(senddata);
        qDebug() << "成功连接到TCP服务器:" << serverIp << ":" << serverPort;
    } else {
        qDebug() << "无法连接到TCP服务器:" << serverIp << ":" << serverPort;
    }

    // 保存线程和处理器的引用到UserSession，以便于应用程序退出时正确释放资源
    UserSession::getInstance().setDataProcessorThread(m_dataProcessorThread);
}

/**
 * 处理TCP响应数据
 *
 * 此方法接收来自TcpClient中转的响应数据
 */
void MainWindow::onTcpResponseReceived(const QByteArray &response)
{
    if (response.isEmpty()) {
        qWarning() << "收到空的TCP响应数据";
        return;
    }

    qDebug() << "MainWindow: 收到TCP响应数据，大小:" << response.size() << "字节";

    // 解析响应数据，这里示例以十六进制显示部分数据
    QString hexData = response.left(50).toHex(' ').toUpper();
    if (response.size() > 50) {
        hexData += "...";
    }
    qDebug() << "TCP响应数据内容(十六进制):" << hexData;

    // 注意：响应数据现在通过TcpClient直接发送到SchemeManagement
    // 这里只需记录日志并执行MainWindow特有的处理逻辑
}

/**
 * 处理TCP命令处理结果
 *
 * 此方法接收来自TcpClient中转的命令处理结果
 */
void MainWindow::onTcpCommandProcessed(SUBSCRIBE_COMMAND cmdType, bool success, const QString &message)
{
    QString cmdName;
    switch (cmdType) {
    case CMD_SUBSCRIBE:   cmdName = "订阅"; break;
    case CMD_UNSUBSCRIBE: cmdName = "取消订阅"; break;
    case CMD_SUSPEND:     cmdName = "暂停"; break;
    case CMD_CONTINUE:    cmdName = "继续"; break;
    case CMD_RESPONSE:    cmdName = "响应"; break;
    default:              cmdName = QString("未知(%1)").arg(cmdType); break;
    }

    QString statusText = success ? "成功" : "失败";
    qDebug() << "MainWindow: " << cmdName << "命令处理" << statusText << "：" << message;

    // 注意：命令处理结果现在通过TcpClient直接发送到SchemeManagement
    // 这里只需记录日志并执行MainWindow特有的处理逻辑

    // 可以在状态栏显示命令处理结果
    // statusBar()->showMessage(tr("%1命令%2: %3").arg(cmdName).arg(statusText).arg(message), 3000);
}

/**
 * 处理TCP命令处理错误
 *
 * 此方法接收来自TcpClient中转的命令处理错误
 */
void MainWindow::onTcpCommandError(SUBSCRIBE_COMMAND cmdType, const QString &errorMessage)
{
    QString cmdName;
    switch (cmdType) {
    case CMD_SUBSCRIBE:   cmdName = "订阅"; break;
    case CMD_UNSUBSCRIBE: cmdName = "取消订阅"; break;
    case CMD_SUSPEND:     cmdName = "暂停"; break;
    case CMD_CONTINUE:    cmdName = "继续"; break;
    case CMD_RESPONSE:    cmdName = "响应"; break;
    default:              cmdName = QString("未知(%1)").arg(cmdType); break;
    }

    qWarning() << "MainWindow: " << cmdName << "命令处理错误：" << errorMessage;

    // 注意：命令处理错误现在通过TcpClient直接发送到SchemeManagement
    // 这里只需记录日志并执行MainWindow特有的处理逻辑

    // 对于严重错误，可以在主窗口显示错误提示
    // if (cmdType == CMD_SUBSCRIBE || cmdType == CMD_CONTINUE) {
    //     QMessageBox::warning(this, tr("命令处理错误"),
    //                          tr("%1命令处理错误: %2").arg(cmdName).arg(errorMessage));
    // }
}

void MainWindow::onTimeSeriesTriggered()
{
    // 设置标题
    ui->titleLabel->setText("时序图");

    // 显示二楼FC监控界面
    //contentStack->setCurrentWidget(m_fcMonitorWindows);
    //m_graphMonitorWindows->loadSchemeData();

    // 更新按钮选中状态
    overviewButton->setChecked(true);
    documentButton->setChecked(false);
    managementButton->setChecked(false);

    // 更新按钮选中状态
    homeButton->setChecked(false);
}

void MainWindow::onDataLoadTriggered()
{
    // 设置标题
    ui->titleLabel->setText("数据加载监控");

    // 显示数据加载监控页面
    // TODO: 创建并显示数据加载监控页面
    // contentStack->setCurrentWidget(dataLoadWindow);

    // 更新按钮选中状态
    homeButton->setChecked(false);
}

void MainWindow::createRealTimeButtons()
{
    // 创建按钮组
    monitorButtonGroup = new QButtonGroup(this);
    // monitorButtonGroup->addButton(homeButton);

    // 创建数值监控按钮
    valueMonitorButton = new QPushButton("数值监控", this);
    valueMonitorButton->setIcon(QIcon());
    valueMonitorButton->setStyleSheet(
        "QPushButton {"
        "    border: none;"
        "    color: white;"
        "    font-size: 11px;"
        "    min-height: 60px;"
        "    max-height: 60px;"
        "    padding-top: 55px;"
        "    text-align: center;"
        "    border-radius: 4px;"
        "    margin: 2px 5px;"
        "    background-position: center 5px;"
        "    background-repeat: no-repeat;"
        "    background-size: 32px 32px;"
        "    background-image: url(:/images/valueMonitoring.png);"
        "}"
        "QPushButton:hover {"
        "    background-color: rgba(255, 255, 255, 0.15);"
        "    border: 1px solid rgba(255, 255, 255, 0.3);"
        "}"
        "QPushButton:checked {"
        "    background-color: rgba(0, 0, 0, 0.2);"
        "    border: 1px solid rgba(255, 255, 255, 0.5);"
        "    box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.2);"
        "}");
    valueMonitorButton->setCheckable(true);
    monitorButtonGroup->addButton(valueMonitorButton);

    // 创建图形监控按钮
    graphMonitorButton = new QPushButton("图形监控", this);
    graphMonitorButton->setIcon(QIcon());
    graphMonitorButton->setStyleSheet(
        "QPushButton {"
        "    border: none;"
        "    color: white;"
        "    font-size: 11px;"
        "    min-height: 60px;"
        "    max-height: 60px;"
        "    padding-top: 55px;"
        "    text-align: center;"
        "    border-radius: 4px;"
        "    margin: 2px 5px;"
        "    background-position: center 5px;"
        "    background-repeat: no-repeat;"
        "    background-size: 32px 32px;"
        "    background-image: url(:/images/hostOverview2.png);"
        "}"
        "QPushButton:hover {"
        "    background-color: rgba(255, 255, 255, 0.15);"
        "    border: 1px solid rgba(255, 255, 255, 0.3);"
        "}"
        "QPushButton:checked {"
        "    background-color: rgba(0, 0, 0, 0.2);"
        "    border: 1px solid rgba(255, 255, 255, 0.5);"
        "    box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.2);"
        "}");
    graphMonitorButton->setCheckable(true);
    monitorButtonGroup->addButton(graphMonitorButton);

    // 创建时序图按钮
    timeSeriesButton = new QPushButton("时序图", this);
    timeSeriesButton->setIcon(QIcon());
    timeSeriesButton->setStyleSheet(
        "QPushButton {"
        "    border: none;"
        "    color: white;"
        "    font-size: 11px;"
        "    min-height: 60px;"
        "    max-height: 60px;"
        "    padding-top: 55px;"
        "    text-align: center;"
        "    border-radius: 4px;"
        "    margin: 2px 5px;"
        "    background-position: center 5px;"
        "    background-repeat: no-repeat;"
        "    background-size: 32px 32px;"
        "    background-image: url(:/images/hostOverview3.png);"
        "}"
        "QPushButton:hover {"
        "    background-color: rgba(255, 255, 255, 0.15);"
        "    border: 1px solid rgba(255, 255, 255, 0.3);"
        "}"
        "QPushButton:checked {"
        "    background-color: rgba(0, 0, 0, 0.2);"
        "    border: 1px solid rgba(255, 255, 255, 0.5);"
        "    box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.2);"
        "}");
    timeSeriesButton->setCheckable(true);
    monitorButtonGroup->addButton(timeSeriesButton);

    // 创建数据加载监控按钮
    dataLoadButton = new QPushButton("数据加载监控", this);
    dataLoadButton->setIcon(QIcon());
    dataLoadButton->setStyleSheet(
        "QPushButton {"
        "    border: none;"
        "    color: white;"
        "    font-size: 11px;"
        "    min-height: 60px;"
        "    max-height: 60px;"
        "    padding-top: 55px;"
        "    text-align: center;"
        "    border-radius: 4px;"
        "    margin: 2px 5px;"
        "    background-position: center 5px;"
        "    background-repeat: no-repeat;"
        "    background-size: 32px 32px;"
        "    background-image: url(:/images/hostOverview4.png);"
        "}"
        "QPushButton:hover {"
        "    background-color: rgba(255, 255, 255, 0.15);"
        "    border: 1px solid rgba(255, 255, 255, 0.3);"
        "}"
        "QPushButton:checked {"
        "    background-color: rgba(0, 0, 0, 0.2);"
        "    border: 1px solid rgba(255, 255, 255, 0.5);"
        "    box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.2);"
        "}");
    dataLoadButton->setCheckable(true);
    monitorButtonGroup->addButton(dataLoadButton);

    // 创建单位配置按钮
    unitConfigButton = new QPushButton("单位配置", this);
    unitConfigButton->setIcon(QIcon());
    unitConfigButton->setStyleSheet(
        "QPushButton {"
        "    border: none;"
        "    color: white;"
        "    font-size: 11px;"
        "    min-height: 60px;"
        "    max-height: 60px;"
        "    padding-top: 55px;"
        "    text-align: center;"
        "    border-radius: 4px;"
        "    margin: 2px 5px;"
        "    background-position: center 5px;"
        "    background-repeat: no-repeat;"
        "    background-size: 32px 32px;"
        "    background-image: url(:/images/unitConfig.png);"
        "}"
        "QPushButton:hover {"
        "    background-color: rgba(255, 255, 255, 0.15);"
        "    border: 1px solid rgba(255, 255, 255, 0.3);"
        "}"
        "QPushButton:checked {"
        "    background-color: rgba(0, 0, 0, 0.2);"
        "    border: 1px solid rgba(255, 255, 255, 0.5);"
        "    box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.2);"
        "}");
    unitConfigButton->setCheckable(true);
    monitorButtonGroup->addButton(unitConfigButton);

    // 初始时隐藏监控按钮
    valueMonitorButton->setVisible(false);
    graphMonitorButton->setVisible(false);
    timeSeriesButton->setVisible(false);
    dataLoadButton->setVisible(false);
    unitConfigButton->setVisible(false);
}

void MainWindow::createReplayButtons()
{
    // 创建回放按钮组
    replayButtonGroup = new QButtonGroup(this);

    // 创建回放数值监控按钮
    replayValueMonitorButton = new QPushButton("数值监控", this);
    replayValueMonitorButton->setIcon(QIcon());
    replayValueMonitorButton->setStyleSheet(
        "QPushButton {"
        "    border: none;"
        "    color: white;"
        "    font-size: 11px;"
        "    min-height: 60px;"
        "    max-height: 60px;"
        "    padding-top: 55px;"
        "    text-align: center;"
        "    border-radius: 4px;"
        "    margin: 2px 5px;"
        "    background-position: center 5px;"
        "    background-repeat: no-repeat;"
        "    background-size: 32px 32px;"
        "    background-image: url(:/images/valueMonitoring.png);"
        "}"
        "QPushButton:hover {"
        "    background-color: rgba(255, 255, 255, 0.15);"
        "    border: 1px solid rgba(255, 255, 255, 0.3);"
        "}"
        "QPushButton:checked {"
        "    background-color: rgba(0, 0, 0, 0.2);"
        "    border: 1px solid rgba(255, 255, 255, 0.5);"
        "    box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.2);"
        "}");
    replayValueMonitorButton->setCheckable(true);
    replayButtonGroup->addButton(replayValueMonitorButton);

    // 创建回放图形监控按钮
    replayGraphMonitorButton = new QPushButton("图形监控", this);
    replayGraphMonitorButton->setIcon(QIcon());
    replayGraphMonitorButton->setStyleSheet(
        "QPushButton {"
        "    border: none;"
        "    color: white;"
        "    font-size: 11px;"
        "    min-height: 60px;"
        "    max-height: 60px;"
        "    padding-top: 55px;"
        "    text-align: center;"
        "    border-radius: 4px;"
        "    margin: 2px 5px;"
        "    background-position: center 5px;"
        "    background-repeat: no-repeat;"
        "    background-size: 32px 32px;"
        "    background-image: url(:/images/hostOverview2.png);"
        "}"
        "QPushButton:hover {"
        "    background-color: rgba(255, 255, 255, 0.15);"
        "    border: 1px solid rgba(255, 255, 255, 0.3);"
        "}"
        "QPushButton:checked {"
        "    background-color: rgba(0, 0, 0, 0.2);"
        "    border: 1px solid rgba(255, 255, 255, 0.5);"
        "    box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.2);"
        "}");
    replayGraphMonitorButton->setCheckable(true);
    replayButtonGroup->addButton(replayGraphMonitorButton);

    // 创建回放时序图按钮
    replayTimeSeriesButton = new QPushButton("时序图", this);
    replayTimeSeriesButton->setIcon(QIcon());
    replayTimeSeriesButton->setStyleSheet(
        "QPushButton {"
        "    border: none;"
        "    color: white;"
        "    font-size: 11px;"
        "    min-height: 60px;"
        "    max-height: 60px;"
        "    padding-top: 55px;"
        "    text-align: center;"
        "    border-radius: 4px;"
        "    margin: 2px 5px;"
        "    background-position: center 5px;"
        "    background-repeat: no-repeat;"
        "    background-size: 32px 32px;"
        "    background-image: url(:/images/hostOverview3.png);"
        "}"
        "QPushButton:hover {"
        "    background-color: rgba(255, 255, 255, 0.15);"
        "    border: 1px solid rgba(255, 255, 255, 0.3);"
        "}"
        "QPushButton:checked {"
        "    background-color: rgba(0, 0, 0, 0.2);"
        "    border: 1px solid rgba(255, 255, 255, 0.5);"
        "    box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.2);"
        "}");
    replayTimeSeriesButton->setCheckable(true);
    replayButtonGroup->addButton(replayTimeSeriesButton);

    // 创建回放数据加载监控按钮
    replayDataLoadButton = new QPushButton("数据加载监控", this);
    replayDataLoadButton->setIcon(QIcon());
    replayDataLoadButton->setStyleSheet(
        "QPushButton {"
        "    border: none;"
        "    color: white;"
        "    font-size: 11px;"
        "    min-height: 60px;"
        "    max-height: 60px;"
        "    padding-top: 55px;"
        "    text-align: center;"
        "    border-radius: 4px;"
        "    margin: 2px 5px;"
        "    background-position: center 5px;"
        "    background-repeat: no-repeat;"
        "    background-size: 32px 32px;"
        "    background-image: url(:/images/hostOverview4.png);"
        "}"
        "QPushButton:hover {"
        "    background-color: rgba(255, 255, 255, 0.15);"
        "    border: 1px solid rgba(255, 255, 255, 0.3);"
        "}"
        "QPushButton:checked {"
        "    background-color: rgba(0, 0, 0, 0.2);"
        "    border: 1px solid rgba(255, 255, 255, 0.5);"
        "    box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.2);"
        "}");
    replayDataLoadButton->setCheckable(true);
    replayButtonGroup->addButton(replayDataLoadButton);

    // 创建回放单位配置按钮
    replayUnitConfigButton = new QPushButton("单位配置", this);
    replayUnitConfigButton->setIcon(QIcon());
    replayUnitConfigButton->setStyleSheet(
        "QPushButton {"
        "    border: none;"
        "    color: white;"
        "    font-size: 11px;"
        "    min-height: 60px;"
        "    max-height: 60px;"
        "    padding-top: 55px;"
        "    text-align: center;"
        "    border-radius: 4px;"
        "    margin: 2px 5px;"
        "    background-position: center 5px;"
        "    background-repeat: no-repeat;"
        "    background-size: 32px 32px;"
        "    background-image: url(:/images/unitConfig.png);"
        "}"
        "QPushButton:hover {"
        "    background-color: rgba(255, 255, 255, 0.15);"
        "    border: 1px solid rgba(255, 255, 255, 0.3);"
        "}"
        "QPushButton:checked {"
        "    background-color: rgba(0, 0, 0, 0.2);"
        "    border: 1px solid rgba(255, 255, 255, 0.5);"
        "    box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.2);"
        "}");
    replayUnitConfigButton->setCheckable(true);
    replayButtonGroup->addButton(replayUnitConfigButton);

    // 初始时隐藏回放按钮
    replayValueMonitorButton->setVisible(false);
    replayGraphMonitorButton->setVisible(false);
    replayTimeSeriesButton->setVisible(false);
    replayDataLoadButton->setVisible(false);
    replayUnitConfigButton->setVisible(false);
}

void MainWindow::initMonitorButtons()
{
    createRealTimeButtons();
    createReplayButtons();

    // 连接实时按钮信号槽
    connect(valueMonitorButton, &QPushButton::clicked, this, [this]() {
        onValueMonitorTriggered(false);  // 实时模式
    });
    connect(graphMonitorButton, &QPushButton::clicked, this, &MainWindow::onGraphMonitorTriggered);
    connect(timeSeriesButton, &QPushButton::clicked, this, &MainWindow::onTimeSeriesTriggered);
    connect(dataLoadButton, &QPushButton::clicked, this, &MainWindow::onDataLoadTriggered);
    connect(unitConfigButton, &QPushButton::clicked, this, &MainWindow::onUnitConfigTriggered);
    
    // 连接回放按钮信号槽
    connect(replayValueMonitorButton, &QPushButton::clicked, this, [this]() {
        onValueMonitorTriggered(true);  // 回放模式
    });
    connect(replayGraphMonitorButton, &QPushButton::clicked, this, &MainWindow::onGraphMonitorTriggered);
    connect(replayTimeSeriesButton, &QPushButton::clicked, this, &MainWindow::onTimeSeriesTriggered);
    connect(replayDataLoadButton, &QPushButton::clicked, this, &MainWindow::onDataLoadTriggered);
    connect(replayUnitConfigButton, &QPushButton::clicked, this, &MainWindow::onUnitConfigTriggered);
}
