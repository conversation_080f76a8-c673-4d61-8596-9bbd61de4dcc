//
// Created by 合计飞 on 2025/4/15.
//

#ifndef TCP_FRAMESTRUCT_H
#define TCP_FRAMESTRUCT_H

//#include "logger.hpp"

#include <cstdint>
#include <string>
#include <vector>
#include <list>
#include <cstring>
#include <iostream>

#define BUFFER_SIZE (1024*1024)

enum SUBSCRIBE_COMMAND {
    // 订阅
    CMD_SUBSCRIBE = 1,
    // 取消订阅
    CMD_UNSUBSCRIBE,
    // 暂停
    CMD_SUSPEND,
    // 继续
    CMD_CONTINUE,
    // 响应
    CMD_RESPONSE,
    // 数据
    CMD_DATA,
};

enum SUBSCRIBE_RET_CODE {
    // 成功
    SUBSCRIBE_SUC = 0,
    // 订阅条件为空
    SUBSCRIBE_ERR_SUB_RULE_EMPTY,
    // 请求长度错误(与对应请求结构体不匹配)
    SUBSCRIBE_ERR_REQ_LEN_ERROR,
    // 未知的请求类型
    SUBSCRIBE_ERR_UNKNOWN_REQ_TYPE,
    // 未知的ip和端口
    SUBSCRIBE_ERR_UNKNOWN_IP_OR_PORT,

    // AW订阅相关
    SUBSCRIBE_SEND_SUB_ERROR,
    SUBSCRIBE_ERR_NOT_REPLY,
    SUBSCRIBE_ERR_NOT_REPLY_MAIN,
    SUBSCRIBE_ERR_NOT_REPLY_BAK,
    SUBSCRIBE_ERR_REPLY_DATA_ERROR,
    SUBSCRIBE_ERR_UNKNOWN_REPLY_IP,
    SUBSCRIBE_ERR_REPLY_CODE_ERROR
};

//#pragma pack(push, 1)
typedef struct {
    uint32_t packLength; //包长
    uint32_t command;
} SUBSCRIBE_PACKAGE_HEADER;

typedef struct {
    uint32_t sourceId;        // 源id
    uint32_t topicId;      // 订阅主题id
} SUBSCRIBE_PACKAGE_SUB_RULE;

typedef struct {
    SUBSCRIBE_PACKAGE_HEADER header;
    std::vector<SUBSCRIBE_PACKAGE_SUB_RULE> subRuleVec;
} SUBSCRIBE_PACKAGE_CMD_SUB;

typedef struct {
    SUBSCRIBE_PACKAGE_HEADER header;
    std::vector<SUBSCRIBE_PACKAGE_SUB_RULE> unSubRUleVec;
} SUBSCRIBE_PACKAGE_CMD_UNSUB;

typedef struct {
    SUBSCRIBE_PACKAGE_HEADER header;
} SUBSCRIBE_PACKAGE_CMD_SUSPEND;

typedef struct {
    SUBSCRIBE_PACKAGE_HEADER header;
} SUBSCRIBE_PACKAGE_CMD_CONTINUE;

typedef struct {
    SUBSCRIBE_PACKAGE_HEADER header;
    int32_t dataLen;
    char* dataPtr;
} SUBSCRIBE_PACKAGE_CMD_DATA;

typedef struct {
    SUBSCRIBE_PACKAGE_HEADER header;
    int32_t dataLen;
    char dataPtr[];
} SUBSCRIBE_PACKAGE_CMD_DATA_BUFFER;

typedef struct {
    SUBSCRIBE_PACKAGE_HEADER header;
    SUBSCRIBE_RET_CODE code;
    std::string msg;
} SUBSCRIBE_PACKAGE_RESPONSE;

//响应格式
typedef struct {
    SUBSCRIBE_PACKAGE_HEADER header;
    uint32_t code;  //响应码:0-成功,1-失败
} SUBSCRIBE_PACKAGE_RET;

/*******************************大数据订阅-begin*******************************************/
typedef enum {
    SUBSCRIBE_CMD_SUB = 1,          //订阅
    SUBSCRIBE_CMD_UNSUB = 2,        //取消订阅
    SUBSCRIBE_CMD_HEART_BEAT = 3,   //心跳
    SUBSCRIBE_CMD_PRINT_SUB_LIST = 4,
} SUBSCRIBE_CMD;

#pragma pack(push, 1)
typedef struct {
    SUBSCRIBE_PACKAGE_HEADER header;
    uint32_t ip;        //订阅IP
    uint16_t port;      //订阅端口
    uint32_t bench_id;  //试验台号
    uint32_t data_bus_type;
} SUBSCRIBE_PACKAGE_CMD_SUB_BD;

typedef struct {
    SUBSCRIBE_PACKAGE_HEADER header;
    uint32_t ip;
    uint16_t port;
} SUBSCRIBE_PACKAGE_CMD_HEART_BEAT;
#pragma pack(pop)

/*******************************大数据订阅-end*******************************************/

// 定义序列化和反序列化宏
#define SERIALIZE_OBJ(obj) \
    memcpy(ptr, &obj, sizeof(obj)); \
    ptr += sizeof(obj);

// 定义序列化和反序列化宏
#define SERIALIZE_STR(str) \
    {                       \
        uint32_t strSize = str.size(); \
        SERIALIZE_OBJ(strSize) \
        memcpy(ptr, str.data(), strSize); \
        ptr += strSize;        \
    }

#define SERIALIZE_RULE_VEC(vec)     \
    for (const auto& rule : vec) {  \
        SERIALIZE_OBJ(rule.sourceId) \
        SERIALIZE_OBJ(rule.topicId) \
    }

#define SERIALIZE_DATA_PTR(dataPtr, len) \
    memcpy(ptr, dataPtr, len); \
    ptr += len;

#define DESERIALIZE_OBJ(obj) \
    memcpy(&obj, ptr, sizeof(obj)); \
    ptr += sizeof(obj);

#define DESERIALIZE_STR(str) \
    {\
        uint32_t strLen;              \
        DESERIALIZE_OBJ(strLen)  \
        str.assign(ptr, strLen);  \
        ptr += strLen;           \
    }

#define DESERIALIZE_RULE_VEC(vec, num) \
    for (int i = 0; i < num; ++i) {    \
        SUBSCRIBE_PACKAGE_SUB_RULE rule; \
        DESERIALIZE_OBJ(rule.sourceId) \
        DESERIALIZE_OBJ(rule.topicId) \
        vec.push_back(rule);\
    }

#define DESERIALIZE_DATA_PTR(dataPtr, len) \
    memcpy(dataPtr, ptr, len); \
    ptr += len;

inline int serialize(const void *data, uint32_t len, char *buffer) {
    if (len < sizeof(SUBSCRIBE_PACKAGE_HEADER)) {
        std::cout<<"[ERROR]serialize data failed: dataLen(%d) < sizeof(SUBSCRIBE_PACKAGE_HEADER)"<< len << "<"
                << sizeof(SUBSCRIBE_PACKAGE_HEADER) << std::endl;;
        return -1;
    }

    char *ptr = buffer;

    auto *header = static_cast<const SUBSCRIBE_PACKAGE_HEADER *>(data);

    SERIALIZE_OBJ(header->packLength)
    SERIALIZE_OBJ(header->command)

    switch (header->command) {
        // 订阅
        case SUBSCRIBE_COMMAND::CMD_SUBSCRIBE: {
            auto *subData = static_cast<const SUBSCRIBE_PACKAGE_CMD_SUB *>(data);
            uint32_t vecSize = subData->subRuleVec.size();
            SERIALIZE_OBJ(vecSize);
            SERIALIZE_RULE_VEC(subData->subRuleVec)
            break;
        }
            //取消订阅
        case SUBSCRIBE_COMMAND::CMD_UNSUBSCRIBE: {
            auto *unSubData = static_cast<const SUBSCRIBE_PACKAGE_CMD_UNSUB *>(data);
            uint32_t vecSize = unSubData->unSubRUleVec.size();
            SERIALIZE_OBJ(vecSize);
            SERIALIZE_RULE_VEC(unSubData->unSubRUleVec)
            break;
        }
        case SUBSCRIBE_COMMAND::CMD_RESPONSE: {
            auto *responseData = static_cast<const SUBSCRIBE_PACKAGE_RESPONSE *>(data);
            SERIALIZE_OBJ(responseData->code);
            SERIALIZE_STR(responseData->msg)
            break;
        }
        case SUBSCRIBE_COMMAND::CMD_DATA: {
            auto dataData = static_cast<const SUBSCRIBE_PACKAGE_CMD_DATA *>(data);
            SERIALIZE_OBJ(dataData->dataLen)
            SERIALIZE_DATA_PTR(dataData->dataPtr, dataData->dataLen)
            break;
        }
        default:
            break;
    }
    //重新设置包长
    int32_t dataLen = ptr - buffer;
    *(int32_t*)buffer = dataLen;

    return dataLen;
}

inline int deserialize(const char *buffer, int len, void *data) {
    if (len < sizeof(SUBSCRIBE_PACKAGE_HEADER)) {
        std::cout<<"[ERROR]serialize data failed: dataLe < sizeof(SUBSCRIBE_PACKAGE_HEADER)=" << len << "<"
               << sizeof(SUBSCRIBE_PACKAGE_HEADER) << std::endl;
        return -1;
    }

    const char *ptr = buffer;

    auto *header = reinterpret_cast<SUBSCRIBE_PACKAGE_HEADER *>(data);

    DESERIALIZE_OBJ(header->packLength)
    DESERIALIZE_OBJ(header->command)

    header->packLength = sizeof(SUBSCRIBE_PACKAGE_HEADER);

    std::cout<<"deserialize cmd:%d len:"<<header->command <<" " <<  header->packLength<<std::endl;
    switch (header->command) {
        // 订阅
        case SUBSCRIBE_COMMAND::CMD_SUBSCRIBE: {
            auto *subData = reinterpret_cast<SUBSCRIBE_PACKAGE_CMD_SUB *>(data);
            int ruleNum;
            DESERIALIZE_OBJ(ruleNum)
            DESERIALIZE_RULE_VEC(subData->subRuleVec, ruleNum)

            subData->header.packLength += sizeof(SUBSCRIBE_PACKAGE_SUB_RULE) * ruleNum;
            break;
        }
            //取消订阅
        case SUBSCRIBE_COMMAND::CMD_UNSUBSCRIBE: {
            auto *unSubData = reinterpret_cast<SUBSCRIBE_PACKAGE_CMD_UNSUB *>(data);
            int ruleNum;
            DESERIALIZE_OBJ(ruleNum)
            DESERIALIZE_RULE_VEC(unSubData->unSubRUleVec, ruleNum)

            unSubData->header.packLength += sizeof(SUBSCRIBE_PACKAGE_SUB_RULE) * ruleNum;
            break;
        }
        case SUBSCRIBE_COMMAND::CMD_RESPONSE: {
            auto *responseData = static_cast<SUBSCRIBE_PACKAGE_RESPONSE *>(data);
            DESERIALIZE_OBJ(responseData->code);
            DESERIALIZE_STR(responseData->msg)

            responseData->header.packLength += sizeof(responseData->code) + responseData->msg.size();
            break;
        }
        case SUBSCRIBE_COMMAND::CMD_DATA: {
            auto dataData = static_cast<SUBSCRIBE_PACKAGE_CMD_DATA_BUFFER *>(data);
            DESERIALIZE_OBJ(dataData->dataLen)
            DESERIALIZE_DATA_PTR(dataData->dataPtr, dataData->dataLen)

            dataData->header.packLength += dataData->dataLen;
            break;
        }
        default:
            break;
    }
    return ptr - buffer;
}

#endif //PARSER_FRAMESTRUCT_H
