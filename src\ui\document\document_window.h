#ifndef DOCUMENT_WINDOW_H
#define DOCUMENT_WINDOW_H

#include <QMainWindow>
#include <QStandardItemModel>

namespace Ui {
class DocumentWindow;
}

class DocumentWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit DocumentWindow(QWidget *parent = nullptr);
    ~DocumentWindow();

private slots:
    void onUploadFCClicked();
    void onUploadICDClicked();
    void onPreviewClicked();

private:
    void setupUI();
    void setupConnections();
    void initializeDocumentList();

    Ui::DocumentWindow *ui;
    QStandardItemModel *documentModel;
};

#endif // DOCUMENT_WINDOW_H 