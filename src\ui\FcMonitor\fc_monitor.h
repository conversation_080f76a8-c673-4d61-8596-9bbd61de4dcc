#ifndef FC_MONITOR_H
#define FC_MONITOR_H

#include <QWidget>
#include <QVBoxLayout>
#include "fc_monitor_widget.h"

namespace Ui {
class FcMonitor;
}

class FcMonitor : public QWidget
{
    Q_OBJECT

public:
    explicit FcMonitor(QWidget *parent = nullptr);
    ~FcMonitor();

    // 提供对FcMonitorWidget的访问
    FcMonitorWidget* getMonitorWidget() const { return m_monitorWidget; }

private:
    // 便捷方法
    void loadScheme(const QString& schemeName, const QString& xmlFilePath);
    void startMonitoring(const QString& schemeName);
    void stopMonitoring(const QString& schemeName);


    void setupWidget();
    void createSampleData();
    void createDefaultSchemeFile(const QString& filePath);

private:
    Ui::FcMonitor *ui;
    FcMonitorWidget* m_monitorWidget;
};

#endif // FC_MONITOR_H
