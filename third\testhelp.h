#ifndef TESTHELP_H
#define TESTHELP_H
#include <string>

#include "frameStruct.h"


typedef struct {
    uint32_t destId: 16;        // caller_id:请求方id  dstId 目的服务1
    uint32_t sourceId: 16;      // service_id 服务id  srcId 源服务1
    uint32_t interfaceId: 16;   // 接口id 接口1
    uint32_t interfaceType: 16; // 接口类型：0-RPC 1-PUBSUB 2-EVENT 需要动态库查询
    std::string topicValue;     // 主题值 需要动态库查询 <主题值>S1_I1_TOPIC1</主题值>
} SUBSCRIBE_PACKAGE_SUB_RULE;



enum SUBSCRIBE_RET_CODE {
    // 成功
    SUBSCRIBE_SUC = 0,
    // 订阅条件为空
    SUBSCRIBE_ERR_SUB_RULE_EMPTY,
    // 请求长度错误(与对应请求结构体不匹配)
    SUBSCRIBE_ERR_REQ_LEN_ERROR,
    // 未知的请求类型
    SUBSCRIBE_ERR_UNKNOWN_REQ_TYPE,
    // 未知的ip和端口
    SUBSCRIBE_ERR_UNKNOWN_IP_OR_PORT,

    // AW订阅相关
    SUBSCRIBE_SEND_SUB_ERROR,
    SUBSCRIBE_ERR_NOT_REPLY,
    SUBSCRIBE_ERR_NOT_REPLY_MAIN,
    SUBSCRIBE_ERR_NOT_REPLY_BAK,
    SUBSCRIBE_ERR_REPLY_DATA_ERROR,
    SUBSCRIBE_ERR_UNKNOWN_REPLY_IP,
    SUBSCRIBE_ERR_REPLY_CODE_ERROR
};

typedef struct {
    SUBSCRIBE_PACKAGE_HEADER header;
    uint32_t ip;
    uint16_t port; // 客户端数据接收端口 订阅
    uint32_t subRuleArrNum;
    SUBSCRIBE_PACKAGE_SUB_RULE *subRuleArray;
} SUBSCRIBE_PACKAGE_CMD_SUB_TEST;

typedef struct {
    SUBSCRIBE_PACKAGE_HEADER header;
    uint32_t ip;
    uint16_t port; // 客户端数据接收端口 取消订阅
    uint32_t unSubRuleArrNum;
    SUBSCRIBE_PACKAGE_SUB_RULE *unSubRuleArray;
} SUBSCRIBE_PACKAGE_CMD_UNSUB;

typedef struct {
    SUBSCRIBE_PACKAGE_HEADER header;
    uint32_t ip;
    uint16_t port; // 客户端数据接收端口 暂停
} SUBSCRIBE_PACKAGE_CMD_SUSPEND;

typedef struct {
    SUBSCRIBE_PACKAGE_HEADER header;
    uint32_t ip;
    uint16_t port; // 客户端数据接收端口 继续
} SUBSCRIBE_PACKAGE_CMD_CONTINUE;

typedef struct {
    SUBSCRIBE_PACKAGE_HEADER header;
    SUBSCRIBE_RET_CODE code;       // 所有命令的响应
    std::string msg;               // 当code不为0的时候 需要打印这个错误信息
} SUBSCRIBE_PACKAGE_RESPONSE;

typedef struct {
    SUBSCRIBE_PACKAGE_HEADER header;
    uint32_t ip;        //订阅IP
    uint16_t port;      //订阅端口
    uint32_t bench_id;  //试验台号
    uint32_t data_bus_type; //总线数据类型:1-FC,2-任务1394,3-飞管1394,4-818,5-muse
    uint32_t card_id;//818通道号
    unsigned char *sub_data;
} SUBSCRIBE_PACKAGE_CMD_SUB_TO_AW;

typedef struct {
    uint32_t packLength; //包长
    SUBSCRIBE_COMMAND command;
} SUBSCRIBE_PACKAGE_HEADER1;

typedef struct {
    SUBSCRIBE_PACKAGE_HEADER1 header;
    uint32_t ip;
    uint16_t port; // 客户端数据接收端口 订阅
    uint32_t subRuleArrNum;
    SUBSCRIBE_PACKAGE_SUB_RULE *subRuleArray;
} SUBSCRIBE_PACKAGE_CMD_SUB_test;

// 数据结构体
typedef struct {
    char signalFullName[200]; // 信号全名
    int signValue;
    int rawValue;
} PACKAGE_VALUE;


#endif // TESTHELP_H
