#include "fc_monitor.h"
#include "ui_fc_monitor.h"
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QFileInfo>
#include <QTextStream>
#include <QTimer>
#include <QCoreApplication>
#include "../../utils/user_session.h"

FcMonitor::FcMonitor(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::FcMonitor),
    m_monitorWidget(nullptr)
{
    ui->setupUi(this);
//    setupWidget();

//    qDebug() << "FcMonitor初始化完成";
}

FcMonitor::~FcMonitor()
{
    delete ui;
}

void FcMonitor::setupWidget()
{
    // 创建FcMonitorWidget
    m_monitorWidget = new FcMonitorWidget(this);

    // 如果UI文件中有布局，将FcMonitorWidget添加到其中
    // 否则创建一个新的布局
    QVBoxLayout* layout = qobject_cast<QVBoxLayout*>(this->layout());
    if (!layout) {
        layout = new QVBoxLayout(this);
        setLayout(layout);
    }

    layout->addWidget(m_monitorWidget);

    // 创建示例数据用于测试
    createSampleData();
}

void FcMonitor::createSampleData()
{
    // 获取用户名和方案文件路径
    QString userName = UserSession::getInstance().getUsername();
    QString schemeFileDir = QCoreApplication::applicationDirPath() + "/scheme/" + userName + "_solution1.xml";

    //QString schemeFileDir = "/scheme/" + userName + "_solution1.xml";

    qDebug() << "尝试加载FC方案文件:" << schemeFileDir;

    // 检查文件是否存在
    QFile file(schemeFileDir);
    if (!file.exists()) {
        qWarning() << "FC方案文件不存在:" << schemeFileDir;

        // 创建目录（如果不存在）
        QDir dir(QCoreApplication::applicationDirPath() + "/scheme/");
        if (!dir.exists()) {
            dir.mkpath(".");
            qDebug() << "创建方案目录:" << dir.absolutePath();
        }

        // 如果文件不存在，创建一个示例文件
        createDefaultSchemeFile(schemeFileDir);
        return;
    }

    // 读取XML文件内容
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QString errstr = file.errorString();
        qWarning() << "无法打开FC方案文件:" << schemeFileDir << ", 错误:" << errstr;
        return;
    }

    // 使用数据管理器解析XML
    if (m_monitorWidget && m_monitorWidget->m_dataManager) {
        // 从文件名提取方案名称
        QFileInfo fileInfo(schemeFileDir);
        if (m_monitorWidget->m_dataManager->parseXmlData(schemeFileDir)) {
            qDebug() << "成功解析FC方案:" ;
            // 更新方案列表
            m_monitorWidget->updateSchemeList();
        } else {
            qDebug() << "解析FC方案失败:" ;
        }
    }
}

void FcMonitor::createDefaultSchemeFile(const QString& filePath)
{
    qDebug() << "创建默认FC方案文件:" << filePath;

    // 创建默认的XML内容
    QString defaultXmlContent = R"(<?xml version="1.0" encoding="UTF-8"?>
<root>
    <solution name="默认方案" ICD_name="默认ICD" isShared="0">
        <Msg msgName="心跳消息" dstSrv="目标服务" srcSrv="源服务" interface="心跳接口" srcUnit="源单元" dstUnit="目标单元">
            <topic shortName="heartbeat" name="心跳主题">
                <Signal id="1" name="status"/>
                <Signal id="2" name="timestamp"/>
                <Signal id="3" name="system/cpu/usage"/>
                <Signal id="4" name="system/memory/usage"/>
            </topic>
            <topic shortName="status" name="状态主题">
                <Signal id="5" name="online"/>
                <Signal id="6" name="error_code"/>
            </topic>
        </Msg>
        <Msg msgName="数据消息" dstSrv="数据服务" srcSrv="采集服务" interface="数据接口" srcUnit="采集单元" dstUnit="处理单元">
            <topic shortName="sensor_data" name="传感器数据">
                <Signal id="7" name="temperature"/>
                <Signal id="8" name="pressure"/>
                <Signal id="9" name="sensors/temp/zone1"/>
                <Signal id="10" name="sensors/temp/zone2"/>
                <Signal id="11" name="sensors/pressure/main"/>
            </topic>
        </Msg>
    </solution>
</root>)";

    // 写入文件
    QFile file(filePath);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        out.setCodec("UTF-8");
        out << defaultXmlContent;
        file.close();
        qDebug() << "成功创建默认FC方案文件:" << filePath;

        // 创建文件后，重新调用createSampleData来加载它
        QTimer::singleShot(100, this, &FcMonitor::createSampleData);
    } else {
        qWarning() << "无法创建默认FC方案文件:" << filePath << ", 错误:" << file.errorString();
    }
}

void FcMonitor::loadScheme(const QString& schemeName, const QString& xmlFilePath)
{
    if (m_monitorWidget) {
        m_monitorWidget->loadScheme();
    }
}

void FcMonitor::startMonitoring(const QString& schemeName)
{
    if (m_monitorWidget) {
        m_monitorWidget->startMonitoring(schemeName);
    }
}

void FcMonitor::stopMonitoring(const QString& schemeName)
{
    if (m_monitorWidget) {
        m_monitorWidget->stopMonitoring(schemeName);
    }
}
