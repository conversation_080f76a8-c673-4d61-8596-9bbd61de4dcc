#ifndef TEMPLATELISTWIDGET_H
#define TEMPLATELISTWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QScrollArea>
#include <QNetworkAccessManager>
#include <QNetworkReply>

#include "template_list_item.h"

namespace Ui {
class TemplateListWidget;
}

class TemplateListWidget : public QWidget
{
    Q_OBJECT

public:
    explicit TemplateListWidget(QWidget *parent = nullptr);
     void refreshTemplateList();
     void setMonType(bool isGraph);

private slots:
    void onNetworkReply(QNetworkReply *reply);
    void onDownloadClicked(int id);
    void onDeleteClicked(int id);

signals:
    void templateDeleted(const QString& templateName);
    void templateDownload();

private:
    Ui::TemplateListWidget *ui;

    QVBoxLayout *m_mainLayout;
    QScrollArea *m_scrollArea;
    QWidget *m_scrollContent;
    QVBoxLayout *m_scrollLayout;
    QNetworkAccessManager *m_networkManager;
    bool m_isGraph = false;//默认等于数值监控

    void setupUI();
    void loadTemplates();
    void parseTemplateData(const QJsonArray &data);
    void uploadToServer(const QString& filePath, const QString& templateName);
};

#endif // TEMPLATELISTWIDGET_H
