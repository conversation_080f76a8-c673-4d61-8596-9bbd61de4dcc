# FC数据监控系统编译说明

## 编译环境要求

- Qt 6.0 或更高版本
- C++17 支持的编译器
- CMake 3.16+ 或 qmake

## 已修复的编译问题

### 1. QMutex const 问题
**问题**: `error: invalid conversion from 'const QBasicMutex*' to 'QBasicMutex*'`

**解决方案**: 在头文件中将 `QMutex m_dataMutex;` 改为 `mutable QMutex m_dataMutex;`

这样可以在const成员函数中使用QMutexLocker。

### 2. UI文件依赖问题
**问题**: 代码中包含了对ui文件的引用，但实际上没有使用ui文件

**解决方案**: 
- 移除了 `#include "ui_fc_monitor_widget.h"`
- 移除了 `Ui::FcMonitorWidget` 命名空间声明
- 移除了 `ui` 成员变量
- 移除了 `ui->setupUi(this)` 调用

### 3. 缺少头文件包含
**问题**: 某些类型没有正确包含头文件

**解决方案**:
- 在 `fc_data_parser.cpp` 中添加了 `#include <QSet>`
- 确保所有必要的Qt头文件都已包含

### 4. 构造函数初始化列表
**问题**: QTimer没有设置正确的父对象

**解决方案**: 将 `new QTimer()` 改为 `new QTimer(this)`

## 编译方法

### 方法1: 使用CMake

```bash
cd src/ui/FcMonitor
mkdir build
cd build
cmake ..
make
```

### 方法2: 使用qmake

```bash
cd src/ui/FcMonitor
qmake FcMonitor.pro
make
```

### 方法3: 在Qt Creator中编译

1. 打开Qt Creator
2. 选择 "打开项目"
3. 选择 `FcMonitor.pro` 文件
4. 配置编译套件
5. 点击 "构建" 按钮

## 文件结构

```
src/ui/FcMonitor/
├── fc_data_structures.h      # 数据结构定义
├── fc_data_manager.h         # 数据管理器头文件
├── fc_data_manager.cpp       # 数据管理器实现
├── fc_data_parser.h          # 数据解析器头文件
├── fc_data_parser.cpp        # 数据解析器实现
├── fc_monitor_widget.h       # 监控界面头文件
├── fc_monitor_widget.cpp     # 监控界面实现
├── fc_monitor_example.cpp    # 示例程序
├── CMakeLists.txt           # CMake构建文件
├── FcMonitor.pro            # qmake项目文件
├── README.md                # 使用说明
└── COMPILE.md               # 编译说明（本文件）
```

## 运行示例程序

编译成功后，运行示例程序：

```bash
# 如果使用CMake编译
./FcMonitorExample

# 如果使用qmake编译
./FcMonitorExample
```

示例程序将展示：
- FC监控界面的基本功能
- 两层级数据展示（功能单元消息 -> 主题）
- 实时数据模拟和更新
- 监控控制操作

## 集成到现有项目

### 1. 复制文件
将以下文件复制到您的项目中：
- `fc_data_structures.h`
- `fc_data_manager.h` 和 `fc_data_manager.cpp`
- `fc_data_parser.h` 和 `fc_data_parser.cpp`
- `fc_monitor_widget.h` 和 `fc_monitor_widget.cpp`

### 2. 添加到项目文件
在您的CMakeLists.txt或.pro文件中添加这些源文件。

### 3. 使用示例
```cpp
#include "fc_monitor_widget.h"

// 在您的主窗口或应用程序中
FcMonitorWidget* fcMonitor = new FcMonitorWidget(this);
fcMonitor->show();

// 加载方案
fcMonitor->loadScheme("方案名", "path/to/scheme.xml");

// 开始监控
fcMonitor->startMonitoring("方案名");
```

## 注意事项

1. **Qt版本兼容性**: 代码使用Qt6编写，如果需要Qt5兼容性，可能需要少量修改
2. **编译器要求**: 需要支持C++17的编译器
3. **线程安全**: 数据管理器使用了QMutex确保线程安全
4. **内存管理**: 使用了Qt的父子对象机制进行内存管理

## 故障排除

### 编译错误
- 确保Qt开发环境正确安装
- 检查CMake或qmake版本
- 确保所有头文件路径正确

### 运行时错误
- 检查XML文件格式是否正确
- 确保有足够的权限读取文件
- 查看调试输出获取详细错误信息

### 性能问题
- 调整更新频率（UPDATE_INTERVAL）
- 限制显示的数据量
- 使用发布版本编译以获得更好性能

## 技术支持

如果遇到编译或使用问题，请检查：
1. Qt版本和编译器兼容性
2. 所有依赖库是否正确安装
3. 文件路径和权限设置
4. 查看README.md获取更多使用说明
