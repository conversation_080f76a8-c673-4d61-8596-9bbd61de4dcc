#include "unitallocation.h"
#include "ui_unitAllocation.h"
#include <QHeaderView>
#include <QPushButton>
#include <QHBoxLayout>
#include <QTableWidgetItem>
#include <QLineEdit>
#include <QDebug>

unitAllocation::unitAllocation(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::unitAllocation)
{
    ui->setupUi(this);

    // 设置交替行颜色
    ui->tableWidget->setAlternatingRowColors(true);
    ui->tableWidget->setItemDelegateForColumn(0, new FirstColumnDelegate(ui->tableWidget));  // 第一列使用自定义 Delegate

    // 设置表格的列宽
    ui->tableWidget->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Interactive); // 类型列
    ui->tableWidget->horizontalHeader()->setSectionResizeMode(1, QHeaderView::Interactive); // code列
    ui->tableWidget->horizontalHeader()->setSectionResizeMode(2, QHeaderView::Stretch);     // 名称列，自动拉伸
    ui->tableWidget->horizontalHeader()->setSectionResizeMode(3, QHeaderView::Interactive); // 系数列
    ui->tableWidget->horizontalHeader()->setSectionResizeMode(4, QHeaderView::Interactive); // 操作列

    // 设置初始列宽
    ui->tableWidget->setColumnWidth(0, 150); // 类型
    ui->tableWidget->setColumnWidth(1, 120); // code
    // 名称列会自动拉伸，不需要设置
    ui->tableWidget->setColumnWidth(3, 80);  // 系数
    ui->tableWidget->setColumnWidth(4, 120); // 操作

    // 自适应行高
    ui->tableWidget->verticalHeader()->setDefaultSectionSize(36);
    ui->tableWidget->verticalHeader()->setVisible(false); // 隐藏行号

    // 设置选择模式
    ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);

    // 连接按钮信号
    connect(ui->addButton, &QPushButton::clicked, this, &unitAllocation::addNewTypeGroup);

    // 添加示例数据
    addExampleData();
}

unitAllocation::~unitAllocation()
{
    delete ui;
}

void unitAllocation::addExampleData()
{
    // 添加加速度数据组
    addTypeGroup("加速度", {
                               {"901", "横向", "1*2"},
                               {"902", "竖向", "1*3"},
                               {"903", "滚动", "1*4"}
                           });

    // 添加第二组示例数据
    addTypeGroup("温度", {
                             {"801", "发动机", "2.5"},
                             {"802", "电池", "1.8"}
                         });

    // 添加第三组示例数据
    addTypeGroup("压力", {
                             {"701", "油压", "0.8"},
                             {"702", "气压", "1.2"},
                             {"703", "水压", "1.5"}
                         });
}

void unitAllocation::addTypeGroup(const QString &typeName, const QVector<QStringList> &items)
{
    if (items.isEmpty()) {
        return;
    }

    int startRow = ui->tableWidget->rowCount();

    for (int i = 0; i < items.size(); i++) {
        int row = ui->tableWidget->rowCount();
        ui->tableWidget->insertRow(row);

        // 只在第一行显示类型名称
        if (i == 0) {
            QTableWidgetItem *typeItem = new QTableWidgetItem(typeName);
            typeItem->setFlags(typeItem->flags() & ~Qt::ItemIsEditable); // 不可编辑
            ui->tableWidget->setItem(row, 0, typeItem);
        } else {
            // 类型列留空，表示与上面的类型相关
            QTableWidgetItem *emptyItem = new QTableWidgetItem("");
            emptyItem->setFlags(emptyItem->flags() & ~Qt::ItemIsEditable); // 不可编辑
            ui->tableWidget->setItem(row, 0, emptyItem);
        }

        // 设置code
        QTableWidgetItem *codeItem = new QTableWidgetItem(items[i][0]);
        codeItem->setFlags(codeItem->flags() & ~Qt::ItemIsEditable); // 不可编辑
        ui->tableWidget->setItem(row, 1, codeItem);

        // 设置名称
        QTableWidgetItem *nameItem = new QTableWidgetItem(items[i][1]);
        nameItem->setFlags(nameItem->flags() & ~Qt::ItemIsEditable); // 不可编辑
        ui->tableWidget->setItem(row, 2, nameItem);

        // 设置系数(可编辑)
        QTableWidgetItem *coeffItem = new QTableWidgetItem(items[i][2]);
        ui->tableWidget->setItem(row, 3, coeffItem);

        // 添加操作按钮
        addOperationButtons(row);
    }

    // 合并第一列的单元格
    if (items.size() > 1) {
        ui->tableWidget->setSpan(startRow, 0, items.size(), 1);
    }
}

void unitAllocation::addOperationButtons(int row)
{
    // 创建一个widget作为按钮容器
    QWidget *buttonWidget = new QWidget();
    QHBoxLayout *layout = new QHBoxLayout(buttonWidget);
    layout->setContentsMargins(2, 2, 2, 2);
    layout->setSpacing(4);

    // 创建新增按钮
    QPushButton *addBtn = new QPushButton("新增");
    addBtn->setMaximumHeight(24);
    addBtn->setMinimumWidth(40);
    addBtn->setStyleSheet(
        "QPushButton { background-color: #52c41a; color: white; border-radius: 2px; }"
        "QPushButton:hover { background-color: #73d13d; }"
        "QPushButton:pressed { background-color: #389e0d; }"
        );

    // 创建删除按钮
    QPushButton *delBtn = new QPushButton("删除");
    delBtn->setMaximumHeight(24);
    delBtn->setMinimumWidth(40);
    delBtn->setStyleSheet(
        "QPushButton { background-color: #ff4d4f; color: white; border-radius: 2px; }"
        "QPushButton:hover { background-color: #ff7875; }"
        "QPushButton:pressed { background-color: #cf1322; }"
        );

    // 添加按钮到布局
    layout->addWidget(addBtn);
    layout->addWidget(delBtn);

    // 设置widget到表格
    ui->tableWidget->setCellWidget(row, 4, buttonWidget);

    // 连接信号
    connect(addBtn, &QPushButton::clicked, [this, buttonWidget]() {
        // 获取按钮所在的实际行号
        int currentRow = ui->tableWidget->indexAt(buttonWidget->pos()).row();
        if (currentRow >= 0) {
            this->onAddButtonClicked(currentRow);
        }
    });

    // 使用 QSignalMapper 来确保正确的行号
    connect(delBtn, &QPushButton::clicked, [this, buttonWidget]() {
        // 获取按钮所在的实际行号
        int currentRow = ui->tableWidget->indexAt(buttonWidget->pos()).row();
        if (currentRow >= 0) {
            this->onDeleteButtonClicked(currentRow);
        }
    });
}

void unitAllocation::addNewTypeGroup()
{
    AddTypeDialog dialog(this);
    if (dialog.exec() != QDialog::Accepted) {
        return;
    }

    // 获取对话框中的数据
    QString type = dialog.getType();
    QString code = dialog.getCode();
    QString name = dialog.getName();
    QString coefficient = dialog.getCoefficient();

    // 查找是否存在相同类型
    int existingTypeRow = -1;
    int lastRowOfType = -1;

    for (int i = 0; i < ui->tableWidget->rowCount(); i++) {
        QTableWidgetItem *typeItem = ui->tableWidget->item(i, 0);
        if (typeItem && !typeItem->text().isEmpty()) {
            if (typeItem->text() == type) {
                existingTypeRow = i;
            }
        }
        // 如果找到了匹配的类型，继续向下查找直到找到下一个非空类型或到达表格末尾
        if (existingTypeRow != -1) {
            QTableWidgetItem *nextTypeItem = ui->tableWidget->item(i, 0);
            if (nextTypeItem && !nextTypeItem->text().isEmpty() && i != existingTypeRow) {
                lastRowOfType = i - 1;
                break;
            }
            if (i == ui->tableWidget->rowCount() - 1) {
                lastRowOfType = i;
            }
        }
    }

    int insertRow;
    if (existingTypeRow != -1) {
        // 如果找到相同类型，在该类型组的最后插入
        insertRow = lastRowOfType + 1;
        ui->tableWidget->insertRow(insertRow);

        // 添加空的类型单元格
        QTableWidgetItem *emptyTypeItem = new QTableWidgetItem("");
        emptyTypeItem->setFlags(emptyTypeItem->flags() & ~Qt::ItemIsEditable); // 不可编辑
        ui->tableWidget->setItem(insertRow, 0, emptyTypeItem);
    } else {
        // 如果是新类型，在表格末尾添加
        insertRow = ui->tableWidget->rowCount();
        ui->tableWidget->insertRow(insertRow);

        // 添加类型单元格
        QTableWidgetItem *typeItem = new QTableWidgetItem(type);
        typeItem->setFlags(typeItem->flags() & ~Qt::ItemIsEditable); // 不可编辑
        ui->tableWidget->setItem(insertRow, 0, typeItem);
    }

    // 设置code
    QTableWidgetItem *codeItem = new QTableWidgetItem(code);
    codeItem->setFlags(codeItem->flags() & ~Qt::ItemIsEditable); // 不可编辑
    ui->tableWidget->setItem(insertRow, 1, codeItem);

    // 设置名称
    QTableWidgetItem *nameItem = new QTableWidgetItem(name);
    nameItem->setFlags(nameItem->flags() & ~Qt::ItemIsEditable); // 不可编辑
    ui->tableWidget->setItem(insertRow, 2, nameItem);

    // 设置系数
    QTableWidgetItem *coeffItem = new QTableWidgetItem(coefficient);
    ui->tableWidget->setItem(insertRow, 3, coeffItem);

    // 添加操作按钮
    addOperationButtons(insertRow);

    // 更新类型单元格的合并
    if (existingTypeRow != -1) {
        updateTypeSpan(existingTypeRow);
    } else {
        updateTypeSpan(insertRow);
    }
}

void unitAllocation::onAddButtonClicked(int row)
{
    // 获取当前行所属的类型
    QString typeName;
    int typeRow = row;

    // 找到当前行所属的类型行
    while (typeRow >= 0) {
        QTableWidgetItem *typeItem = ui->tableWidget->item(typeRow, 0);
        if (typeItem && !typeItem->text().isEmpty()) {
            typeName = typeItem->text();
            break;
        }
        typeRow--;
    }

    if (typeName.isEmpty()) {
        qDebug() << "无法找到所属类型";
        return;
    }

    // 插入新行
    int newRow = row + 1;
    ui->tableWidget->insertRow(newRow);

    // 空类型单元格
    QTableWidgetItem *emptyTypeItem = new QTableWidgetItem("");
    emptyTypeItem->setFlags(emptyTypeItem->flags() & ~Qt::ItemIsEditable);
    ui->tableWidget->setItem(newRow, 0, emptyTypeItem);

    // 设置code
    QTableWidgetItem *codeItem = new QTableWidgetItem("新Code");
    ui->tableWidget->setItem(newRow, 1, codeItem);

    // 设置名称
    QTableWidgetItem *nameItem = new QTableWidgetItem("新名称");
    ui->tableWidget->setItem(newRow, 2, nameItem);

    // 设置系数(可编辑)
    QTableWidgetItem *coeffItem = new QTableWidgetItem("1.0");
    ui->tableWidget->setItem(newRow, 3, coeffItem);

    // 添加操作按钮
    addOperationButtons(newRow);

    // 更新类型单元格的合并
    updateTypeSpan(typeRow);

    // 让用户立即编辑名称
    ui->tableWidget->editItem(nameItem);
}

void unitAllocation::onDeleteButtonClicked(int row)
{
    // 获取当前行所属的类型
    QString typeName;
    int typeRow = row;
    int spanCount = 0;  // 初始化为0而不是1

    // 找到当前行所属的类型行
    while (typeRow >= 0) {
        QTableWidgetItem *typeItem = ui->tableWidget->item(typeRow, 0);
        if (typeItem && !typeItem->text().isEmpty()) {
            typeName = typeItem->text();
            break;
        }
        typeRow--;
    }

    if (typeName.isEmpty()) {
        qDebug() << "无法找到所属类型";
        return;
    }

    // 获取当前类型的行数以及该类型的结束位置
    int endRow = typeRow;  // 初始化为类型行本身
    for (int i = typeRow; i < ui->tableWidget->rowCount(); i++) {
        QTableWidgetItem *item = ui->tableWidget->item(i, 0);
        // 当我们找到下一个类型行（非空文本）时，说明当前类型组已经结束
        if (i > typeRow && item && !item->text().isEmpty()) {
            // 找到了下一个类型行，但不包括这一行
            break;
        }
        endRow = i;  // 更新当前类型组的最后一行
        spanCount++;
    }

    // 确保要删除的行属于当前类型组
    if (row < typeRow || row > endRow) {
        qDebug() << "要删除的行不属于当前类型组";
        return;
    }

    // 如果要删除的是类型行（第一行），且还有其他行
    if (row == typeRow && spanCount > 1) {
        // 将类型名称移动到下一行
        QTableWidgetItem *nextItem = new QTableWidgetItem(typeName);
        nextItem->setFlags(nextItem->flags() & ~Qt::ItemIsEditable); // 不可编辑
        ui->tableWidget->setItem(row + 1, 0, nextItem);
    }

    // 移除行
    ui->tableWidget->removeRow(row);

    // 如果删除的是类型行，需要更新typeRow
    if (row == typeRow && spanCount > 1) {
        typeRow = row; // 因为删除了一行，所以新的类型行就在原来的位置
    }

    // 更新类型单元格的合并
    // 即使只剩下一行也需要更新合并单元格
    updateTypeSpan(typeRow);
}

void unitAllocation::updateTypeSpan(int typeRow)
{
    // 检查表格是否为空
    if (ui->tableWidget->rowCount() == 0) {
        return;
    }

    // 确保类型行有效
    if (typeRow < 0 || typeRow >= ui->tableWidget->rowCount()) {
        return;
    }

    // 检查类型行是否仍然存在且包含类型名称
    QTableWidgetItem *typeItem = ui->tableWidget->item(typeRow, 0);
    if (!typeItem || typeItem->text().isEmpty()) {
        return;
    }

    // 计算当前类型组有多少行
    int spanCount = 1;
    for (int i = typeRow + 1; i < ui->tableWidget->rowCount(); i++) {
        QTableWidgetItem *item = ui->tableWidget->item(i, 0);
        if (item && !item->text().isEmpty()) {
            // 找到了下一个类型行
            break;
        }
        spanCount++;
    }

    // 清除之前的合并状态
    ui->tableWidget->setSpan(typeRow, 0, 1, 1);

    // 更新合并单元格
    if (spanCount > 1) {
        ui->tableWidget->setSpan(typeRow, 0, spanCount, 1);
    }
}
