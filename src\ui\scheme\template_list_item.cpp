#include "template_list_item.h"
#include "ui_template_list_item.h"
#include <QIcon>
#include <QMessageBox>
#include <QDomDocument>
#include <QDomElement>
#include <QDomNodeList>
#include <QFileInfo>

#include "utils/user_session.h"
#include "utils/api_url_manager.h"
#include "utils/file_list_service.h"
#include "third/parseAndAnalysis.h"

TemplateListItem::TemplateListItem(QWidget *parent):
    QWidget(parent),
    m_templateId(0),
    ui(new Ui::TemplateListItem)
{
    ui->setupUi(this);
    setupUI();
}

void TemplateListItem::setupUI()
{
    m_nameLabel = ui->schemeNameLabel;
    m_nameLabel->setStyleSheet("font-size: 14px; color: #333333;");
    m_nameLabel->setMinimumWidth(100);

    m_userNameLabel = ui->userNameLabel;
    m_userNameLabel->setStyleSheet("font-size: 12px; color: #666666;");
    m_userNameLabel->setMinimumWidth(30);

    m_createTimeLabel = ui->timeLabel;
    m_createTimeLabel->setStyleSheet("font-size: 12px; color: #666666;");
    m_createTimeLabel->setMinimumWidth(80);

    m_downloadBtn = ui->downLoadBtn;
    m_downloadBtn->setIcon(QIcon(":/images/arrow-down.png"));
    m_downloadBtn->setIconSize(QSize(20, 20));
    m_downloadBtn->setFixedSize(32, 32);
    m_downloadBtn->setStyleSheet(
        "QPushButton { border: none; background-color: transparent; }"
        "QPushButton:hover { background-color: #e6f7ff; border-radius: 16px; }"
        "QPushButton:pressed { background-color: #bae7ff; border-radius: 16px; }"
    );

    m_deleteBtn = ui->deleteBtn;
    m_deleteBtn->setIcon(QIcon(":/images/delete.png"));
    m_deleteBtn->setIconSize(QSize(20, 20));
    m_deleteBtn->setFixedSize(32, 32);
    m_deleteBtn->setStyleSheet(
        "QPushButton { border: none; background-color: transparent; }"
        "QPushButton:hover { background-color: #fff1f0; border-radius: 16px; }"
        "QPushButton:pressed { background-color: #ffccc7; border-radius: 16px; }"
    );



    connect(m_downloadBtn, &QPushButton::clicked,this, &TemplateListItem::onDownloadClicked);

    connect(m_deleteBtn, &QPushButton::clicked,this,&TemplateListItem::onDeleteClicked);

    m_networkManager = new QNetworkAccessManager(this);
//    connect(m_networkManager, &QNetworkAccessManager::finished,
//            this, &TemplateListItem::onNetworkReply);


}

void TemplateListItem::setTemplateInfo(const QString &name, const QString &userName,
                                     const QString &createTime, int id, bool isCurrentUser,
                                     const QString &url)
{
    m_name = name;
    m_templateId = id;
    m_url = url;

    m_nameLabel->setText(name);
    m_userNameLabel->setText(userName);
    m_createTimeLabel->setText(createTime);

    // 只有当前用户的模板才显示删除按钮
    m_deleteBtn->setVisible(isCurrentUser);
}
void TemplateListItem::onDownloadClicked()
{

    // 查找对应的模板项
    QUrl url(m_url);
    QString templateName = ui->schemeNameLabel->text();
    if (!url.isValid()) {
        qWarning() << "文件URL无效:" << m_url;
        QMessageBox::warning(this, tr("下载失败"),
                             tr("方案 %1 的URL无效").arg(templateName));
        return;
    }


    // 创建网络请求
    QNetworkRequest request(url);
    request.setRawHeader("accept", "*/*");
    QString token = UserSession::getInstance().getToken();
    if (!token.isEmpty()) {
        request.setRawHeader("Authorization", token.toUtf8());
    }

    // 发送下载请求
    QNetworkReply* reply = m_networkManager->get(request);

    // 连接下载完成的信号
    connect(reply, &QNetworkReply::finished, [this, reply, templateName]() {
        if (reply->error() == QNetworkReply::NoError) {
            // 获取下载的数据
            QByteArray data = reply->readAll();

            // 获取用户名和本地文件路径
            QString userName = UserSession::getInstance().getUsername();
            QString localPath;
            if(!m_isGraph)
            {
                localPath = QCoreApplication::applicationDirPath() + "/scheme/" + userName + "_solution.xml";
            }
            else {
                 localPath = QCoreApplication::applicationDirPath() + "/scheme/" + userName + "_graph_solution.xml";
            }

            // 读取现有XML文件
            QDomDocument doc;
            QFile file(localPath);
            if (file.exists()) {
                if (!file.open(QIODevice::ReadOnly)) {
                    QMessageBox::warning(this, tr("错误"), tr("无法打开方案文件"));
                    reply->deleteLater();
                    return;
                }
                QString errorMsg;
                int errorLine, errorColumn;
                if (!doc.setContent(&file, &errorMsg, &errorLine, &errorColumn)) {
                    file.close();
                    QMessageBox::warning(this, tr("错误"),
                                         tr("XML解析错误：%1\n行：%2，列：%3").arg(errorMsg).arg(errorLine).arg(errorColumn));
                    reply->deleteLater();
                    return;
                }
                file.close();
            } else {
                // 如果文件不存在，创建新的XML文档
                QDomElement root = doc.createElement("root");
                doc.appendChild(root);
            }

            // 解析下载的XML数据
            QDomDocument downloadedDoc;
            if (!downloadedDoc.setContent(data)) {
                QMessageBox::warning(this, tr("错误"), tr("无法解析下载的XML数据"));
                reply->deleteLater();
                return;
            }

            // 获取下载文档中的solution元素
            QDomElement downloadedRoot = downloadedDoc.documentElement();
            QDomNodeList solutions = downloadedRoot.elementsByTagName("solution");

            // 将下载的solution添加到现有文档中
            QDomElement root = doc.documentElement();
            for (int i = 0; i < solutions.count(); i++) {
                QDomElement solution = solutions.at(i).toElement();
                // 检查是否存在同名solution
                QDomNodeList existingSolutions = root.elementsByTagName("solution");
                bool exists = false;
                for (int j = 0; j < existingSolutions.count(); j++) {
                    QDomElement existingSolution = existingSolutions.at(j).toElement();
                    if (existingSolution.attribute("name") == solution.attribute("name") &&
                            existingSolution.attribute("ICD_name") == solution.attribute("ICD_name")) {
                        exists = true;
                        break;
                    }
                }

                if (!exists) {
                    // 导入新的solution节点
                    QDomElement importedSolution = doc.importNode(solution, true).toElement();
                    root.appendChild(importedSolution);
                }
            }

            // 保存XML文件
            if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
                QMessageBox::warning(this, tr("错误"), tr("无法保存方案文件"));
                return;
            }

            // 保存合并后的XML内容
            QTextStream out(&file);
            out.setCodec("UTF-8");
            doc.save(out, 4); // 4是缩进空格数
            QString content = doc.toString(4);
            file.close();

            // 上传到服务器
            uploadToServer(content, QFileInfo(localPath).fileName());

        } else {
            QMessageBox::warning(this, tr("下载失败"),
                                 tr("下载模板失败: %1").arg(reply->errorString()));
        }
        reply->deleteLater();
    });

    emit downloadClicked(m_templateId);
}
void TemplateListItem::onDeleteClicked()
{
    // 查找对应的模板项
    QString templateName = ui->schemeNameLabel->text();

    // 创建网络请求
    QNetworkRequest request;
    if(!m_isGraph)
    {
        request = QNetworkRequest(ApiUrlManager::getInstance().getUrl(ApiUrlManager::SCHEME_UNSHARE));
    }else {
        request = QNetworkRequest(ApiUrlManager::getInstance().getUrl(ApiUrlManager::GRAPH_TEMPLATE_DEL));
    }
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    QString token = UserSession::getInstance().getToken();
    if (!token.isEmpty()) {
        request.setRawHeader("Authorization", token.toUtf8());
    }

    // 获取用户名和ICD版本
    QString username = UserSession::getInstance().getUsername();
    void *m_decoderToken = FileListService::getInstance().getParseandanalysisP();
    // 从XML中获取ICD版本
    QString icdVersion = QString::fromStdString(getCurrentIcdVersion(m_decoderToken));

    // 准备请求数据
    QJsonObject jsonObj;
    jsonObj["content"] = "";
    jsonObj["icdVersion"] = icdVersion;
    jsonObj["id"] = 0;
    jsonObj["name"] = templateName;
    jsonObj["type"] = 1;
    jsonObj["userId"] = UserSession::getInstance().getUserId();
    jsonObj["userName"] = UserSession::getInstance().getUsername();

    QJsonDocument jsonDoc(jsonObj);
    QByteArray jsonData = jsonDoc.toJson();

    // 发送删除请求
    QNetworkReply* reply = m_networkManager->sendCustomRequest(request, "DELETE",jsonData);

    // 连接请求完成的信号
    connect(reply, &QNetworkReply::finished, [reply,this,templateName]() {
        //QScopedPointer<QNetworkReply, QScopedPointerDeleteLater> replyPtr(reply);
        if (reply->error() == QNetworkReply::NoError) {
            QByteArray response = reply->readAll();
            QJsonDocument doc = QJsonDocument::fromJson(response);
            QJsonObject obj = doc.object();

            if (obj["success"].toBool()) {
//                QMessageBox::information(this, tr("删除成功"),
//                                         tr("模板 %1 已成功删除").arg(templateName));
                unShared();
                emit deleteClicked(m_templateId);
                //如果本地有该方案，修改方案列表该方案的状态
            } else {
                QMessageBox::warning(this, tr("删除失败"),
                                     tr("删除模板失败: %1").arg(obj["message"].toString()));
            }
        } else {
            QMessageBox::warning(this, tr("删除失败"),
                                 tr("删除模板失败: %1").arg(reply->errorString()));
        }
        reply->deleteLater();
    });

}

void TemplateListItem::onNetworkReply(QNetworkReply *reply)
{
    if (reply->error() == QNetworkReply::NoError) {
        QByteArray data = reply->readAll();
        QJsonDocument doc = QJsonDocument::fromJson(data);
        QJsonObject obj = doc.object();

        if (obj["success"].toBool()) {
            QJsonArray templateArray = obj["data"].toArray();
        }
    }
    reply->deleteLater();
}

void TemplateListItem::uploadToServer(const QString &content, const QString &fileName)
{
    // 获取用户会话数据
    QString token = UserSession::getInstance().getToken();

    // 创建网络请求
    QNetworkRequest request;
    if(!m_isGraph)
    {
        request = QNetworkRequest(ApiUrlManager::getInstance().getUrl(ApiUrlManager::SCHEME_UPDATE));
    }else {
        request = QNetworkRequest(ApiUrlManager::getInstance().getUrl(ApiUrlManager::GRAPH_SCHEME_UPDATE));
    }

    request.setRawHeader("accept", "*/*");
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    if (!token.isEmpty()) {
        request.setRawHeader("Authorization", token.toUtf8());
    }

    // 创建 JSON 数据
    QJsonObject jsonObj;
    jsonObj["content"] = content;
    jsonObj["id"] = 0;  // 按接口要求设置为0
    jsonObj["name"] = fileName;
    jsonObj["type"] = "solutions";
    jsonObj["userId"] =  UserSession::getInstance().getUserId();

    // 添加用户ID（如果可用）
    if (UserSession::getInstance().isLoggedIn() && UserSession::getInstance().getUserId() > 0) {
        jsonObj["userId"] = UserSession::getInstance().getUserId();
    }

    QJsonDocument jsonDoc(jsonObj);
    QByteArray jsonData = jsonDoc.toJson();

    // 发送 POST 请求
    // 发送上传请求
    //QNetworkAccessManager *networkManager = new QNetworkAccessManager(this);
    QNetworkReply* reply = m_networkManager->post(request, jsonData);

    // 连接请求完成的信号
    connect(reply, &QNetworkReply::finished, [this, reply, fileName]() {
        if (reply->error() == QNetworkReply::NoError) {
            QByteArray response = reply->readAll();
            QJsonDocument doc = QJsonDocument::fromJson(response);
            QJsonObject obj = doc.object();

            if (obj["success"].toBool()) {
//                QMessageBox::information(this, tr("上传成功"),
//                    tr("模板 %1 已成功上传到服务器").arg(fileName));
            } else {
                QMessageBox::warning(this, tr("上传失败"),
                    tr("上传模板失败: %1").arg(obj["message"].toString()));
            }
        } else {
            QMessageBox::warning(this, tr("上传失败"),
                tr("上传模板失败: %1").arg(reply->errorString()));
        }
        reply->deleteLater();
    });
}

void TemplateListItem::unShared()
{
    QString schemeName = ui->schemeNameLabel->text();
    QString userName = UserSession::getInstance().getUsername();
    QString filePath;
    if(!m_isGraph)
    {
        filePath = QCoreApplication::applicationDirPath() + "/scheme/" + userName + "_solution.xml";
    }
    else {
        filePath = QCoreApplication::applicationDirPath() + "/scheme/" + userName + "_graph_solution.xml";
    }


    // 获取ICD版本
    void *m_decoderToken = FileListService::getInstance().getParseandanalysisP();
    QString icdVersion = QString::fromStdString(getCurrentIcdVersion(m_decoderToken));

    // 打开XML文件
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QMessageBox::warning(this, tr("错误"), tr("无法打开方案文件"));
        return;
    }

    // 读取XML内容
    QDomDocument doc;
    QString errorMsg;
    int errorLine, errorColumn;
    if (!doc.setContent(&file, &errorMsg, &errorLine, &errorColumn)) {
        file.close();
        QMessageBox::warning(this, tr("错误"),
                             tr("XML解析错误：%1\n行：%2，列：%3").arg(errorMsg).arg(errorLine).arg(errorColumn));
        return;
    }
    file.close();

    // 查找指定名称的方案
    QDomElement root = doc.documentElement();
    QDomNodeList solutions = root.elementsByTagName("solution");
    bool found = false;

    for (int i = 0; i < solutions.count(); i++) {
        QDomElement solution = solutions.at(i).toElement();
        if (solution.attribute("name") == schemeName && solution.attribute("ICD_name") == icdVersion) {
            // 切换分享状态
            solution.setAttribute("isShared", 0);
            found = true;
            break;
        }
    }

    if (!found) {
        QMessageBox::warning(this, tr("错误"), tr("未找到方案：%1").arg(schemeName));
        return;
    }

    // 重新打开文件用于写入
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::warning(this, tr("错误"), tr("无法打开文件进行写入"));
        return;
    }

    // 创建文本流
    QTextStream out(&file);
    out.setCodec("UTF-8");  // 设置编码为UTF-8

    // 写入XML内容
    doc.save(out, 4);  // 4是缩进空格数
    QString content = doc.toString(4);
    file.close();

//    // 更新按钮图标
//    ui->shareBtn->setIcon(QIcon(m_isShared ? ":/images/share-blue.png" : ":/images/share.png"));

    // 上传整个文件到服务器
    QString fileName = QFileInfo(filePath).fileName();
    uploadToServer(content, fileName);
}
