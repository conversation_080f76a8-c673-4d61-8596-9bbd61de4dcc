﻿#include "reset_dialog.h"
#include <QMessageBox>

ResetDialog::ResetDialog(QWidget *parent)
    : QDialog(parent)
    , passwordEditNew(new QLineEdit(this))
    , passwordEditConfirm(new QLineEdit(this))
    , confirmBtn(new QPushButton("确认", this))
    , cancelBtn(new QPushButton("取消", this))
    , mainLayout(new QVBoxLayout(this))
    , formLayout(new QFormLayout())
    , buttonLayout(new QHBoxLayout())
{
    setupUI();
    setupConnections();
}

ResetDialog::~ResetDialog()
{

}

void ResetDialog::setupUI()
{
    // 设置窗口标题和属性
    setWindowTitle("添加用户");
    setFixedSize(400, 300);
    setModal(true);
    // setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);
    setWindowFlags(Qt::Dialog | Qt::WindowTitleHint | Qt::WindowCloseButtonHint);
    // 设置表单布局
    formLayout->setContentsMargins(5, 5, 5, 5);
    formLayout->setSpacing(10);  // 减小表单项之间的间距

    // 创建并设置每个输入项的标签样式

    QLabel *passwordLabelNew = new QLabel("新密码", this);
    QLabel *passwordLabel = new QLabel("确认新密码", this);

    // 设置标签样式 - 蓝色背景，白色文本，固定高度
    QString labelStyle = "QLabel { background-color: #1890ff; color: white; padding: 6px; min-width: 60px; min-height: 20px; font-weight: bold; text-align: center; }";
    passwordLabelNew->setStyleSheet(labelStyle);
    passwordLabel->setStyleSheet(labelStyle);

    // 固定标签大小
    passwordLabelNew->setFixedSize(60, 32);
    passwordLabel->setFixedSize(60, 32);;

    // 设置标签对齐方式
    passwordLabelNew->setAlignment(Qt::AlignCenter);
    passwordLabel->setAlignment(Qt::AlignCenter);

    // 设置输入框样式和大小
    QString inputStyle = "QLineEdit { border: 1px solid #d9d9d9; border-radius: 0; padding: 6px; min-height: 20px; }";
    passwordEditNew->setStyleSheet(inputStyle);
    passwordEditNew->setEchoMode(QLineEdit::Password);
    passwordEditConfirm->setStyleSheet(inputStyle);
    passwordEditConfirm->setEchoMode(QLineEdit::Password);  // 密码输入框显示为圆点

    // 固定输入框高度
    passwordEditNew->setFixedHeight(32);
    passwordEditConfirm->setFixedHeight(32);


    // 添加表单项
    formLayout->addRow(passwordLabelNew, passwordEditNew);
    formLayout->addRow(passwordLabel, passwordEditConfirm);

    // 设置按钮样式
    confirmBtn->setStyleSheet(
        "QPushButton { background-color: #1890ff; color: white; border: none; border-radius: 4px; padding: 6px 16px; font-weight: bold; min-width: 80px; min-height: 32px; }"
        "QPushButton:hover { background-color: #40a9ff; }"
        "QPushButton:pressed { background-color: #096dd9; }"
        );

    cancelBtn->setStyleSheet(
        "QPushButton { background-color: white; color: #595959; border: 1px solid #d9d9d9; border-radius: 4px; padding: 6px 16px; min-width: 80px; min-height: 32px; }"
        "QPushButton:hover { background-color: #fafafa; color: #40a9ff; border-color: #40a9ff; }"
        );

    // 固定按钮高度
    confirmBtn->setFixedHeight(32);
    cancelBtn->setFixedHeight(32);

    // 设置按钮布局
    buttonLayout->addStretch();
    buttonLayout->addWidget(cancelBtn);
    buttonLayout->addWidget(confirmBtn);
    buttonLayout->setSpacing(10);
    buttonLayout->setContentsMargins(20, 10, 20, 20);

    // 设置主布局
    mainLayout->addLayout(formLayout);
    mainLayout->addStretch();
    mainLayout->addLayout(buttonLayout);
    setLayout(mainLayout);

    // 设置整体窗口样式
    setStyleSheet("QDialog { background-color: white; }");
}

void ResetDialog::setupConnections()
{
    connect(confirmBtn, &QPushButton::clicked, this, &ResetDialog::onConfirmClicked);
    connect(cancelBtn, &QPushButton::clicked, this, &ResetDialog::onCancelClicked);
}

void ResetDialog::onConfirmClicked()
{
    if (passwordEditNew->text().isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请输入新密码");
        passwordEditNew->setFocus();
        return;
    }

    if (passwordEditConfirm->text().isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请输入确认密码");
        passwordEditConfirm->setFocus();
        return;
    }
    // 输入验证通过，接受对话框
    accept();
}

void ResetDialog::onCancelClicked()
{
    // 取消对话框
    reject();
}


QString ResetDialog::getNewPassword() const
{
    return passwordEditNew->text();
}

QString ResetDialog::getconfirmPassword() const
{
    return passwordEditConfirm->text();
}

