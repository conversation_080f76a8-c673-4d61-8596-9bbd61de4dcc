#ifndef UNSUBSCRIBE_HANDLER_H
#define UNSUBSCRIBE_HANDLER_H

#include "command_handler.h"

/**
 * @brief 取消订阅命令处理器
 */
class UnsubscribeCommandHandler : public CommandHandler
{
    Q_OBJECT

public:
    explicit UnsubscribeCommandHandler(QObject *parent = nullptr);
    ~UnsubscribeCommandHandler() override;
    CommandHandler::CommandResult processCommand(const QByteArray &data) override;

private:
    // 可以添加特定于取消订阅命令的私有成员和方法
};

#endif // UNSUBSCRIBE_HANDLER_H 
