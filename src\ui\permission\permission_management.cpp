#include "permission_management.h"
#include "ui_permissionmanagement.h"
#include <QMessageBox>
#include <QScrollBar>
#include <QCheckBox>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QListWidgetItem>
#include <QListWidget>
#include <QInputDialog>
#include <QComboBox>
#include <QLabel>
#include <QDebug>

PermissionManagement::PermissionManagement(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::PermissionManagement)
    , permissionModel(new QStandardItemModel(this))
    , roleList({"管理员", "普通用户", "访客"})
{
    ui->setupUi(this);
    
    // 初始化角色列表
    initializeRoleList();
    
    // 初始化树形视图
    initializePermissionTree();
    
    // 建立控件连接
    setupConnections();
    
    // 默认选中第一个角色（管理员）
    if (ui->roleListWidget->count() > 0) {
        ui->roleListWidget->setCurrentRow(0);
        loadRolePermissionData(roleList.first());
    }
}

PermissionManagement::~PermissionManagement()
{
    delete ui;
}

void PermissionManagement::setupConnections()
{
    connect(ui->roleListWidget, &QListWidget::itemClicked, this, &PermissionManagement::onRoleListItemClicked);
    connect(permissionModel, &QStandardItemModel::itemChanged, this, &PermissionManagement::onFunctionItemChanged);
    connect(ui->saveButton, &QPushButton::clicked, this, &PermissionManagement::onSaveButtonClicked);
    connect(ui->addRoleButton, &QPushButton::clicked, this, &PermissionManagement::onAddRoleButtonClicked);
}

void PermissionManagement::initializeRoleList()
{
    // 清空角色列表
    ui->roleListWidget->clear();
    
    // 添加预定义角色
    for (const QString &role : roleList) {
        QListWidgetItem *item = new QListWidgetItem(role, ui->roleListWidget);
        item->setTextAlignment(Qt::AlignCenter);
        ui->roleListWidget->addItem(item);
    }
}

void PermissionManagement::initializePermissionTree()
{
    // 设置模型标题
    QStringList headers;
    headers << "功能范围" << "操作权限" << "数据范围";
    permissionModel->setHorizontalHeaderLabels(headers);
    
    // 将模型设置到树视图
    ui->permissionTreeView->setModel(permissionModel);
    
    // 定义功能组和操作
    QStringList operationTypes;
    operationTypes << "模板生成" << "模板编辑" << "分享到模板库";
    
    // 添加主机总览组
    QStringList mainHostFunctions;
    mainHostFunctions << "数值监控" << "图像监控" << "时序图" << "自动测试" << "视频监控" << "文件配置" << "数据装订";
    addFunctionGroup("主机总览", mainHostFunctions, operationTypes);
    
    // 添加管理中心组
    QStringList managementFunctions;
    managementFunctions << "用户管理" << "权限管理" << "用户日志" << "系统日志" << "系统设置" << "地址设置";
    addFunctionGroup("管理中心", managementFunctions, operationTypes);
    
    // 展开所有节点
    ui->permissionTreeView->expandAll();
    
    // 调整列宽
    ui->permissionTreeView->setColumnWidth(0, 180);
    ui->permissionTreeView->setColumnWidth(1, 250);
    ui->permissionTreeView->setColumnWidth(2, 200);
    
    // 显示列标题，因为现在不再在上方显示单独的标签
    ui->permissionTreeView->header()->setVisible(true);
    
    // 设置列标题样式
    ui->permissionTreeView->header()->setStyleSheet(
        "QHeaderView::section {"
        "   background-color: #f0f2f5;"
        "   color: #333333;"
        "   font-weight: bold;"
        "   font-size: 14px;"
        "   padding: 8px 5px;"
        "   border: none;"
        "   border-bottom: 1px solid #e8e8e8;"
        "}"
        "QHeaderView::section:first {"
        "   padding-left: 10px;"
        "}"
    );

    // 为特定功能项添加数据范围下拉框
    addDataRangeComboBoxes();
}

void PermissionManagement::addDataRangeComboBoxes()
{
    // 更新后只为用户日志添加数据范围下拉框
    QStringList itemsWithDataRange;
    itemsWithDataRange << "用户日志";

    // 查找含有这些功能项的组
    QModelIndex hostIndex = findItemIndex("主机总览");
    QModelIndex managementIndex = findItemIndex("管理中心");

    QList<QPair<QModelIndex, QStandardItem*>> groups;
    if (hostIndex.isValid()) {
        groups.append(qMakePair(hostIndex, permissionModel->itemFromIndex(hostIndex)));
    }
    if (managementIndex.isValid()) {
        groups.append(qMakePair(managementIndex, permissionModel->itemFromIndex(managementIndex)));
    }

    // 遍历所有组和功能项
    for (const auto &groupPair : groups) {
        QModelIndex groupIndex = groupPair.first;
        QStandardItem *groupItem = groupPair.second;
        
        for (int i = 0; i < groupItem->rowCount(); ++i) {
            QStandardItem *functionItem = groupItem->child(i, 0);
            
            // 检查该功能项是否需要数据范围下拉框
            if (functionItem && itemsWithDataRange.contains(functionItem->text())) {
                QModelIndex dataRangeIndex = permissionModel->index(i, 2, groupIndex);
                
                // 创建容器控件和布局，以便对齐下拉框
                QWidget *containerWidget = new QWidget();
                QHBoxLayout *containerLayout = new QHBoxLayout(containerWidget);
                containerLayout->setContentsMargins(0, 2, 5, 2);
                containerLayout->setAlignment(Qt::AlignLeft);
                
                // 创建数据范围下拉框
                QComboBox *dataRangeComboBox = new QComboBox();
                dataRangeComboBox->addItem("全部数据");
                dataRangeComboBox->addItem("部分数据");
                dataRangeComboBox->addItem("个人数据");
                dataRangeComboBox->setStyleSheet(
                    "QComboBox {"
                    "   border: 1px solid #d9d9d9;"
                    "   border-radius: 4px;"
                    "   padding: 2px 8px;"
                    "   min-height: 25px;"
                    "   min-width: 150px;"
                    "   background-color: white;"
                    "}"
                    "QComboBox::drop-down {"
                    "   subcontrol-origin: padding;"
                    "   subcontrol-position: right center;"
                    "   width: 20px;"
                    "   border-left: none;"
                    "}"
                    "QComboBox QAbstractItemView {"
                    "   border: 1px solid #d9d9d9;"
                    "   selection-background-color: #1890ff;"
                    "   selection-color: white;"
                    "   background-color: white;"
                    "   padding: 2px;"
                    "   min-width: 150px;"
                    "}"
                );
                
                // 设置下拉框的对象名称，便于后续查找
                dataRangeComboBox->setObjectName(functionItem->text() + "_dataRange");
                
                // 添加下拉框到容器
                containerLayout->addWidget(dataRangeComboBox);
                containerLayout->addStretch();
                
                // 设置容器到数据范围列
                ui->permissionTreeView->setIndexWidget(dataRangeIndex, containerWidget);
                
                // 连接信号槽
                connect(dataRangeComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), 
                        this, &PermissionManagement::onDataRangeChanged);
                
                // 连接功能项选中状态变化的监听
                // 不使用错误的dataChanged信号，而是通过model的itemChanged信号来监听
                // 保存functionItem的位置，以便在model信号中识别它
                QModelIndex functionIndex = permissionModel->indexFromItem(functionItem);
                connect(permissionModel, &QStandardItemModel::itemChanged, 
                        [this, containerWidget, functionIndex](QStandardItem *changedItem) {
                    // 检查变化的项是否是我们要监视的功能项
                    if (permissionModel->indexFromItem(changedItem) == functionIndex) {
                        // 当功能项被选中时显示下拉框，否则隐藏
                        containerWidget->setVisible(changedItem->checkState() == Qt::Checked);
                    }
                });
                
                // 初始根据功能项选中状态设置下拉框可见性
                containerWidget->setVisible(functionItem->checkState() == Qt::Checked);
            }
        }
    }
}

QModelIndex PermissionManagement::findItemIndex(const QString &text)
{
    for (int i = 0; i < permissionModel->rowCount(); ++i) {
        QModelIndex index = permissionModel->index(i, 0);
        if (permissionModel->data(index).toString() == text) {
            return index;
        }
    }
    return QModelIndex();
}

void PermissionManagement::addFunctionGroup(const QString &groupName, const QStringList &functions, const QStringList &operations)
{
    // 创建组节点
    QStandardItem *groupItem = new QStandardItem(groupName);
    groupItem->setCheckable(true);
    groupItem->setEditable(false);
    
    // 添加空项以保持列对齐
    QStandardItem *operationPlaceholder = new QStandardItem("");
    operationPlaceholder->setEditable(false);
    
    QStandardItem *dataPlaceholder = new QStandardItem("");
    dataPlaceholder->setEditable(false);
    
    // 添加组节点
    QList<QStandardItem*> groupRow;
    groupRow << groupItem << operationPlaceholder << dataPlaceholder;
    permissionModel->appendRow(groupRow);
    
    // 为每个功能添加子节点
    for (const QString &function : functions) {
        QStandardItem *functionItem = new QStandardItem(function);
        functionItem->setCheckable(true);
        functionItem->setEditable(false);
        
        // 创建操作权限布局
        QStandardItem *operationItem = new QStandardItem();
        operationItem->setEditable(false);
        
        // 创建数据范围项
        QStandardItem *dataItem = new QStandardItem("");
        dataItem->setEditable(false);
        
        // 添加功能节点
        QList<QStandardItem*> functionRow;
        functionRow << functionItem << operationItem << dataItem;
        groupItem->appendRow(functionRow);
        
        // 为操作权限列添加操作类型复选框
        QWidget *operationWidget = new QWidget();
        QHBoxLayout *operationLayout = new QHBoxLayout(operationWidget);
        operationLayout->setContentsMargins(5, 2, 5, 2);
        operationLayout->setSpacing(5);
        operationLayout->setAlignment(Qt::AlignLeft);

        // 添加操作权限复选框
        for (const QString &operation : operations) {
            QCheckBox *checkbox = new QCheckBox(operation);
            checkbox->setStyleSheet(
                "QCheckBox {"
                "   spacing: 5px;"
                "}"
                "QCheckBox::indicator {"
                "   width: 16px;"
                "   height: 16px;"
                "}"
                "QCheckBox::indicator:unchecked {"
                "   border: 1px solid #d9d9d9;"
                "   background-color: white;"
                "   border-radius: 3px;"
                "}"
                "QCheckBox::indicator:checked {"
                "   border: 1px solid #1890ff;"
                "   background-color: #1890ff;"
                "   border-radius: 3px;"
                "}"
            );
            operationLayout->addWidget(checkbox);
            
            // 连接信号，使得勾选操作时也勾选功能
            connect(checkbox, &QCheckBox::toggled, [this, functionItem](bool checked) {
                if (checked && functionItem->checkState() != Qt::Checked) {
                    functionItem->setCheckState(Qt::Checked);
                }
            });
        }
        
        operationLayout->addStretch();
        
        // 获取操作列的索引
        QModelIndex operationIndex = permissionModel->indexFromItem(operationItem);
        
        // 设置操作列的控件
        ui->permissionTreeView->setIndexWidget(operationIndex, operationWidget);
    }
}

QString PermissionManagement::getCurrentRole() const
{
    QListWidgetItem *currentItem = ui->roleListWidget->currentItem();
    return currentItem ? currentItem->text() : QString();
}

QComboBox* PermissionManagement::getDataRangeComboBox(const QString &functionName)
{
    // 如果没有指定功能名称，默认返回用户日志的数据范围下拉框
    QString targetName = functionName.isEmpty() ? "用户日志" : functionName;
    
    // 通过遍历数据范围元素查找指定功能的下拉框
    QList<QComboBox*> comboBoxes = ui->permissionTreeView->findChildren<QComboBox*>(targetName + "_dataRange");
    if (!comboBoxes.isEmpty()) {
        return comboBoxes.first();
    }
    
    return nullptr;
}

void PermissionManagement::loadRolePermissionData(const QString &role)
{
    // 更新角色标签
    ui->roleLabel->setText(role);
    
    // 需要设置数据范围的功能项列表，现在只包含用户日志
    QStringList itemsWithDataRange;
    itemsWithDataRange << "用户日志";
    
    // 根据角色设置不同的权限
    if (role == "管理员") {
        // 管理员拥有所有权限
        for (int i = 0; i < permissionModel->rowCount(); ++i) {
            QStandardItem *groupItem = permissionModel->item(i, 0);
            groupItem->setCheckState(Qt::Checked);
            
            for (int j = 0; j < groupItem->rowCount(); ++j) {
                QStandardItem *functionItem = groupItem->child(j, 0);
                functionItem->setCheckState(Qt::Checked);
                
                // 获取操作列的控件并选中所有复选框
                QModelIndex operationIndex = permissionModel->index(j, 1, permissionModel->indexFromItem(groupItem));
                QWidget *operationWidget = ui->permissionTreeView->indexWidget(operationIndex);
                
                if (operationWidget) {
                    QList<QCheckBox*> checkboxes = operationWidget->findChildren<QCheckBox*>();
                    for (QCheckBox *checkbox : checkboxes) {
                        checkbox->setChecked(true);
                    }
                }
            }
        }
        
        // 为所有需要数据范围的功能项设置为全部数据
        for (const QString &item : itemsWithDataRange) {
            QComboBox *dataRangeComboBox = getDataRangeComboBox(item);
            if (dataRangeComboBox) {
                dataRangeComboBox->setCurrentIndex(0); // 全部数据
            }
        }
    } else if (role == "普通用户") {
        // 普通用户只有部分权限
        for (int i = 0; i < permissionModel->rowCount(); ++i) {
            QStandardItem *groupItem = permissionModel->item(i, 0);
            
            if (groupItem->text() == "主机总览") {
                groupItem->setCheckState(Qt::Checked);
                
                for (int j = 0; j < groupItem->rowCount(); ++j) {
                    QStandardItem *functionItem = groupItem->child(j, 0);
                    functionItem->setCheckState(Qt::Checked);
                    
                    // 获取操作列的控件并选中部分复选框
                    QModelIndex operationIndex = permissionModel->index(j, 1, permissionModel->indexFromItem(groupItem));
                    QWidget *operationWidget = ui->permissionTreeView->indexWidget(operationIndex);
                    
                    if (operationWidget) {
                        QList<QCheckBox*> checkboxes = operationWidget->findChildren<QCheckBox*>();
                        for (int k = 0; k < checkboxes.size(); ++k) {
                            // 普通用户只有模板生成和模板编辑权限
                            checkboxes[k]->setChecked(k < 2);
                        }
                    }
                }
            } else {
                groupItem->setCheckState(Qt::Unchecked);
                
                for (int j = 0; j < groupItem->rowCount(); ++j) {
                    QStandardItem *functionItem = groupItem->child(j, 0);
                    functionItem->setCheckState(Qt::Unchecked);
                    
                    // 获取操作列的控件并取消选中所有复选框
                    QModelIndex operationIndex = permissionModel->index(j, 1, permissionModel->indexFromItem(groupItem));
                    QWidget *operationWidget = ui->permissionTreeView->indexWidget(operationIndex);
                    
                    if (operationWidget) {
                        QList<QCheckBox*> checkboxes = operationWidget->findChildren<QCheckBox*>();
                        for (QCheckBox *checkbox : checkboxes) {
                            checkbox->setChecked(false);
                        }
                    }
                }
            }
        }
        
        // 为所有需要数据范围的功能项设置为部分数据
        for (const QString &item : itemsWithDataRange) {
            QComboBox *dataRangeComboBox = getDataRangeComboBox(item);
            if (dataRangeComboBox) {
                dataRangeComboBox->setCurrentIndex(1); // 部分数据
            }
        }
    } else if (role == "访客") {
        // 访客只有查看权限
        for (int i = 0; i < permissionModel->rowCount(); ++i) {
            QStandardItem *groupItem = permissionModel->item(i, 0);
            
            if (groupItem->text() == "主机总览") {
                groupItem->setCheckState(Qt::PartiallyChecked);
                
                for (int j = 0; j < groupItem->rowCount(); ++j) {
                    QStandardItem *functionItem = groupItem->child(j, 0);
                    
                    // 访客只能查看数值监控、图像监控和时序图
                    if (j < 3) {
                        functionItem->setCheckState(Qt::Checked);
                        
                        // 获取操作列的控件并只选中模板生成复选框
                        QModelIndex operationIndex = permissionModel->index(j, 1, permissionModel->indexFromItem(groupItem));
                        QWidget *operationWidget = ui->permissionTreeView->indexWidget(operationIndex);
                        
                        if (operationWidget) {
                            QList<QCheckBox*> checkboxes = operationWidget->findChildren<QCheckBox*>();
                            for (int k = 0; k < checkboxes.size(); ++k) {
                                // 访客只有模板生成权限
                                checkboxes[k]->setChecked(k == 0);
                            }
                        }
                    } else {
                        functionItem->setCheckState(Qt::Unchecked);
                        
                        // 获取操作列的控件并取消选中所有复选框
                        QModelIndex operationIndex = permissionModel->index(j, 1, permissionModel->indexFromItem(groupItem));
                        QWidget *operationWidget = ui->permissionTreeView->indexWidget(operationIndex);
                        
                        if (operationWidget) {
                            QList<QCheckBox*> checkboxes = operationWidget->findChildren<QCheckBox*>();
                            for (QCheckBox *checkbox : checkboxes) {
                                checkbox->setChecked(false);
                            }
                        }
                    }
                }
            } else {
                groupItem->setCheckState(Qt::Unchecked);
                
                for (int j = 0; j < groupItem->rowCount(); ++j) {
                    QStandardItem *functionItem = groupItem->child(j, 0);
                    functionItem->setCheckState(Qt::Unchecked);
                    
                    // 获取操作列的控件并取消选中所有复选框
                    QModelIndex operationIndex = permissionModel->index(j, 1, permissionModel->indexFromItem(groupItem));
                    QWidget *operationWidget = ui->permissionTreeView->indexWidget(operationIndex);
                    
                    if (operationWidget) {
                        QList<QCheckBox*> checkboxes = operationWidget->findChildren<QCheckBox*>();
                        for (QCheckBox *checkbox : checkboxes) {
                            checkbox->setChecked(false);
                        }
                    }
                }
            }
        }
        
        // 为所有需要数据范围的功能项设置为个人数据
        for (const QString &item : itemsWithDataRange) {
            QComboBox *dataRangeComboBox = getDataRangeComboBox(item);
            if (dataRangeComboBox) {
                dataRangeComboBox->setCurrentIndex(2); // 个人数据
            }
        }
    } else {
        // 自定义角色默认没有权限，需要手动设置
        for (int i = 0; i < permissionModel->rowCount(); ++i) {
            QStandardItem *groupItem = permissionModel->item(i, 0);
            groupItem->setCheckState(Qt::Unchecked);
            
            for (int j = 0; j < groupItem->rowCount(); ++j) {
                QStandardItem *functionItem = groupItem->child(j, 0);
                functionItem->setCheckState(Qt::Unchecked);
                
                // 获取操作列的控件并取消选中所有复选框
                QModelIndex operationIndex = permissionModel->index(j, 1, permissionModel->indexFromItem(groupItem));
                QWidget *operationWidget = ui->permissionTreeView->indexWidget(operationIndex);
                
                if (operationWidget) {
                    QList<QCheckBox*> checkboxes = operationWidget->findChildren<QCheckBox*>();
                    for (QCheckBox *checkbox : checkboxes) {
                        checkbox->setChecked(false);
                    }
                }
            }
        }
        
        // 为所有需要数据范围的功能项设置为个人数据
        for (const QString &item : itemsWithDataRange) {
            QComboBox *dataRangeComboBox = getDataRangeComboBox(item);
            if (dataRangeComboBox) {
                dataRangeComboBox->setCurrentIndex(2); // 个人数据
            }
        }
    }
}

void PermissionManagement::onRoleListItemClicked(QListWidgetItem *item)
{
    if (item) {
        // 加载选中角色的权限数据
        loadRolePermissionData(item->text());
    }
}

void PermissionManagement::onFunctionItemChanged(QStandardItem *item)
{
    // 检查是否为功能项
    if (!item->parent()) {
        // 是分组项，更新所有子项
        Qt::CheckState state = item->checkState();
        for (int i = 0; i < item->rowCount(); ++i) {
            QStandardItem *childItem = item->child(i, 0);
            childItem->setCheckState(state);
            
            // 获取操作列的控件并同步复选框状态
            QModelIndex operationIndex = permissionModel->index(i, 1, permissionModel->indexFromItem(item));
            QWidget *operationWidget = ui->permissionTreeView->indexWidget(operationIndex);
            
            if (operationWidget) {
                QList<QCheckBox*> checkboxes = operationWidget->findChildren<QCheckBox*>();
                for (QCheckBox *checkbox : checkboxes) {
                    checkbox->setChecked(state == Qt::Checked);
                }
            }
        }
    } else {
        // 是功能项，检查其状态并更新组状态
        QStandardItem *parentItem = item->parent();
        bool allChecked = true;
        bool allUnchecked = true;
        
        for (int i = 0; i < parentItem->rowCount(); ++i) {
            QStandardItem *siblingItem = parentItem->child(i, 0);
            if (siblingItem->checkState() != Qt::Checked) {
                allChecked = false;
            }
            if (siblingItem->checkState() != Qt::Unchecked) {
                allUnchecked = false;
            }
        }
        
        if (allChecked) {
            parentItem->setCheckState(Qt::Checked);
        } else if (allUnchecked) {
            parentItem->setCheckState(Qt::Unchecked);
        } else {
            parentItem->setCheckState(Qt::PartiallyChecked);
        }
        
        // 如果取消选中功能，同时取消选中所有操作
        if (item->checkState() == Qt::Unchecked) {
            int row = item->row();
            QModelIndex operationIndex = permissionModel->index(row, 1, permissionModel->indexFromItem(parentItem));
            QWidget *operationWidget = ui->permissionTreeView->indexWidget(operationIndex);
            
            if (operationWidget) {
                QList<QCheckBox*> checkboxes = operationWidget->findChildren<QCheckBox*>();
                for (QCheckBox *checkbox : checkboxes) {
                    checkbox->setChecked(false);
                }
            }
        }
    }
}

void PermissionManagement::onSaveButtonClicked()
{
    // 保存权限配置
    QString currentRole = getCurrentRole();
    if (!currentRole.isEmpty()) {
        // 仅获取用户日志的数据范围设置
        QStringList dataRangeSettings;
        QComboBox *dataRangeComboBox = getDataRangeComboBox("用户日志");
        if (dataRangeComboBox && dataRangeComboBox->isVisible()) {
            dataRangeSettings << QString("用户日志: %1").arg(dataRangeComboBox->currentText());
        }
        
        // 构建信息字符串
        QString message = QString("角色 %1 的权限配置已保存").arg(currentRole);
        
        // 如果有数据范围设置，则添加到消息中
        if (!dataRangeSettings.isEmpty()) {
            message += QString("\n数据范围设置:\n%1").arg(dataRangeSettings.join("\n"));
        }
        
        QMessageBox::information(this, "保存成功", message);
    }
}

void PermissionManagement::onDataRangeChanged(int index)
{
    QComboBox *comboBox = qobject_cast<QComboBox*>(sender());
    if (comboBox) {
        QString dataRange = comboBox->currentText();
        QString currentRole = getCurrentRole();
        
        // 在实际应用中，您可能希望将这里的信息记录到日志或数据库中
        qDebug() << "角色" << currentRole << "的数据范围更改为:" << dataRange;
    }
}

void PermissionManagement::onAddRoleButtonClicked()
{
    bool ok;
    QString roleName = QInputDialog::getText(this, "新增角色",
                                            "请输入角色名称：", QLineEdit::Normal,
                                            "", &ok);
    if (ok && !roleName.isEmpty()) {
        // 检查角色名是否已存在
        if (roleList.contains(roleName)) {
            QMessageBox::warning(this, "错误", "角色名 '" + roleName + "' 已存在！");
            return;
        }
        
        // 添加新角色到列表
        roleList.append(roleName);
        
        // 创建新的列表项
        QListWidgetItem *newItem = new QListWidgetItem(roleName);
        newItem->setTextAlignment(Qt::AlignCenter);
        ui->roleListWidget->addItem(newItem);
        
        // 选中新添加的角色
        ui->roleListWidget->setCurrentItem(newItem);
        
        // 加载新角色的权限数据（默认无权限）
        loadRolePermissionData(roleName);
        
        QMessageBox::information(this, "新增角色", "角色 '" + roleName + "' 已成功添加！请设置其权限。");
    }
}