﻿#ifndef PARSEANDANALYSIS_PARSEANDANALYSIS_H
#define PARSEANDANALYSIS_PARSEANDANALYSIS_H

#include <string>
#include <list>
#include "icdStructDef.h"

#if defined(_WIN32)
#include <QtCore/qglobal.h>
#  if defined(MY_LIBRARY_EXPORTS)
/* We are building this library */
#    define MY_LIBRARY_API __declspec(dllexport)
#  else
/* We are using this library */
// #    define MY_LIBRARY_API __declspec(dllimport)
/* windows使用qt的导出标识 */
#    define MY_LIBRARY_API Q_DECL_EXPORT
#  endif
#else
#  define MY_LIBRARY_API
#endif

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 测试接口
 * @param a
 * @param b 
 * @return 返回a+b
 */
MY_LIBRARY_API int add(int a, int b);

/**
 * 加载icd文件
 * @param icdFile
 * @return
 */
MY_LIBRARY_API int loadIcd(const std::string &icdFile);

/**
 * @brief 初始化decoder对象指针, 调用前必须先调用loadIcdJson接口
 * @param token decoder对象指针
 * @return
 */
MY_LIBRARY_API int initDecoder(void *&token);

/**
 * @brief 销毁decoder对象指针
 * @param token decoder对象指针
 * @return
 */
MY_LIBRARY_API int destroyDecoder(void *&token);

/**
 *
 * @return 返回当前icd版本
 */
MY_LIBRARY_API std::string getCurrentIcdVersion(void *&token);


/**
 * @brief
 * @param buffer
 * @param dataList
 */
MY_LIBRARY_API void msgpackPackVector(std::stringstream &buffer, std::vector<std::string> dataList);

/**
 * @brief 获取功能域列表
 * @param token [in]  decoder对象指针
 * @param funcUnitVec [out] 功能域列表
 * @return 0-成功
 */
MY_LIBRARY_API int getFuncDomainList(void *&token, std::vector<FuncDomainInfo> &funcDomainVec);

/**
 * 根据功能单元查询与之相关的消息
 * @param token [in] decoder对象指针
 * @param shortName [in] 功能单元缩略名
 * @param messageVec [out] 消息缩略名列表
 * @return
 */
MY_LIBRARY_API int getMessageListByFuncUnitShortName(void *&token, const std::string &shortName, std::vector<std::string> &messageVec);

/**
 * 根据消息标识符获取消息详情，包含主题结构
 * @param token [in] decoder对象指针
 * @param shortName [in] 消息名称
 * @param message [out] 消息详情
 * @return
 */
MY_LIBRARY_API int getMessageInfoByMessageName(void *&token, const std::string &msgName, Message &message);

/**
 * 根据消息标识符获取消息详情，不包含主题结构
 * @param token [in] decoder对象指针
 * @param shortName [in] 消息缩略名
 * @param funcUnitMessage [out] 消息详情
 * @return
 */
MY_LIBRARY_API int getFuncUintMessageByMessageName(void *&token, const std::string &msgName, FuncUnitMessage &funcUnitMessage);

MY_LIBRARY_API int topicShortNameToTopicValue(void *&token, const std::string &topicShortName, std::string &topicValue);

/**
 * 解析数据帧头
 * @param token
 * @param dataBuffer
 * @param dataHeader
 * @return
 */
MY_LIBRARY_API int parseDataHeader(void *&token, DataBuffer &dataBuffer, DataHeader &dataHeader);

/**
 * 解析数据
 * @param token
 * @param dataBuffer
 * @param parsedData
 * @return
 */
MY_LIBRARY_API int parseData(void *&token, DataBuffer &dataBuffer, ParsedData &parsedData);

#ifdef __cplusplus
}
#endif
#endif //PARSEANDANALYSIS_PARSEANDANALYSIS_H
