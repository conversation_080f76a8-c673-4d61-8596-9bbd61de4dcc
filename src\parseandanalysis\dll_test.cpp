#include <QCoreApplication>
#include <QDebug>
#include <QString>
#include <QDir>
#include "dll_loader.h"

int main(int argc, char *argv[])
{
    QCoreApplication a(argc, argv);
    
    // 获取当前目录
    QString currentDir = QDir::currentPath();
    qDebug() << "Current directory:" << currentDir;
    
    // 设置DLL路径 - 可以通过命令行参数提供，或使用默认路径
    QString dllPath;
    if (argc > 1) {
        dllPath = argv[1];
    } else {
        // 默认路径，用户可以根据实际情况修改
        dllPath = "D:/project/611/0264/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/debug/libparseAndAnalysis.dll";
    }
    
    qDebug() << "Attempting to load DLL from:" << dllPath;
    
    // 创建DLL加载器
    DllLoader loader;
    
    // 初始化DLL
    bool success = loader.init(dllPath);
    if (success) {
        qDebug() << "DLL loaded successfully";
        
        // 尝试使用add函数
        try {
            int result = loader.add(5, 3);
            qDebug() << "5 + 3 =" << result;
        } catch (const std::runtime_error& e) {
            qDebug() << "add function not available:" << e.what();
        }
        
        // 释放DLL
        loader.release();
    } else {
        qDebug() << "Failed to load DLL";
    }
    
    return 0;
} 